# LBVD POS - Trạng Thái Dự Án In Bluetooth

## 1. Tổng Quan

Dự án này là một ứng dụng POS (Point of Sale) được xây dựng bằng Flutter. Trọng tâm phát triển hiện tại là tích hợp chức năng in hóa đơn qua máy in nhiệt Bluetooth, sử dụng phương pháp giao tiếp native qua Platform Channel để đảm bảo hiệu suất và độ tin cậy.

## 2. Hiện Trạng Chức Năng (Tính đến 23/08/2025)

**Chức năng cốt lõi "Cài đặt máy in" đã được hoàn thiện và hoạt động ổn định.**

### 2.1. <PERSON>ến Trúc

*   **Flutter (Dart):** Cung cấp giao diện người dùng và logic nghiệp vụ.
*   **Platform Channel:** Sử dụng `MethodChannel` với tên `com.house.tgcorp.com.vn/spp_printer` để giao tiếp giữa Dart và Kotlin.
*   **Native (Kotlin):** Xử lý toàn bộ logic giao tiếp Bluetooth cấp thấp, giúp tránh các vấn đề về tương thích và hiệu năng của các thư viện Flutter bên thứ ba.

### 2.2. Luồng Hoạt Động Chi Tiết

1.  **`PrinterSettingsPage.dart` (UI):**
    *   Hiển thị trạng thái kết nối (Đã kết nối / Chưa kết nối).
    *   Cung cấp nút "Tìm máy in" để liệt kê các máy in đã được ghép nối (paired) trong cài đặt Bluetooth của Android.
    *   Cho phép người dùng chọn và "Kết nối" với một máy in.
    *   Cung cấp các nút "Test TSPL" và "Test CPCL" để kiểm tra việc in ấn.
    *   Cho phép "Ngắt kết nối".

2.  **`NativePrinterService.dart` (Service Layer):**
    *   Là một lớp trung gian (wrapper) cho `MethodChannel`, cung cấp các hàm Dart thân thiện để gọi sang code native, bao gồm: `getBondedDevices`, `connect`, `disconnect`, `testPrintTSPL`, `testPrintCPCL`, và `isConnected`.

3.  **`MainActivity.kt` & `SppPrinter.kt` (Native Layer):**
    *   `SppPrinter.kt`: Chứa toàn bộ logic để xử lý Bluetooth SPP (Serial Port Profile), bao gồm tìm kiếm, kết nối, ngắt kết nối, gửi dữ liệu in, và kiểm tra trạng thái kết nối hiện tại.
    *   `MainActivity.kt`: Đóng vai trò là cầu nối, nhận các lệnh gọi từ `MethodChannel` của Flutter và ủy quyền cho `SppPrinter` thực thi.

### 2.3. Logic Ghi Nhớ & Tự Động Kết Nối

*   **Lưu trữ:** Khi kết nối thành công, tên (`printer_name`) và địa chỉ MAC (`printer_address`) của máy in được lưu vào `SharedPreferences`.
*   **Tự động kết nối:** Khi người dùng mở lại trang `PrinterSettingsPage`:
    1.  Ứng dụng đọc thông tin máy in đã lưu.
    2.  Nó gọi hàm `isConnected()` bên phía native để kiểm tra xem kết nối có còn tồn tại ở tầng hệ điều hành hay không.
    3.  **Nếu còn kết nối:** Giao diện được cập nhật ngay lập tức sang trạng thái "Đã kết nối" mà không cần thực hiện kết nối lại.
    4.  **Nếu không còn kết nối:** Ứng dụng sẽ tự động thực hiện quy trình kết nối lại đến máy in đã lưu.
*   **Ngắt kết nối:** Thao tác ngắt kết nối chỉ đóng socket Bluetooth và không xóa thông tin máy in đã lưu, đảm bảo lần truy cập sau vẫn có thể tự động kết nối lại.

## 3. Hướng Dẫn Test

### 3.1. Yêu Cầu

*   Một thiết bị Android thật.
*   Một máy in nhiệt Bluetooth hỗ trợ lệnh TSPL hoặc CPCL.
*   Máy in **phải được ghép nối (paired)** với thiết bị Android trong phần cài đặt Bluetooth của hệ điều hành trước khi test.

### 3.2. Các Bước Kiểm Tra

1.  Chạy ứng dụng bằng lệnh `flutter run`.
2.  Điều hướng đến trang "Cài đặt máy in".
3.  **Kiểm tra tìm kiếm:** Nhấn "Tìm máy in". Danh sách các máy in đã ghép nối phải được hiển thị.
4.  **Kiểm tra kết nối:** Chọn một máy in và nhấn "Kết nối". Trạng thái phải chuyển thành "Đã kết nối: [Tên máy in]".
5.  **Kiểm tra in:** Nhấn "Test TSPL" và "Test CPCL". Máy in phải in ra các nhãn test tương ứng.
6.  **Kiểm tra duy trì trạng thái:**
    *   Thoát khỏi trang cài đặt và quay lại.
    *   Trạng thái phải ngay lập tức hiển thị là "Đã kết nối" mà không cần bất kỳ thao tác nào.
7.  **Kiểm tra tự động kết nối lại:**
    *   Nhấn "Ngắt kết nối".
    *   Thoát khỏi trang cài đặt và quay lại.
    *   Ứng dụng phải tự động thực hiện quá trình kết nối lại với máy in đó.

## 4. Các Bước Tiếp Theo

*   Tích hợp `NativePrinterService` vào các màn hình bán hàng, thu ngân để thực hiện in hóa đơn thực tế.
*   Mở rộng chức năng để hỗ trợ in hình ảnh (yêu cầu chuyển đổi bitmap sang command set của máy in).
*   Xây dựng giải pháp tương tự cho nền tảng iOS nếu cần.

