#!/bin/bash

# Script test API tạo đơn hàng vé đơn giản
# Chạy: chmod +x test_api_simple.sh && ./test_api_simple.sh

echo "🎫 Testing Ticket Order API..."

# Test 1: Tạo đơn hàng vé với dữ liệu hợp lệ
echo "📝 Test 1: Valid ticket order data"
curl -X POST http://localhost:4000/user/api/create-ticket-order \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "products": [
      {
        "id": "64f1234567890abcdef12345",
        "name": "Vé Tour Hạ Long 2N1Đ",
        "price": 1500000,
        "quantity": 2,
        "thumbnail": "/images/tour-ha-long.jpg",
        "note": "Phòng đôi"
      }
    ],
    "customerName": "Nguyễn Văn Test",
    "phone": "**********",
    "email": "<EMAIL>",
    "totalAmount": 3000000,
    "paymentMethod": 0,
    "bankCode": "TM",
    "ticketType": "tour",
    "eventLocation": "Vịnh Hạ Long"
  }' \
  --output json

echo -e "\n\n"

# Test 2: Test với dữ liệu thiếu
echo "📝 Test 2: Missing required fields"
curl -X POST http://localhost:4000/user/api/create-ticket-order \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "products": [],
    "customerName": "",
    "phone": "",
    "totalAmount": 0
  }' \
  --output json

echo -e "\n\n"

# Test 3: Test endpoint tồn tại
echo "📝 Test 3: Check if endpoint exists"
curl -X OPTIONS http://localhost:4000/user/api/create-ticket-order \
  -H "Accept: application/json" \
  -v

echo -e "\n\n✅ Tests completed!"
echo "📋 Kiểm tra:"
echo "1. API có trả về response không?"
echo "2. Validation có hoạt động không?"
echo "3. Endpoint có tồn tại không?"
