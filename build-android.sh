#!/bin/bash

echo "Building Flutter app bundle..."
flutter build appbundle --release

# Updated path for Flutter build output
FILE="build/app/outputs/bundle/release/app-release.aab"

echo "Uploading to DeployGate..."
curl -F "token=ceae3a6ca79098d4f308fe249d4c6fa4f07a17bd" \
            -F "file=@$FILE" \
            -F "message=New build from GitLab CI/CD" \
            https://deploygate.com/api/users/thanhdevapp/apps

echo "Upload completed!"
