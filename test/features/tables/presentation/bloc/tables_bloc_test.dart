import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dartz/dartz.dart';

import 'package:myhome/features/tables/presentation/bloc/tables_bloc.dart';
import 'package:myhome/features/tables/domain/usecases/get_all_tables_usecase.dart';
import 'package:myhome/features/tables/domain/usecases/get_store_table_stats_usecase.dart';
import 'package:myhome/features/tables/domain/entities/table_entity.dart';
import 'package:myhome/core/errors/failures.dart';

import 'tables_bloc_test.mocks.dart';

@GenerateMocks([
  GetAllTablesUseCase,
  GetStoreTableStatsUseCase,
])
void main() {
  late TablesBloc tablesBloc;
  late MockGetAllTablesUseCase mockGetAllTablesUseCase;
  late MockGetStoreTableStatsUseCase mockGetStoreTableStatsUseCase;

  setUp(() {
    mockGetAllTablesUseCase = MockGetAllTablesUseCase();
    mockGetStoreTableStatsUseCase = MockGetStoreTableStatsUseCase();
    tablesBloc = TablesBloc(
      getAllTablesUseCase: mockGetAllTablesUseCase,
      getStoreTableStatsUseCase: mockGetStoreTableStatsUseCase,
    );
  });

  tearDown(() {
    tablesBloc.close();
  });

  group('TablesBloc', () {
    const tStoreId = '623a97cdbe781e0ba814f227';
    const tAllTablesEntity = AllTablesEntity(
      tables: [],
      stats: TableStatsEntity(),
      storeId: tStoreId,
    );

    test('initial state should be TablesInitial', () {
      expect(tablesBloc.state, equals(const TablesInitial()));
    });

    group('LoadAllTablesEvent', () {
      blocTest<TablesBloc, TablesState>(
        'should emit [TablesLoading, TablesLoaded] when data is gotten successfully',
        build: () {
          when(mockGetAllTablesUseCase(any))
              .thenAnswer((_) async => const Right(tAllTablesEntity));
          return tablesBloc;
        },
        act: (bloc) => bloc.add(const LoadAllTablesEvent(storeId: tStoreId)),
        expect: () => [
          const TablesLoading(),
          const TablesLoaded(allTables: tAllTablesEntity),
        ],
        verify: (_) {
          verify(mockGetAllTablesUseCase(
            const GetAllTablesParams(storeId: tStoreId),
          ));
        },
      );

      blocTest<TablesBloc, TablesState>(
        'should emit [TablesLoading, TablesError] when getting data fails',
        build: () {
          when(mockGetAllTablesUseCase(any))
              .thenAnswer((_) async => const Left(ServerFailure(message: 'Server Error')));
          return tablesBloc;
        },
        act: (bloc) => bloc.add(const LoadAllTablesEvent(storeId: tStoreId)),
        expect: () => [
          const TablesLoading(),
          const TablesError(message: 'Server Error'),
        ],
        verify: (_) {
          verify(mockGetAllTablesUseCase(
            const GetAllTablesParams(storeId: tStoreId),
          ));
        },
      );
    });

    group('RefreshTablesEvent', () {
      blocTest<TablesBloc, TablesState>(
        'should emit [TablesLoaded] with isRefreshing true then false',
        build: () {
          when(mockGetAllTablesUseCase(any))
              .thenAnswer((_) async => const Right(tAllTablesEntity));
          return tablesBloc;
        },
        seed: () => const TablesLoaded(allTables: tAllTablesEntity),
        act: (bloc) => bloc.add(const RefreshTablesEvent(storeId: tStoreId)),
        expect: () => [
          const TablesLoaded(allTables: tAllTablesEntity, isRefreshing: true),
          const TablesLoaded(allTables: tAllTablesEntity),
        ],
      );
    });
  });
}
