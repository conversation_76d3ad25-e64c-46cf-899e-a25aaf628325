// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in myhome/test/features/tables/presentation/bloc/tables_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:dartz/dartz.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:myhome/core/errors/failures.dart' as _i6;
import 'package:myhome/features/tables/domain/entities/table_entity.dart'
    as _i7;
import 'package:myhome/features/tables/domain/repositories/tables_repository.dart'
    as _i2;
import 'package:myhome/features/tables/domain/usecases/get_all_tables_usecase.dart'
    as _i4;
import 'package:myhome/features/tables/domain/usecases/get_store_table_stats_usecase.dart'
    as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTablesRepository_0 extends _i1.SmartFake
    implements _i2.TablesRepository {
  _FakeTablesRepository_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeEither_1<L, R> extends _i1.SmartFake implements _i3.Either<L, R> {
  _FakeEither_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [GetAllTablesUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetAllTablesUseCase extends _i1.Mock
    implements _i4.GetAllTablesUseCase {
  MockGetAllTablesUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.TablesRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeTablesRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.TablesRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i7.AllTablesEntity>> call(
          _i4.GetAllTablesParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue:
            _i5.Future<_i3.Either<_i6.Failure, _i7.AllTablesEntity>>.value(
                _FakeEither_1<_i6.Failure, _i7.AllTablesEntity>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i5.Future<_i3.Either<_i6.Failure, _i7.AllTablesEntity>>);
}

/// A class which mocks [GetStoreTableStatsUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetStoreTableStatsUseCase extends _i1.Mock
    implements _i8.GetStoreTableStatsUseCase {
  MockGetStoreTableStatsUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.TablesRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeTablesRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.TablesRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i7.TableStatsEntity>> call(
          _i8.GetStoreTableStatsParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue:
            _i5.Future<_i3.Either<_i6.Failure, _i7.TableStatsEntity>>.value(
                _FakeEither_1<_i6.Failure, _i7.TableStatsEntity>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i5.Future<_i3.Either<_i6.Failure, _i7.TableStatsEntity>>);
}
