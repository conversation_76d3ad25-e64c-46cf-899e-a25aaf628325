import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  await testInvoiceAPI();
}

Future<void> testInvoiceAPI() async {
  const String baseUrl = 'http://localhost:4000';
  const String storeId = '623a97cdbe781e0ba814f227';
  const String token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2ODRhYTg1ZGY5Y2IwNzUxNjg2ZWQwYTgiLCJ0eXBlIjoyLCJjb2RlIjoxODk3LCJiaXJ0aGRheSI6MTc0OTcyMzIyOTg5NSwibW9kaWZ5QXQiOjE3NTYxMDU0NjYwMDAsImNyZWF0ZUF0IjoxNzQ5NzIzMjI5MDAwLCJwb2ludCI6MjAsImNvbmZpcm1QaG9uZSI6MSwiY29uZmlybUVtYWlsIjowLCJ3YWxsZXQiOjAsImZ1bmRzIjowLCJmZWUiOjAsInNlcnZpY2VzR2FzIjoxLCJzZXJ2aWNlc1Nob3dyb29tIjoxLCJzZXJ2aWNlc1BhcmtpbmciOjEsInNlcnZpY2VzU3BhIjoxLCJzZXJ2aWNlc0hvdGVsIjoxLCJzZXJ2aWNlc0V4YW1pbmF0aW9uIjoxLCJzZXJ2aWNlc1N0b3JlIjoxLCJub3RpZmljYXRpb25zQmlsbFJldHVybiI6MCwiY291bnRNZXNzYWdlIjowLCJjb3VudE9ubGluZVNlc3Npb24iOjAsIm5vdGlmaWNhdGlvbnMiOjQsInN0YXR1c1N0b3JlIjoxLCJzdGF0dXMiOjEsInBob25lIjoiMDk4NzY1NDMyMSIsImFkZHJlc3NMaXN0IjpbeyJzdHJlZXQiOiJOZ8O1IDIxIEzDqiB2xINuIGx1b25nIiwicGhvbmUiOiIwOTY4ODY4ODYyIiwiZGVmYXVsdCI6dHJ1ZSwibmFtZSI6ImdvbGRtYXJrIiwicHJvdmluY2UiOiJUaMOgbmggcGjhu5EgSMOgIE7hu5lpIiwiZGlzdHJpY3QiOiJRdeG6rW4gSG_DoG4gS2nhur9tIiwid2FyZCI6IlBoxrDhu51uZyDEkOG7k25nIFh1w6JuIiwiYWRkcmVzcyI6Ik5nw7UgMjEgTMOqIHbEg24gbHVvbmcsIFBoxrDhu51uZyDEkOG7k25nIFh1w6JuLCBRdeG6rW4gSG_DoG4gS2nhur9tLCBUaMOgbmggcGjhu5EgSMOgIE7hu5lpIiwiX2lkIjoiNjg1MjM5ZWI5ZGFiZmNjZjg3MDc4OGVhIn1dLCJhZGRyZXNzIjoiIiwibG9jYXRpb24iOiIiLCJzdHJlZXQiOiIiLCJlbWFpbCI6IiIsInVzZXJOYW1lIjoiIiwiZnVsbE5hbWUiOiJOZ3V5ZW4gVmFuIEEiLCJfX3YiOjAsInBpY3R1cmUiOiIvdGVtcGxhdGUvZGVmYXVsdC1hdmF0YXIuanBnIiwiaWF0IjoxNzU2MDkzNzQ1LCJleHAiOjI1NDQ0OTM3NDV9.Re7omIWBlXhsM_UAml_lcTlEPJeu5Sd_dGn7AX719qU';

  print('🧪 Testing Invoice API...');
  
  try {
    final url = Uri.parse('$baseUrl/user/api/orders-with-tables')
        .replace(queryParameters: {
      'output': 'json',
      'storeId': storeId,
      'page': '1',
      'limit': '20',
    });

    print('📍 URL: $url');

    final response = await http.get(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'access-token': token,
      },
    );

    print('📊 Status Code: ${response.statusCode}');
    print('📋 Headers: ${response.headers}');
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      print('✅ Success: ${data['success']}');
      print('📝 Message: ${data['message']}');
      
      if (data['data'] != null) {
        final orders = data['data'] as List;
        print('📦 Orders count: ${orders.length}');
        
        if (orders.isNotEmpty) {
          print('🔍 First order structure:');
          final firstOrder = orders.first as Map<String, dynamic>;
          print('   Keys: ${firstOrder.keys.toList()}');
          
          // Print some key fields
          print('   _id: ${firstOrder['_id']}');
          print('   orderId: ${firstOrder['orderId']}');
          print('   code: ${firstOrder['code']}');
          print('   customerName: ${firstOrder['customerName']}');
          print('   customerPhone: ${firstOrder['customerPhone']}');
          print('   status: ${firstOrder['status']}');
          print('   totalAmount: ${firstOrder['totalAmount']}');
          print('   orderTime: ${firstOrder['orderTime']}');
          
          if (firstOrder['products'] != null) {
            final products = firstOrder['products'] as List;
            print('   Products count: ${products.length}');
            if (products.isNotEmpty) {
              print('   First product: ${products.first}');
            }
          }
          
          if (firstOrder['tableInfo'] != null) {
            print('   Table info: ${firstOrder['tableInfo']}');
          }
        }
      }
    } else {
      print('❌ Error: ${response.statusCode}');
      print('📄 Body: ${response.body}');
    }
  } catch (e) {
    print('💥 Exception: $e');
  }
}
