import 'dart:convert';

// Mock data để test InvoiceModel parsing
void main() {
  testInvoiceModelParsing();
}

void testInvoiceModelParsing() {
  print('🧪 Testing InvoiceModel parsing...');
  
  // Mock order data structure dựa trên API response
  final mockOrderData = {
    "_id": "67a1234567890abcdef12345",
    "orderId": "ORD-2025-001",
    "paymentId": "PAY-2025-001",
    "code": 2025001,
    "customerName": "Nguyen Van Test",
    "customerPhone": "**********",
    "customerEmail": "<EMAIL>",
    "status": 1, // Processing
    "totalAmount": 65000,
    "subtotal": 65000,
    "discountAmount": 0,
    "transportFee": 0,
    "orderTime": "2025-08-25T08:00:00.000Z",
    "completedTime": null,
    "notes": "Test order for invoice",
    "paymentMethod": "cash",
    "bankCode": null,
    "isPayOnline": false,
    "serviceType": "dine-in",
    "coupon": null,
    "storeId": "623a97cdbe781e0ba814f227",
    "staffId": "staff123",
    "staffName": "Nguyen Van Staff",
    "products": [
      {
        "_id": "prod1",
        "productId": "prod1",
        "name": "Cà phê đen",
        "quantity": 2,
        "price": 25000,
        "totalPrice": 50000,
        "unit": "ly",
        "notes": null
      },
      {
        "_id": "prod2",
        "productId": "prod2",
        "name": "Bánh mì",
        "quantity": 1,
        "price": 15000,
        "totalPrice": 15000,
        "unit": "cái",
        "notes": "Không hành"
      }
    ],
    "tableInfo": {
      "tableId": "623a97cdbe781e0ba814f229",
      "tableName": "Bàn 01",
      "areaId": "623a97cdbe781e0ba814f228",
      "areaName": "Tầng 1",
      "capacity": 4,
      "status": 1
    }
  };

  print('📋 Mock order data:');
  print('   _id: ${mockOrderData['_id']}');
  print('   orderId: ${mockOrderData['orderId']}');
  print('   customerName: ${mockOrderData['customerName']}');
  print('   status: ${mockOrderData['status']}');
  print('   totalAmount: ${mockOrderData['totalAmount']}');
  print('   products count: ${(mockOrderData['products'] as List).length}');
  print('   tableInfo: ${mockOrderData['tableInfo'] != null ? 'Yes' : 'No'}');

  // Test API response structure
  final mockApiResponse = {
    "error": false,
    "message": "Lấy danh sách đơn hàng với bàn ăn thành công",
    "data": {
      "orders": [mockOrderData],
      "total": 1,
      "page": 1,
      "limit": 20,
      "totalPages": 1
    }
  };

  print('\n📊 Mock API response structure:');
  print('   error: ${mockApiResponse['error']}');
  print('   message: ${mockApiResponse['message']}');
  final dataObj = mockApiResponse['data'] as Map<String, dynamic>;
  print('   data.orders count: ${(dataObj['orders'] as List).length}');
  print('   data.total: ${dataObj['total']}');
  print('   data.page: ${dataObj['page']}');

  // Test JSON serialization
  print('\n🔄 Testing JSON serialization...');
  final jsonString = jsonEncode(mockApiResponse);
  print('   JSON length: ${jsonString.length} characters');
  
  final parsedBack = jsonDecode(jsonString);
  print('   Parsed back successfully: ${parsedBack['error'] == false}');

  print('\n✅ InvoiceModel parsing test completed!');
  print('📝 Next steps:');
  print('   1. Update InvoiceModel.fromOrderJson() to handle this structure');
  print('   2. Test with real API data');
  print('   3. Implement proper error handling');
}
