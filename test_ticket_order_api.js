#!/usr/bin/env node

/**
 * Script test API tạo đơn hàng vé
 * 
 * Chạy: node test_ticket_order_api.js
 */

const axios = require('axios');

// Cấu hình API
const API_BASE_URL = 'http://localhost:4000';
const API_ENDPOINT = '/user/api/create-ticket-order';

// Dữ liệu test
const TEST_DATA = {
  products: [
    {
      id: '64f1234567890abcdef12345',
      name: 'Vé Tour Hạ Long 2N1Đ',
      price: 1500000,
      quantity: 2,
      thumbnail: '/images/tour-ha-long.jpg',
      note: 'Phòng đôi'
    },
    {
      id: '64f1234567890abcdef12346', 
      name: '<PERSON><PERSON>ham quan Vịnh Lan Hạ',
      price: 800000,
      quantity: 1,
      thumbnail: '/images/vinh-lan-ha.jpg',
      note: ''
    }
  ],
  customerName: 'Nguyễn Văn Test',
  phone: '**********',
  email: '<EMAIL>',
  address: '123 Đường ABC, Quận 1, TP.HCM',
  note: 'Đơn hàng test API',
  totalAmount: 3800000,
  subtotal: 3800000,
  discountAmount: 0,
  paymentMethod: 0, // Tiền mặt
  bankCode: 'TM',
  travelDate: '2024-12-25',
  ticketType: 'tour',
  eventLocation: 'Vịnh Hạ Long'
};

// Token test (cần thay bằng token thật)
const TEST_TOKEN = 'your-test-token-here';

async function testCreateTicketOrder() {
  console.log('🎫 Testing Ticket Order API...');
  console.log('📍 Endpoint:', `${API_BASE_URL}${API_ENDPOINT}`);
  console.log('📦 Test Data:', JSON.stringify(TEST_DATA, null, 2));
  
  try {
    const response = await axios.post(
      `${API_BASE_URL}${API_ENDPOINT}`,
      TEST_DATA,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Accept': 'application/json'
        },
        params: {
          output: 'json'
        }
      }
    );

    console.log('✅ API Response Status:', response.status);
    console.log('✅ API Response Data:', JSON.stringify(response.data, null, 2));
    
    // Kiểm tra response structure
    if (response.data && response.data.vnpUrl !== undefined) {
      console.log('✅ Response structure is correct');
      console.log('💳 Payment URL:', response.data.vnpUrl || 'Cash payment (no URL)');
      console.log('🆔 Order ID:', response.data.orderId);
      console.log('💰 Payment ID:', response.data.paymentId);
    } else {
      console.log('⚠️  Response structure might be unexpected');
    }

  } catch (error) {
    console.error('❌ API Test Failed:');
    
    if (error.response) {
      // Server responded with error status
      console.error('📊 Status:', error.response.status);
      console.error('📝 Response:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      // Request was made but no response received
      console.error('📡 No response received:', error.request);
    } else {
      // Something else happened
      console.error('🔧 Error:', error.message);
    }
  }
}

async function testWithoutAuth() {
  console.log('\n🔒 Testing without authentication...');
  
  try {
    const response = await axios.post(
      `${API_BASE_URL}${API_ENDPOINT}`,
      TEST_DATA,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        params: {
          output: 'json'
        }
      }
    );

    console.log('⚠️  Unexpected: API allowed request without auth');
    console.log('📊 Status:', response.status);
    
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly rejected unauthorized request');
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

async function testInvalidData() {
  console.log('\n📝 Testing with invalid data...');
  
  const invalidData = {
    // Missing required fields
    customerName: '',
    phone: '',
    products: [],
    totalAmount: 0
  };
  
  try {
    const response = await axios.post(
      `${API_BASE_URL}${API_ENDPOINT}`,
      invalidData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Accept': 'application/json'
        },
        params: {
          output: 'json'
        }
      }
    );

    console.log('⚠️  Unexpected: API accepted invalid data');
    
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ Correctly rejected invalid data');
      console.log('📝 Error message:', error.response.data.message || 'Validation error');
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

// Chạy tests
async function runTests() {
  console.log('🚀 Starting API Tests...\n');
  
  await testCreateTicketOrder();
  await testWithoutAuth();
  await testInvalidData();
  
  console.log('\n🏁 Tests completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Thay TEST_TOKEN bằng token thật từ login API');
  console.log('2. Kiểm tra database xem đơn hàng đã được tạo chưa');
  console.log('3. Test payment flow với bankCode khác "TM"');
  console.log('4. Test với mobile app thật');
}

runTests().catch(console.error);
