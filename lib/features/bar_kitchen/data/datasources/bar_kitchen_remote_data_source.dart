import '../../../../core/network/api_client.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/kitchen_order_model.dart';
import '../models/kitchen_item_model.dart';
import '../../../invoice/data/models/invoice_model.dart';
import 'package:flutter/foundation.dart';

/// Interface cho remote data source của Bar/Kitchen
abstract class BarKitchenRemoteDataSource {
  /// Lấy danh sách đơn hàng cho bếp (sử dụng API invoice)
  Future<List<KitchenOrderModel>> getKitchenOrders({
    required String storeId,
    String? status,
    DateTime? date,
    int page = 1,
    int limit = 50,
  });

  /// Lấy danh sách món ăn được gom nhóm (API mới)
  Future<List<KitchenItemModel>> getKitchenItems({
    required String storeId,
    String? category,
    DateTime? date,
  });

  /// Cập nhật trạng thái đơn hàng
  Future<KitchenOrderModel> updateOrderStatus({
    required String orderId,
    required String newStatus,
  });

  /// Cập nhật trạng thái món ăn
  Future<KitchenItemModel> updateItemStatus({
    required String itemId,
    required int completedQuantity,
  });
}

/// Implementation của BarKitchenRemoteDataSource
class BarKitchenRemoteDataSourceImpl implements BarKitchenRemoteDataSource {
  final ApiClient apiClient;

  BarKitchenRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<KitchenOrderModel>> getKitchenOrders({
    required String storeId,
    String? status,
    DateTime? date,
    int page = 1,
    int limit = 50,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'storeId': storeId,
        'page': page,
        'limit': limit,
        'output': 'json', // Đảm bảo có output=json
      };

      // Thêm filter cho kitchen orders (chỉ lấy đơn đang xử lý)
      if (status != null) {
        queryParams['status'] = status;
      } else {
        // Lấy các đơn hàng đang cần xử lý trong bếp
        queryParams['statuses'] = '1,2'; // Processing, Confirmed
      }

      if (date != null) {
        queryParams['startDate'] = DateTime(date.year, date.month, date.day).toIso8601String();
        queryParams['endDate'] = DateTime(date.year, date.month, date.day, 23, 59, 59).toIso8601String();
      }

      // Sử dụng API invoice hiện có
      final response = await apiClient.get(
        '/user/api/orders-with-tables',
        queryParameters: queryParams,
      );

      debugPrint('🍳 Kitchen Orders API Response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('🍳 Kitchen Orders Raw Response: $data');
        debugPrint('🍳 Response data type: ${data.runtimeType}');
        debugPrint('🍳 Response keys: ${data is Map ? data.keys.toList() : 'Not a Map'}');

        if (data['error'] == false && data['data'] != null) {
          // Kiểm tra xem data['data'] có phải là List không
          final dynamic rawData = data['data'];
          debugPrint('🍳 Raw data type: ${rawData.runtimeType}');
          debugPrint('🍳 Raw data content: $rawData');

          List<dynamic> ordersData;
          if (rawData is List) {
            ordersData = rawData;
            debugPrint('🍳 Data is List with ${ordersData.length} items');
          } else if (rawData is Map) {
            debugPrint('🍳 Data is Map with keys: ${rawData.keys.toList()}');
            if (rawData.containsKey('orders')) {
              ordersData = rawData['orders'] as List<dynamic>;
              debugPrint('🍳 Found orders key with ${ordersData.length} items');
            } else if (rawData.containsKey('data')) {
              ordersData = rawData['data'] as List<dynamic>;
              debugPrint('🍳 Found nested data key with ${ordersData.length} items');
            } else {
              debugPrint('🍳 Map does not contain expected keys');
              throw ServerException(
                message: 'API response format not recognized',
              );
            }
          } else {
            debugPrint('🍳 Unexpected data format: $rawData');
            throw ServerException(
              message: 'Unexpected data format from API',
            );
          }

          debugPrint('🍳 Found ${ordersData.length} orders for kitchen');

          try {
            // Chuyển đổi từ Invoice sang KitchenOrder
            final result = ordersData
                .map((orderJson) {
                  debugPrint('🍳 Processing order: ${orderJson['_id'] ?? 'unknown'}');
                  return KitchenOrderModel.fromInvoiceJson(orderJson);
                })
                .toList();
            debugPrint('🍳 Successfully converted ${result.length} orders');
            return result;
          } catch (e, stackTrace) {
            debugPrint('🍳 Error converting orders: $e');
            debugPrint('🍳 Stack trace: $stackTrace');
            throw ServerException(
              message: 'Error processing kitchen orders: $e',
            );
          }
        } else {
          debugPrint('🍳 API Error: ${data['message']}');
          throw ServerException(
            message: data['message'] ?? 'Failed to load kitchen orders',
          );
        }
      } else {
        debugPrint('🍳 HTTP Error: ${response.statusCode}');
        throw ServerException(
          message: 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to load kitchen orders: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<KitchenItemModel>> getKitchenItems({
    required String storeId,
    String? category,
    DateTime? date,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'storeId': storeId,
        'output': 'json',
      };

      if (category != null) queryParams['category'] = category;
      if (date != null) {
        queryParams['startDate'] = DateTime(date.year, date.month, date.day).toIso8601String();
        queryParams['endDate'] = DateTime(date.year, date.month, date.day, 23, 59, 59).toIso8601String();
      }

      // Sử dụng API mới cho kitchen items (sẽ tạo sau)
      final response = await apiClient.get(
        '/user/api/kitchen-items-aggregated',
        queryParameters: queryParams,
      );

      debugPrint('🍳 Kitchen Items API Response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['error'] == false && data['data'] != null) {
          final List<dynamic> itemsData = data['data'] as List<dynamic>;
          debugPrint('🍳 Found ${itemsData.length} aggregated items for kitchen');

          return itemsData
              .map((json) => KitchenItemModel.fromJson(json as Map<String, dynamic>))
              .toList();
        } else {
          debugPrint('🍳 API Error: ${data['message']}');
          throw ServerException(
            message: data['message'] ?? 'Failed to get kitchen items',
          );
        }
      } else {
        debugPrint('🍳 HTTP Error: ${response.statusCode}');
        throw ServerException(
          message: 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to load kitchen items: ${e.toString()}',
      );
    }
  }

  @override
  Future<KitchenOrderModel> updateOrderStatus({
    required String orderId,
    required String newStatus,
  }) async {
    try {
      final response = await apiClient.put(
        '/kitchen/orders/$orderId/status',
        data: {'status': newStatus},
      );

      if (response.data['success'] == true) {
        return KitchenOrderModel.fromJson(response.data['data'] as Map<String, dynamic>);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to update order status',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<KitchenItemModel> updateItemStatus({
    required String itemId,
    required int completedQuantity,
  }) async {
    try {
      final response = await apiClient.put(
        '/kitchen/items/$itemId/status',
        data: {'completed_quantity': completedQuantity},
      );

      if (response.data['success'] == true) {
        return KitchenItemModel.fromJson(response.data['data'] as Map<String, dynamic>);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to update item status',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
