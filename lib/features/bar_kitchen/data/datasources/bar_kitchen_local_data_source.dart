import '../models/kitchen_order_model.dart';
import '../models/kitchen_item_model.dart';

/// Interface cho local data source của Bar/Kitchen
abstract class BarKitchenLocalDataSource {
  Future<List<KitchenOrderModel>> getCachedOrders();
  Future<List<KitchenItemModel>> getCachedItems();
  Future<void> cacheOrders(List<KitchenOrderModel> orders);
  Future<void> cacheItems(List<KitchenItemModel> items);
  Future<void> clearCache();
}

/// Implementation của BarKitchenLocalDataSource
class BarKitchenLocalDataSourceImpl implements BarKitchenLocalDataSource {
  // Sample data cho đơn hàng
  final List<KitchenOrderModel> _mockOrders = [
    KitchenOrderModel(
      id: '1',
      orderNumber: '#0345',
      orderTime: DateTime(2021, 6, 20, 13, 30),
      tableName: 'Bàn 1-Khu vực 1',
      customerCount: 10,
      status: 'preparing',
      items: const [
        KitchenOrderItemModel(
          id: '1-1',
          itemName: '<PERSON><PERSON> bống hấp xi dầu',
          quantity: 3,
          category: 'food',
          status: 'preparing',
        ),
        KitchenOrderItemModel(
          id: '1-2',
          itemName: 'Hàu nướng phô mai',
          quantity: 2,
          category: 'food',
          status: 'pending',
        ),
      ],
      notes: '+ Ghi chú',
    ),
    KitchenOrderModel(
      id: '2',
      orderNumber: '#0345',
      orderTime: DateTime(2021, 6, 20, 13, 30),
      tableName: 'Bàn 1-Khu vực 1',
      customerCount: 10,
      status: 'pending',
      items: const [
        KitchenOrderItemModel(
          id: '2-1',
          itemName: 'Cá mú hấp bia',
          quantity: 1,
          category: 'food',
          status: 'pending',
        ),
        KitchenOrderItemModel(
          id: '2-2',
          itemName: 'Tôm hùm nướng',
          quantity: 2,
          category: 'food',
          status: 'pending',
        ),
      ],
      notes: '+ Ghi chú',
    ),
    KitchenOrderModel(
      id: '3',
      orderNumber: '#0345',
      orderTime: DateTime(2021, 6, 20, 13, 30),
      tableName: 'Bàn 1-Khu vực 1',
      customerCount: 10,
      status: 'ready',
      items: const [
        KitchenOrderItemModel(
          id: '3-1',
          itemName: 'Cua rang me',
          quantity: 2,
          category: 'food',
          status: 'ready',
        ),
        KitchenOrderItemModel(
          id: '3-2',
          itemName: 'Bia Saigon',
          quantity: 4,
          category: 'drink',
          status: 'ready',
        ),
      ],
    ),
  ];

  // Sample data cho món ăn theo món
  final List<KitchenItemModel> _mockItems = [
    KitchenItemModel(
      id: 'item-1',
      itemName: 'Cá bống hấp xi dầu',
      category: 'food',
      totalQuantity: 3,
      completedQuantity: 0,
      priority: 'high',
      estimatedTime: DateTime.now().add(const Duration(minutes: 15)),
      orders: const [
        KitchenItemOrderModel(
          orderId: '1',
          orderNumber: '#0345',
          tableName: 'Bàn 1-Khu vực 1',
          quantity: 3,
        ),
      ],
    ),
    KitchenItemModel(
      id: 'item-2',
      itemName: 'Hàu nướng phô mai',
      category: 'food',
      totalQuantity: 3,
      completedQuantity: 1,
      priority: 'medium',
      estimatedTime: DateTime.now().add(const Duration(minutes: 10)),
      orders: const [
        KitchenItemOrderModel(
          orderId: '2',
          orderNumber: '#0345',
          tableName: 'Bàn 1-Khu vực 1',
          quantity: 2,
        ),
        KitchenItemOrderModel(
          orderId: '4',
          orderNumber: '#0348',
          tableName: 'Bàn 5-Khu vực 1',
          quantity: 1,
        ),
      ],
    ),
    KitchenItemModel(
      id: 'item-3',
      itemName: 'Cá mú hấp bia',
      category: 'food',
      totalQuantity: 3,
      completedQuantity: 3,
      priority: 'low',
      orders: const [
        KitchenItemOrderModel(
          orderId: '3',
          orderNumber: '#0345',
          tableName: 'Bàn 1-Khu vực 1',
          quantity: 1,
        ),
        KitchenItemOrderModel(
          orderId: '5',
          orderNumber: '#0349',
          tableName: 'Bàn 7-Khu vực 2',
          quantity: 2,
        ),
      ],
    ),
  ];

  @override
  Future<List<KitchenOrderModel>> getCachedOrders() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));
    return _mockOrders;
  }

  @override
  Future<List<KitchenItemModel>> getCachedItems() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));
    return _mockItems;
  }

  @override
  Future<void> cacheOrders(List<KitchenOrderModel> orders) async {
    // Simulate caching
    await Future.delayed(const Duration(milliseconds: 100));
    // In real implementation, would save to local storage
  }

  @override
  Future<void> cacheItems(List<KitchenItemModel> items) async {
    // Simulate caching
    await Future.delayed(const Duration(milliseconds: 100));
    // In real implementation, would save to local storage
  }

  @override
  Future<void> clearCache() async {
    // Simulate cache clearing
    await Future.delayed(const Duration(milliseconds: 50));
    // In real implementation, would clear local storage
  }
}
