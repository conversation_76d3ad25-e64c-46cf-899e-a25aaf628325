import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/kitchen_order.dart';
import '../../domain/entities/kitchen_item.dart';
import '../../domain/repositories/bar_kitchen_repository.dart';
import '../datasources/bar_kitchen_remote_data_source.dart';
import '../datasources/bar_kitchen_local_data_source.dart';

/// Implementation của BarKitchenRepository
class BarKitchenRepositoryImpl implements BarKitchenRepository {
  final BarKitchenRemoteDataSource remoteDataSource;
  final BarKitchenLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  BarKitchenRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<KitchenOrder>>> getKitchenOrders({
    String? status,
    DateTime? date,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        // Gọi API để lấy orders
        final orderModels = await remoteDataSource.getKitchenOrders(
          storeId: '623a97cdbe781e0ba814f227', // Default store ID
          status: status,
          date: date,
        );

        // Cache data
        await localDataSource.cacheOrders(orderModels);

        return Right(orderModels.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      try {
        final orderModels = await localDataSource.getCachedOrders();
        return Right(orderModels.map((model) => model.toEntity()).toList());
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, List<KitchenItem>>> getKitchenItems({
    String? category,
    DateTime? date,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        // Gọi API để lấy kitchen items
        final itemModels = await remoteDataSource.getKitchenItems(
          storeId: '623a97cdbe781e0ba814f227', // Default store ID
          category: category,
          date: date,
        );

        // Cache data
        await localDataSource.cacheItems(itemModels);

        return Right(itemModels.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      try {
        final itemModels = await localDataSource.getCachedItems();
        return Right(itemModels.map((model) => model.toEntity()).toList());
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, KitchenOrder>> updateOrderStatus({
    required String orderId,
    required String newStatus,
  }) async {
    try {
      final orderModels = await localDataSource.getCachedOrders();
      final orderModel = orderModels.firstWhere((o) => o.id == orderId);
      final updatedOrder = orderModel.copyWith(status: newStatus);
      return Right(updatedOrder);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update order status: $e'));
    }
  }

  @override
  Future<Either<Failure, KitchenItem>> updateItemStatus({
    required String itemId,
    required int completedQuantity,
  }) async {
    try {
      final itemModels = await localDataSource.getCachedItems();
      final itemModel = itemModels.firstWhere((i) => i.id == itemId);
      final updatedItem =
          itemModel.copyWith(completedQuantity: completedQuantity);
      return Right(updatedItem);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update item status: $e'));
    }
  }

  @override
  Future<Either<Failure, KitchenOrder>> getOrderDetail(String orderId) async {
    // For now, get from the list of orders
    final ordersResult = await getKitchenOrders();
    return ordersResult.fold(
      (failure) => Left(failure),
      (orders) {
        try {
          final order = orders.firstWhere((o) => o.id == orderId);
          return Right(order);
        } catch (e) {
          return Left(ServerFailure(message: 'Order not found'));
        }
      },
    );
  }

  @override
  Future<Either<Failure, KitchenItem>> getItemDetail(String itemId) async {
    // For now, get from the list of items
    final itemsResult = await getKitchenItems();
    return itemsResult.fold(
      (failure) => Left(failure),
      (items) {
        try {
          final item = items.firstWhere((i) => i.id == itemId);
          return Right(item);
        } catch (e) {
          return Left(ServerFailure(message: 'Item not found'));
        }
      },
    );
  }

  @override
  Future<Either<Failure, void>> refreshData() async {
    try {
      await localDataSource.clearCache();
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to refresh data'));
    }
  }
}
