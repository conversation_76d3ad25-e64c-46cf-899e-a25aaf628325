import '../../domain/entities/kitchen_item.dart';

/// Data model cho KitchenItem với JSON serialization
class KitchenItemModel extends KitchenItem {
  const KitchenItemModel({
    required super.id,
    required super.itemName,
    required super.category,
    required super.totalQuantity,
    required super.completedQuantity,
    required super.orders,
    required super.priority,
    super.estimatedTime,
  });

  /// Tạo từ JSON
  factory KitchenItemModel.fromJson(Map<String, dynamic> json) {
    return KitchenItemModel(
      id: json['id'] as String? ?? json['itemName'] as String? ?? '',
      itemName: json['itemName'] as String? ?? json['item_name'] as String? ?? '',
      category: json['category'] as String? ?? 'food',
      totalQuantity: json['totalQuantity'] as int? ?? json['total_quantity'] as int? ?? 0,
      completedQuantity: json['completedQuantity'] as int? ?? json['completed_quantity'] as int? ?? 0,
      orders: (json['orders'] as List<dynamic>? ?? [])
          .map((order) => KitchenItemOrderModel.fromJson(order as Map<String, dynamic>))
          .toList(),
      priority: json['priority'] as String? ?? 'medium',
      estimatedTime: json['estimatedTime'] != null
          ? DateTime.parse(json['estimatedTime'] as String)
          : json['estimated_time'] != null
              ? DateTime.parse(json['estimated_time'] as String)
              : null,
    );
  }

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'item_name': itemName,
      'category': category,
      'total_quantity': totalQuantity,
      'completed_quantity': completedQuantity,
      'orders': orders.map((order) => (order as KitchenItemOrderModel).toJson()).toList(),
      'priority': priority,
      'estimated_time': estimatedTime?.toIso8601String(),
    };
  }

  /// Tạo từ entity
  factory KitchenItemModel.fromEntity(KitchenItem item) {
    return KitchenItemModel(
      id: item.id,
      itemName: item.itemName,
      category: item.category,
      totalQuantity: item.totalQuantity,
      completedQuantity: item.completedQuantity,
      orders: item.orders.map((order) => KitchenItemOrderModel.fromEntity(order)).toList(),
      priority: item.priority,
      estimatedTime: item.estimatedTime,
    );
  }

  /// Chuyển thành entity
  KitchenItem toEntity() {
    return KitchenItem(
      id: id,
      itemName: itemName,
      category: category,
      totalQuantity: totalQuantity,
      completedQuantity: completedQuantity,
      orders: orders,
      priority: priority,
      estimatedTime: estimatedTime,
    );
  }
}

/// Data model cho KitchenItemOrder với JSON serialization
class KitchenItemOrderModel extends KitchenItemOrder {
  const KitchenItemOrderModel({
    required super.orderId,
    required super.orderNumber,
    required super.tableName,
    required super.quantity,
    super.specialInstructions,
  });

  /// Tạo từ JSON
  factory KitchenItemOrderModel.fromJson(Map<String, dynamic> json) {
    return KitchenItemOrderModel(
      orderId: json['orderId'] as String? ?? json['order_id'] as String? ?? '',
      orderNumber: json['orderNumber'] as String? ?? json['order_number'] as String? ?? '',
      tableName: json['tableName'] as String? ?? json['table_name'] as String? ?? '',
      quantity: json['quantity'] as int? ?? 1,
      specialInstructions: json['specialInstructions'] as String? ?? json['special_instructions'] as String?,
    );
  }

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'order_id': orderId,
      'order_number': orderNumber,
      'table_name': tableName,
      'quantity': quantity,
      'special_instructions': specialInstructions,
    };
  }

  /// Tạo từ entity
  factory KitchenItemOrderModel.fromEntity(KitchenItemOrder order) {
    return KitchenItemOrderModel(
      orderId: order.orderId,
      orderNumber: order.orderNumber,
      tableName: order.tableName,
      quantity: order.quantity,
      specialInstructions: order.specialInstructions,
    );
  }

  /// Chuyển thành entity
  KitchenItemOrder toEntity() {
    return KitchenItemOrder(
      orderId: orderId,
      orderNumber: orderNumber,
      tableName: tableName,
      quantity: quantity,
      specialInstructions: specialInstructions,
    );
  }
}
