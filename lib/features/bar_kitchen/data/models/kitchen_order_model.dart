import '../../domain/entities/kitchen_order.dart';

/// Data model cho KitchenOrder với JSON serialization
class KitchenOrderModel extends KitchenOrder {
  const KitchenOrderModel({
    required super.id,
    required super.orderNumber,
    required super.orderTime,
    required super.tableName,
    required super.customerCount,
    required super.status,
    required super.items,
    super.notes,
  });

  /// Tạo từ JSON
  factory KitchenOrderModel.fromJson(Map<String, dynamic> json) {
    return KitchenOrderModel(
      id: json['id'] as String,
      orderNumber: json['order_number'] as String,
      orderTime: DateTime.parse(json['order_time'] as String),
      tableName: json['table_name'] as String,
      customerCount: json['customer_count'] as int,
      status: json['status'] as String,
      items: (json['items'] as List<dynamic>)
          .map((item) => KitchenOrderItemModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      notes: json['notes'] as String?,
    );
  }

  /// Tạo từ Invoice JSON (từ API orders-with-tables)
  factory KitchenOrderModel.fromInvoiceJson(Map<String, dynamic> json) {
    // Parse table info
    final tableInfo = json['tableInfo'] as Map<String, dynamic>?;
    final tableName = tableInfo?['tableName'] as String? ?? 'N/A';

    // Parse order time
    DateTime orderTime;
    try {
      orderTime = DateTime.parse(json['orderTime'] as String);
    } catch (e) {
      orderTime = DateTime.now();
    }

    // Map order status to kitchen status
    String kitchenStatus = _mapOrderStatusToKitchenStatus(json['status'] as int? ?? 0);

    // Parse products to kitchen items
    final products = json['products'] as List<dynamic>? ?? [];
    final kitchenItems = products
        .map((product) => KitchenOrderItemModel.fromInvoiceProductJson(product as Map<String, dynamic>))
        .toList();

    return KitchenOrderModel(
      id: json['_id'] as String? ?? '',
      orderNumber: '#${json['code'] ?? 0}',
      orderTime: orderTime,
      tableName: tableName,
      customerCount: tableInfo?['customerCount'] as int? ?? 1,
      status: kitchenStatus,
      items: kitchenItems,
      notes: json['notes'] as String?,
    );
  }

  /// Map order status sang kitchen status
  static String _mapOrderStatusToKitchenStatus(int orderStatus) {
    switch (orderStatus) {
      case 1: // Processing
        return 'pending';
      case 2: // Confirmed
        return 'preparing';
      case 3: // Paid/Completed
        return 'ready';
      case 4: // Cancelled
        return 'cancelled';
      default:
        return 'pending';
    }
  }

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'order_time': orderTime.toIso8601String(),
      'table_name': tableName,
      'customer_count': customerCount,
      'status': status,
      'items': items.map((item) => (item as KitchenOrderItemModel).toJson()).toList(),
      'notes': notes,
    };
  }

  /// Tạo từ entity
  factory KitchenOrderModel.fromEntity(KitchenOrder order) {
    return KitchenOrderModel(
      id: order.id,
      orderNumber: order.orderNumber,
      orderTime: order.orderTime,
      tableName: order.tableName,
      customerCount: order.customerCount,
      status: order.status,
      items: order.items.map((item) => KitchenOrderItemModel.fromEntity(item)).toList(),
      notes: order.notes,
    );
  }

  /// Chuyển thành entity
  KitchenOrder toEntity() {
    return KitchenOrder(
      id: id,
      orderNumber: orderNumber,
      orderTime: orderTime,
      tableName: tableName,
      customerCount: customerCount,
      status: status,
      items: items,
      notes: notes,
    );
  }
}

/// Data model cho KitchenOrderItem với JSON serialization
class KitchenOrderItemModel extends KitchenOrderItem {
  const KitchenOrderItemModel({
    required super.id,
    required super.itemName,
    required super.quantity,
    required super.category,
    super.specialInstructions,
    required super.status,
  });

  /// Tạo từ JSON
  factory KitchenOrderItemModel.fromJson(Map<String, dynamic> json) {
    return KitchenOrderItemModel(
      id: json['id'] as String,
      itemName: json['item_name'] as String,
      quantity: json['quantity'] as int,
      category: json['category'] as String,
      specialInstructions: json['special_instructions'] as String?,
      status: json['status'] as String,
    );
  }

  /// Tạo từ Invoice Product JSON
  factory KitchenOrderItemModel.fromInvoiceProductJson(Map<String, dynamic> json) {
    // Determine category based on product name or other criteria
    String category = _determineCategoryFromProduct(json['name'] as String? ?? '');

    return KitchenOrderItemModel(
      id: json['_id'] as String? ?? json['productId'] as String? ?? '',
      itemName: json['name'] as String? ?? 'Unknown Item',
      quantity: json['quantity'] as int? ?? 1,
      category: category,
      specialInstructions: json['notes'] as String?,
      status: 'pending', // Default status for new items
    );
  }

  /// Xác định category từ tên sản phẩm
  static String _determineCategoryFromProduct(String productName) {
    final name = productName.toLowerCase();

    // Đồ uống
    if (name.contains('cà phê') || name.contains('coffee') ||
        name.contains('trà') || name.contains('tea') ||
        name.contains('nước') || name.contains('juice') ||
        name.contains('sinh tố') || name.contains('smoothie') ||
        name.contains('soda') || name.contains('coca')) {
      return 'drink';
    }

    // Tráng miệng
    if (name.contains('bánh') || name.contains('cake') ||
        name.contains('kem') || name.contains('ice cream') ||
        name.contains('chè') || name.contains('pudding') ||
        name.contains('tiramisu') || name.contains('flan')) {
      return 'dessert';
    }

    // Mặc định là đồ ăn
    return 'food';
  }

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'item_name': itemName,
      'quantity': quantity,
      'category': category,
      'special_instructions': specialInstructions,
      'status': status,
    };
  }

  /// Tạo từ entity
  factory KitchenOrderItemModel.fromEntity(KitchenOrderItem item) {
    return KitchenOrderItemModel(
      id: item.id,
      itemName: item.itemName,
      quantity: item.quantity,
      category: item.category,
      specialInstructions: item.specialInstructions,
      status: item.status,
    );
  }

  /// Chuyển thành entity
  KitchenOrderItem toEntity() {
    return KitchenOrderItem(
      id: id,
      itemName: itemName,
      quantity: quantity,
      category: category,
      specialInstructions: specialInstructions,
      status: status,
    );
  }
}
