import 'package:flutter/material.dart';

import '../../domain/entities/cho_hai_san_product.dart';
import '../../../../src/common/widgets/product_item_widget.dart';

/// Widget hiển thị grid sản phẩm với responsive design
class ProductGridWidget extends StatelessWidget {
  /// Danh sách sản phẩm
  final List<ChoHaiSanProduct> products;

  /// Callback khi tap vào sản phẩm
  final Function(ChoHaiSanProduct product)? onProductTap;

  /// Callback khi thêm vào giỏ hàng
  final Function(ChoHaiSanProduct product)? onAddToCart;

  /// <PERSON><PERSON> còn trang tiếp theo không
  final bool hasMore;

  /// Đang load more không
  final bool isLoadingMore;

  /// Callback load more
  final VoidCallback? onLoadMore;

  /// Thông báo lỗi load more
  final String? loadMoreError;

  const ProductGridWidget({
    Key? key,
    required this.products,
    this.onProductTap,
    this.onAddToCart,
    this.hasMore = false,
    this.isLoadingMore = false,
    this.onLoadMore,
    this.loadMoreError,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (products.isEmpty) {
      return const _EmptyProductsWidget();
    }

    return CustomScrollView(
      slivers: [
        // Products Grid
        SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4, // Thay đổi từ 3 thành 4 cột
              mainAxisExtent: 240, // Tăng thêm một chút để tránh overflow
              crossAxisSpacing:
                  16, // Giảm khoảng cách ngang để phù hợp với 4 cột
              mainAxisSpacing: 24, // Giảm khoảng cách dọc
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final product = products[index];
                return ProductItemWidget(
                  title: product.name,
                  imageUrl: product.thumbnail,
                  price: product.formattedPrice,
                  oldPrice:
                      product.hasDiscount ? product.formattedPriceOld : null,
                  discount: product.discount,
                  onTap: () => onProductTap?.call(product),
                );
              },
              childCount: products.length,
            ),
          ),
        ),

        // Load More Section
        if (hasMore || isLoadingMore || loadMoreError != null)
          SliverToBoxAdapter(
            child: _LoadMoreSection(
              hasMore: hasMore,
              isLoadingMore: isLoadingMore,
              onLoadMore: onLoadMore,
              loadMoreError: loadMoreError,
            ),
          ),
      ],
    );
  }
}

/// Widget hiển thị khi không có sản phẩm
class _EmptyProductsWidget extends StatelessWidget {
  const _EmptyProductsWidget();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Không có sản phẩm nào',
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Widget xử lý load more
class _LoadMoreSection extends StatelessWidget {
  final bool hasMore;
  final bool isLoadingMore;
  final VoidCallback? onLoadMore;
  final String? loadMoreError;

  const _LoadMoreSection({
    required this.hasMore,
    required this.isLoadingMore,
    this.onLoadMore,
    this.loadMoreError,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Loading indicator
          if (isLoadingMore) const CircularProgressIndicator(),

          // Load more button
          if (hasMore && !isLoadingMore && loadMoreError == null)
            ElevatedButton(
              onPressed: onLoadMore,
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(200, 44),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('XEM THÊM SẢN PHẨM'),
            ),

          // Error message with retry
          if (loadMoreError != null)
            Column(
              children: [
                Text(
                  loadMoreError!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.red[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: onLoadMore,
                  child: const Text('THỬ LẠI'),
                ),
              ],
            ),

          // End message
          if (!hasMore && !isLoadingMore && loadMoreError == null)
            Text(
              'Đã hiển thị tất cả sản phẩm',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
        ],
      ),
    );
  }
}
