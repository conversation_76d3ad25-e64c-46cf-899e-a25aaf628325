import '../../../../core/network/api_client.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/order_model.dart';

/// Interface cho remote data source của Orders
abstract class OrdersRemoteDataSource {
  Future<List<OrderModel>> getOrdersWithTableInfo({
    required String storeId,
    List<int>? statuses,
    int page = 1,
    int limit = 20,
  });

  Future<List<OrderModel>> getOrdersByTable({
    required String areaId,
    required String tableId,
    required String storeId,
    List<int>? statuses,
  });

  Future<OrderModel?> getCurrentOrderByTable({
    required String areaId,
    required String tableId,
    required String storeId,
  });

  Future<OrderModel> createOrderWithTable({
    required String storeId,
    required String areaId,
    required String tableId,
    required String serviceType,
    required List<Map<String, dynamic>> products,
    required String customerName,
    required String phone,
    required double totalAmount,
    String? email,
    double? subtotal,
    double? discountAmount,
    String? coupon,
    String? note,
    int? paymentMethod,
    String? bankCode,
    int? numberOfGuests,
    int? estimatedDuration,
    String? specialRequests,
  });

  Future<bool> updateOrderAndTableStatus({
    required String orderId,
    required int newStatus,
    required String storeId,
  });

  Future<Map<String, dynamic>> getTableOrderStats({
    required String storeId,
    DateTime? fromDate,
    DateTime? toDate,
  });

  Future<OrderModel> getOrderById({
    required String orderId,
  });

  Future<bool> cancelOrder({
    required String orderId,
    required String storeId,
    String? reason,
  });

  Future<List<OrderModel>> getOrdersByStatus({
    required String storeId,
    required List<int> statuses,
    int page = 1,
    int limit = 20,
  });

  Future<List<OrderModel>> searchOrders({
    required String storeId,
    String? keyword,
    List<int>? statuses,
    DateTime? fromDate,
    DateTime? toDate,
    int page = 1,
    int limit = 20,
  });
}

/// Implementation của OrdersRemoteDataSource
class OrdersRemoteDataSourceImpl implements OrdersRemoteDataSource {
  final ApiClient apiClient;

  OrdersRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<OrderModel>> getOrdersWithTableInfo({
    required String storeId,
    List<int>? statuses,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'storeId': storeId,
        'page': page,
        'limit': limit,
      };

      if (statuses != null && statuses.isNotEmpty) {
        queryParams['statuses'] = statuses.join(',');
      }

      final response = await apiClient.get(
        '/user/api/orders-with-tables?output=json',
        queryParameters: queryParams,
      );

      if (response.data['success'] == true) {
        final List<dynamic> ordersJson = response.data['data']['orders'] as List<dynamic>;
        return ordersJson
            .map((json) => OrderModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get orders with table info',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<List<OrderModel>> getOrdersByTable({
    required String areaId,
    required String tableId,
    required String storeId,
    List<int>? statuses,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'storeId': storeId,
      };

      if (statuses != null && statuses.isNotEmpty) {
        queryParams['statuses'] = statuses.join(',');
      }

      final response = await apiClient.get(
        '/user/api/orders-by-table/$areaId/$tableId',
        queryParameters: queryParams,
      );

      if (response.data['success'] == true) {
        final List<dynamic> ordersJson = response.data['data']['orders'] as List<dynamic>;
        return ordersJson
            .map((json) => OrderModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get orders by table',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<OrderModel?> getCurrentOrderByTable({
    required String areaId,
    required String tableId,
    required String storeId,
  }) async {
    try {
      // Lấy đơn hàng active (status 0, 1, 2)
      final orders = await getOrdersByTable(
        areaId: areaId,
        tableId: tableId,
        storeId: storeId,
        statuses: [0, 1, 2],
      );

      // Trả về đơn hàng gần nhất (đã sort theo createAt desc)
      return orders.isNotEmpty ? orders.first : null;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<OrderModel> createOrderWithTable({
    required String storeId,
    required String areaId,
    required String tableId,
    required String serviceType,
    required List<Map<String, dynamic>> products,
    required String customerName,
    required String phone,
    required double totalAmount,
    String? email,
    double? subtotal,
    double? discountAmount,
    String? coupon,
    String? note,
    int? paymentMethod,
    String? bankCode,
    int? numberOfGuests,
    int? estimatedDuration,
    String? specialRequests,
  }) async {
    try {
      final Map<String, dynamic> requestData = {
        'storeId': storeId,
        'areaId': areaId,
        'tableId': tableId,
        'serviceType': serviceType,
        'products': products,
        'customerName': customerName,
        'phone': phone,
        'totalAmount': totalAmount,
        'email': email ?? '',
        'subtotal': subtotal ?? totalAmount,
        'discountAmount': discountAmount ?? 0,
        'coupon': coupon ?? '',
        'note': note ?? '',
        'paymentMethod': paymentMethod ?? 0,
        'bankCode': bankCode ?? 'TM',
        'numberOfGuests': numberOfGuests ?? 1,
        'estimatedDuration': estimatedDuration ?? 60,
        'specialRequests': specialRequests ?? '',
      };

      final response = await apiClient.post(
        '/user/api/create-order-with-table',
        data: requestData,
      );

      if (response.data['success'] == true) {
        return OrderModel.fromJson(response.data['data']['order'] as Map<String, dynamic>);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to create order with table',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<bool> updateOrderAndTableStatus({
    required String orderId,
    required int newStatus,
    required String storeId,
  }) async {
    try {
      final Map<String, dynamic> requestData = {
        'status': newStatus,
        'storeId': storeId,
      };

      final response = await apiClient.post(
        '/user/api/update-order-table-status/$orderId',
        data: requestData,
      );

      if (response.data['success'] == true) {
        return response.data['data']['updated'] == true;
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to update order status',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<Map<String, dynamic>> getTableOrderStats({
    required String storeId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'storeId': storeId,
      };

      if (fromDate != null) {
        queryParams['fromDate'] = fromDate.millisecondsSinceEpoch;
      }
      if (toDate != null) {
        queryParams['toDate'] = toDate.millisecondsSinceEpoch;
      }

      final response = await apiClient.get(
        '/user/api/table-order-stats',
        queryParameters: queryParams,
      );

      if (response.data['success'] == true) {
        return response.data['data'] as Map<String, dynamic>;
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get table order stats',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<OrderModel> getOrderById({
    required String orderId,
  }) async {
    try {
      final response = await apiClient.get('/user/api/orders/$orderId');

      if (response.data['success'] == true) {
        return OrderModel.fromJson(response.data['data']['order'] as Map<String, dynamic>);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get order by id',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<bool> cancelOrder({
    required String orderId,
    required String storeId,
    String? reason,
  }) async {
    try {
      return await updateOrderAndTableStatus(
        orderId: orderId,
        newStatus: 4, // Status 4 = Cancelled
        storeId: storeId,
      );
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<List<OrderModel>> getOrdersByStatus({
    required String storeId,
    required List<int> statuses,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      return await getOrdersWithTableInfo(
        storeId: storeId,
        statuses: statuses,
        page: page,
        limit: limit,
      );
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<List<OrderModel>> searchOrders({
    required String storeId,
    String? keyword,
    List<int>? statuses,
    DateTime? fromDate,
    DateTime? toDate,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'storeId': storeId,
        'page': page,
        'limit': limit,
      };

      if (keyword != null && keyword.isNotEmpty) {
        queryParams['keyword'] = keyword;
      }
      if (statuses != null && statuses.isNotEmpty) {
        queryParams['statuses'] = statuses.join(',');
      }
      if (fromDate != null) {
        queryParams['fromDate'] = fromDate.millisecondsSinceEpoch;
      }
      if (toDate != null) {
        queryParams['toDate'] = toDate.millisecondsSinceEpoch;
      }

      final response = await apiClient.get(
        '/user/api/search-orders',
        queryParameters: queryParams,
      );

      if (response.data['success'] == true) {
        final List<dynamic> ordersJson = response.data['data']['orders'] as List<dynamic>;
        return ordersJson
            .map((json) => OrderModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to search orders',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
