import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/order_entity.dart';
import '../../domain/repositories/orders_repository.dart';
import '../datasources/orders_remote_data_source.dart';

/// Implementation của OrdersRepository
class OrdersRepositoryImpl implements OrdersRepository {
  final OrdersRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  OrdersRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<OrderEntity>>> getOrdersWithTableInfo({
    required String storeId,
    List<int>? statuses,
    int page = 1,
    int limit = 20,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final orders = await remoteDataSource.getOrdersWithTableInfo(
          storeId: storeId,
          statuses: statuses,
          page: page,
          limit: limit,
        );
        return Right(orders);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<OrderEntity>>> getOrdersByTable({
    required String areaId,
    required String tableId,
    required String storeId,
    List<int>? statuses,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final orders = await remoteDataSource.getOrdersByTable(
          areaId: areaId,
          tableId: tableId,
          storeId: storeId,
          statuses: statuses,
        );
        return Right(orders);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, OrderEntity?>> getCurrentOrderByTable({
    required String areaId,
    required String tableId,
    required String storeId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final order = await remoteDataSource.getCurrentOrderByTable(
          areaId: areaId,
          tableId: tableId,
          storeId: storeId,
        );
        return Right(order);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, OrderEntity>> createOrderWithTable({
    required String storeId,
    required String areaId,
    required String tableId,
    required String serviceType,
    required List<Map<String, dynamic>> products,
    required String customerName,
    required String phone,
    required double totalAmount,
    String? email,
    double? subtotal,
    double? discountAmount,
    String? coupon,
    String? note,
    int? paymentMethod,
    String? bankCode,
    int? numberOfGuests,
    int? estimatedDuration,
    String? specialRequests,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final order = await remoteDataSource.createOrderWithTable(
          storeId: storeId,
          areaId: areaId,
          tableId: tableId,
          serviceType: serviceType,
          products: products,
          customerName: customerName,
          phone: phone,
          totalAmount: totalAmount,
          email: email,
          subtotal: subtotal,
          discountAmount: discountAmount,
          coupon: coupon,
          note: note,
          paymentMethod: paymentMethod,
          bankCode: bankCode,
          numberOfGuests: numberOfGuests,
          estimatedDuration: estimatedDuration,
          specialRequests: specialRequests,
        );
        return Right(order);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, bool>> updateOrderAndTableStatus({
    required String orderId,
    required int newStatus,
    required String storeId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.updateOrderAndTableStatus(
          orderId: orderId,
          newStatus: newStatus,
          storeId: storeId,
        );
        return Right(result);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getTableOrderStats({
    required String storeId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final stats = await remoteDataSource.getTableOrderStats(
          storeId: storeId,
          fromDate: fromDate,
          toDate: toDate,
        );
        return Right(stats);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, OrderEntity>> getOrderById({
    required String orderId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final order = await remoteDataSource.getOrderById(orderId: orderId);
        return Right(order);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, bool>> cancelOrder({
    required String orderId,
    required String storeId,
    String? reason,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.cancelOrder(
          orderId: orderId,
          storeId: storeId,
          reason: reason,
        );
        return Right(result);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<OrderEntity>>> getOrdersByStatus({
    required String storeId,
    required List<int> statuses,
    int page = 1,
    int limit = 20,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final orders = await remoteDataSource.getOrdersByStatus(
          storeId: storeId,
          statuses: statuses,
          page: page,
          limit: limit,
        );
        return Right(orders);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<OrderEntity>>> searchOrders({
    required String storeId,
    String? keyword,
    List<int>? statuses,
    DateTime? fromDate,
    DateTime? toDate,
    int page = 1,
    int limit = 20,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final orders = await remoteDataSource.searchOrders(
          storeId: storeId,
          keyword: keyword,
          statuses: statuses,
          fromDate: fromDate,
          toDate: toDate,
          page: page,
          limit: limit,
        );
        return Right(orders);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
