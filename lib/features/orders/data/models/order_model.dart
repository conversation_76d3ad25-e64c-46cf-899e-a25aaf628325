import '../../domain/entities/order_entity.dart';

/// Model cho Order trong data layer
class OrderModel extends OrderEntity {
  const OrderModel({
    required super.id,
    required super.orderId,
    required super.paymentId,
    required super.code,
    required super.serviceType,
    required super.userId,
    required super.storeId,
    required super.customerName,
    required super.phone,
    required super.email,
    required super.products,
    required super.subtotal,
    required super.discountAmount,
    required super.totalAmount,
    required super.transportFee,
    required super.status,
    required super.paymentMethod,
    required super.bankCode,
    required super.isPayOnline,
    required super.note,
    required super.coupon,
    required super.orderTime,
    super.completedTime,
    super.tableInfo,
  });

  /// Factory constructor từ JSON
  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['_id'] ?? '',
      orderId: json['orderId'] ?? '',
      paymentId: json['paymentId'] ?? '',
      code: json['code'] ?? 0,
      serviceType: json['serviceType'] ?? '',
      userId: json['userId'] ?? '',
      storeId: json['storeId'] ?? '',
      customerName: json['customerName'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      products: (json['products'] as List<dynamic>?)
              ?.map((item) => OrderItemModel.fromJson(item))
              .toList() ??
          [],
      subtotal: (json['subtotal'] ?? 0).toDouble(),
      discountAmount: (json['discountAmount'] ?? 0).toDouble(),
      totalAmount: (json['totalAmount'] ?? 0).toDouble(),
      transportFee: (json['transportFee'] ?? 0).toDouble(),
      status: json['status'] ?? 0,
      paymentMethod: json['paymentMethod'] ?? 0,
      bankCode: json['bankCode'] ?? '',
      isPayOnline: json['isPayOnline'] ?? 0,
      note: json['note'] ?? '',
      coupon: json['coupon'] ?? '',
      orderTime: json['orderTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['orderTime'])
          : DateTime.now(),
      completedTime: json['completedTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['completedTime'])
          : null,
      tableInfo: json['tableInfo'] != null
          ? OrderTableInfoModel.fromJson(json['tableInfo'])
          : null,
    );
  }

  /// Chuyển đổi sang JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'orderId': orderId,
      'paymentId': paymentId,
      'code': code,
      'serviceType': serviceType,
      'userId': userId,
      'storeId': storeId,
      'customerName': customerName,
      'phone': phone,
      'email': email,
      'products': products.map((item) => (item as OrderItemModel).toJson()).toList(),
      'subtotal': subtotal,
      'discountAmount': discountAmount,
      'totalAmount': totalAmount,
      'transportFee': transportFee,
      'status': status,
      'paymentMethod': paymentMethod,
      'bankCode': bankCode,
      'isPayOnline': isPayOnline,
      'note': note,
      'coupon': coupon,
      'orderTime': orderTime.millisecondsSinceEpoch,
      'completedTime': completedTime?.millisecondsSinceEpoch,
      'tableInfo': (tableInfo as OrderTableInfoModel?)?.toJson(),
    };
  }

  /// Copy with method
  @override
  OrderModel copyWith({
    String? id,
    String? orderId,
    String? paymentId,
    int? code,
    String? serviceType,
    String? userId,
    String? storeId,
    String? customerName,
    String? phone,
    String? email,
    List<OrderItemEntity>? products,
    double? subtotal,
    double? discountAmount,
    double? totalAmount,
    double? transportFee,
    int? status,
    int? paymentMethod,
    String? bankCode,
    int? isPayOnline,
    String? note,
    String? coupon,
    DateTime? orderTime,
    DateTime? completedTime,
    OrderTableInfoEntity? tableInfo,
  }) {
    return OrderModel(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      paymentId: paymentId ?? this.paymentId,
      code: code ?? this.code,
      serviceType: serviceType ?? this.serviceType,
      userId: userId ?? this.userId,
      storeId: storeId ?? this.storeId,
      customerName: customerName ?? this.customerName,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      products: products ?? this.products,
      subtotal: subtotal ?? this.subtotal,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      transportFee: transportFee ?? this.transportFee,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      bankCode: bankCode ?? this.bankCode,
      isPayOnline: isPayOnline ?? this.isPayOnline,
      note: note ?? this.note,
      coupon: coupon ?? this.coupon,
      orderTime: orderTime ?? this.orderTime,
      completedTime: completedTime ?? this.completedTime,
      tableInfo: tableInfo ?? this.tableInfo,
    );
  }
}

/// Model cho Order Item
class OrderItemModel extends OrderItemEntity {
  const OrderItemModel({
    required super.productId,
    required super.productName,
    required super.productPrice,
    required super.thumbnail,
    required super.count,
    required super.price,
    required super.noteProduct,
    required super.weight,
    required super.classifyActive,
  });

  factory OrderItemModel.fromJson(Map<String, dynamic> json) {
    return OrderItemModel(
      productId: json['productId'] ?? '',
      productName: json['productName'] ?? '',
      productPrice: (json['productPrice'] ?? 0).toDouble(),
      thumbnail: json['thumbnail'] ?? '',
      count: json['count'] ?? 0,
      price: (json['price'] ?? 0).toDouble(),
      noteProduct: json['noteProduct'] ?? '',
      weight: json['weight'] ?? '',
      classifyActive: (json['classifyActive'] as List<dynamic>?)
              ?.map((item) => ClassifyActiveModel.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'productPrice': productPrice,
      'thumbnail': thumbnail,
      'count': count,
      'price': price,
      'noteProduct': noteProduct,
      'weight': weight,
      'classifyActive': classifyActive.map((item) => (item as ClassifyActiveModel).toJson()).toList(),
    };
  }
}

/// Model cho Classify Active
class ClassifyActiveModel extends ClassifyActiveEntity {
  const ClassifyActiveModel({
    required super.id,
    required super.name,
    required super.value,
    required super.price,
    required super.mass,
  });

  factory ClassifyActiveModel.fromJson(Map<String, dynamic> json) {
    return ClassifyActiveModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      value: json['value'] ?? '',
      price: json['price'] ?? '',
      mass: json['mass'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'value': value,
      'price': price,
      'mass': mass,
    };
  }
}

/// Model cho Table Info
class OrderTableInfoModel extends OrderTableInfoEntity {
  const OrderTableInfoModel({
    required super.areaId,
    required super.tableId,
    required super.tableName,
    required super.areaName,
    required super.capacity,
    required super.reservedAt,
    required super.estimatedDuration,
    super.actualStartTime,
    super.actualEndTime,
    required super.tableStatus,
    required super.numberOfGuests,
    required super.specialRequests,
  });

  factory OrderTableInfoModel.fromJson(Map<String, dynamic> json) {
    return OrderTableInfoModel(
      areaId: json['areaId'] ?? '',
      tableId: json['tableId'] ?? '',
      tableName: json['tableName'] ?? '',
      areaName: json['areaName'] ?? '',
      capacity: json['capacity'] ?? 0,
      reservedAt: json['reservedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['reservedAt'])
          : DateTime.now(),
      estimatedDuration: json['estimatedDuration'] ?? 0,
      actualStartTime: json['actualStartTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['actualStartTime'])
          : null,
      actualEndTime: json['actualEndTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['actualEndTime'])
          : null,
      tableStatus: json['tableStatus'] ?? '',
      numberOfGuests: json['numberOfGuests'] ?? 0,
      specialRequests: json['specialRequests'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'areaId': areaId,
      'tableId': tableId,
      'tableName': tableName,
      'areaName': areaName,
      'capacity': capacity,
      'reservedAt': reservedAt.millisecondsSinceEpoch,
      'estimatedDuration': estimatedDuration,
      'actualStartTime': actualStartTime?.millisecondsSinceEpoch,
      'actualEndTime': actualEndTime?.millisecondsSinceEpoch,
      'tableStatus': tableStatus,
      'numberOfGuests': numberOfGuests,
      'specialRequests': specialRequests,
    };
  }
}
