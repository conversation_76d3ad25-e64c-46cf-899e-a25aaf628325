import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../../do_uong/domain/entities/cart_item.dart';
import '../../../do_uong/presentation/bloc/cart_bloc.dart';

class CartItemWidget extends StatelessWidget {
  final CartItem item;

  const CartItemWidget({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: PosSpacing.sm),
      padding: const EdgeInsets.all(PosSpacing.sm),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: PosColors.borderLight),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Image.network(
              item.product.thumbnail,
              width: 50,
              height: 50,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 50,
                  height: 50,
                  color: PosColors.borderLight,
                  child: Icon(
                    Icons.local_drink,
                    color: PosColors.textSecondary,
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: PosSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.name,
                  style: PosTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  item.product.formattedPrice,
                  style: PosTypography.bodySmall.copyWith(
                    color: PosColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  'Ghi chú: ${item.note ?? ''}',
                  style: PosTypography.bodySmall.copyWith(
                    color: PosColors.textSecondary,
                    fontStyle:
                        item.note == null ? FontStyle.italic : FontStyle.normal,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: PosColors.borderLight),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(color: PosColors.borderLight),
                    ),
                  ),
                  child: InkWell(
                    onTap: () {
                      context.read<CartBloc>().add(
                            UpdateQuantity(
                              productId: item.productId,
                              quantity: item.quantity - 1,
                            ),
                          );
                    },
                    child: const Icon(
                      Icons.remove,
                      size: 16,
                      color: PosColors.textSecondary,
                    ),
                  ),
                ),
                Container(
                  width: 40,
                  height: 32,
                  alignment: Alignment.center,
                  child: Text(
                    '${item.quantity}',
                    style: PosTypography.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(color: PosColors.borderLight),
                    ),
                  ),
                  child: InkWell(
                    onTap: () {
                      context.read<CartBloc>().add(
                            UpdateQuantity(
                              productId: item.productId,
                              quantity: item.quantity + 1,
                            ),
                          );
                    },
                    child: const Icon(
                      Icons.add,
                      size: 16,
                      color: PosColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
