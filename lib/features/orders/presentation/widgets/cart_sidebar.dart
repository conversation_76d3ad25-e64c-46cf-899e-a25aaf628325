import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../../do_uong/presentation/bloc/cart_bloc.dart';
import 'cart_item_widget.dart';
import '../../../../components/order_type_selection_modal.dart' as new_modal;

class CartSidebar extends StatefulWidget {
  final String? tableName;
  const CartSidebar({super.key, this.tableName});
  @override
  State<CartSidebar> createState() => _CartSidebarState();
}

class _CartSidebarState extends State<CartSidebar> {
  new_modal.OrderType _selectedOrderType = new_modal.OrderType.dineIn;
  bool _isProcessingPayment = false;

  Future<void> _handlePayment() async {
    setState(() {
      _isProcessingPayment = true;
    });

    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isProcessingPayment = false;
    });

    // TODO: Implement actual payment logic
    debugPrint('Payment completed');
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, cartState) {
        return Column(
          children: [
            Container(
              padding: const EdgeInsets.all(PosSpacing.md),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: PosColors.borderLight),
                ),
              ),
              child: Row(
                children: [
                  InkWell(
                    onTap: () async {
                      final result = await showDialog<new_modal.OrderType>(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) => new_modal.OrderTypeSelectionModal(
                          selectedOrderType: _selectedOrderType,
                          onOrderTypeSelected: (orderType) {
                            Navigator.of(context).pop(orderType);
                          },
                        ),
                      );
                      if (result != null) {
                        setState(() {
                          _selectedOrderType = result;
                        });
                      }
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      height: 36,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                      ),
                      decoration: BoxDecoration(
                        color: _selectedOrderType.color,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _selectedOrderType.icon,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            _selectedOrderType.label,
                            style: const TextStyle(
                              fontFamily: 'Varela',
                              fontSize: 15,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFFFFFFFF),
                              height: 1.0,
                              letterSpacing: 0,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: PosSpacing.sm),
                  Container(
                    height: 36,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFF9800),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        '${cartState.totalQuantity}',
                        style: const TextStyle(
                          fontFamily: 'Varela',
                          fontSize: 15,
                          fontWeight: FontWeight.w400,
                          color: Color(0xFFFFFFFF),
                          height: 1.0,
                          letterSpacing: 0,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '#0345',
                    style: const TextStyle(
                      fontFamily: 'Varela',
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF966021),
                      height: 1.0,
                      letterSpacing: 0,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: cartState.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.local_drink_outlined,
                            size: 64,
                            color: PosColors.textSecondary,
                          ),
                          const SizedBox(height: PosSpacing.md),
                          Text(
                            'Giỏ hàng trống',
                            style: PosTypography.bodyLarge.copyWith(
                              color: PosColors.textSecondary,
                            ),
                          ),
                          const SizedBox(height: PosSpacing.sm),
                          Text(
                            'Thêm đồ uống vào giỏ hàng',
                            style: PosTypography.bodyMedium.copyWith(
                              color: PosColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(PosSpacing.sm),
                      itemCount: cartState.items.length,
                      itemBuilder: (context, index) {
                        final item = cartState.items[index];
                        return CartItemWidget(item: item);
                      },
                    ),
            ),
            if (cartState.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(PosSpacing.md),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(color: PosColors.borderLight),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Thanh tiền:',
                          style: PosTypography.bodyLarge.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          NumberFormat.currency(locale: 'vi_VN', symbol: 'đ')
                              .format(cartState.totalPrice),
                          style: PosTypography.headingSmall.copyWith(
                            color: PosColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: PosSpacing.md),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.only(right: 20),
                            child: ElevatedButton(
                              onPressed:
                                  _isProcessingPayment ? null : _handlePayment,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF27C7FF),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: PosSpacing.md,
                                ),
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                disabledBackgroundColor: const Color(0xFF27C7FF)
                                    .withValues(alpha: 0.7),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'THANH TOÁN',
                                    style: PosTypography.bodyMedium.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                  if (_isProcessingPayment) ...[
                                    const SizedBox(width: 8),
                                    const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: PosSpacing.sm),
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFF9800),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: IconButton(
                            onPressed: () {
                              context.read<CartBloc>().add(const ClearCart());
                            },
                            icon: const Icon(
                              Icons.delete_outline,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }
}
