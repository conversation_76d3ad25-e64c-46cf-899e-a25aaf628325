import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../models/pos_models.dart';

class OrderCardWidget extends StatelessWidget {
  final Order order;
  final VoidCallback? onTap;
  final bool isSelected;

  const OrderCardWidget({
    super.key,
    required this.order,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: PosColors.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _getStatusBorderColor(),
            width: 1.5,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildHeader(),
              _buildTotal(),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('#${order.id}-Bàn ${order.tableId}',
            style: PosTypography.bodyLarge),
        _buildStatusChip(order.status),
      ],
    );
  }

  Widget _buildTotal() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          NumberFormat.currency(locale: 'vi_VN', symbol: 'đ')
              .format(order.total),
          style: PosTypography.headingLarge
              .copyWith(color: PosColors.textSecondary),
        ),
        if (order.status == OrderStatus.completed)
          Icon(Icons.print_outlined, color: PosColors.textSecondary),
      ],
    );
  }

  Widget _buildFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          DateFormat('dd-MM-yyyy HH:mm').format(order.createdAt),
          style:
              PosTypography.bodyMedium.copyWith(color: PosColors.textSecondary),
        ),
        Text(
          '${order.items.length} đồ',
          style:
              PosTypography.bodyMedium.copyWith(color: PosColors.textSecondary),
        ),
      ],
    );
  }

  Widget _buildStatusChip(OrderStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusChipColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        _getStatusText(status),
        style: PosTypography.bodySmall
            .copyWith(color: _getStatusChipColor(status)),
      ),
    );
  }

  Color _getStatusBorderColor() {
    return isSelected ? PosColors.success : PosColors.borderLight;
  }

  Color _getStatusChipColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return PosColors.warning;
      case OrderStatus.completed:
        return PosColors.success;
      default:
        return PosColors.textSecondary;
    }
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Đang chờ';
      case OrderStatus.preparing:
        return 'Đang làm';
      case OrderStatus.ready:
        return 'Sẵn sàng';
      case OrderStatus.served:
        return 'Đã phục vụ';
      case OrderStatus.completed:
        return 'PAID';
      case OrderStatus.cancelled:
        return 'Đã hủy';
    }
  }
}
