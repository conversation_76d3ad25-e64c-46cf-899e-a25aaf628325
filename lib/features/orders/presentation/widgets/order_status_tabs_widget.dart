import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../bloc/orders_bloc.dart';
import '../bloc/orders_event.dart';
import '../bloc/orders_state.dart';

/// Widget hiển thị tabs trạng thái đơn hàng với số lượng
class OrderStatusTabsWidget extends StatelessWidget {
  final String storeId;
  final Function(List<int>?) onStatusChanged;

  const OrderStatusTabsWidget({
    super.key,
    required this.storeId,
    required this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrdersBloc, OrdersState>(
      builder: (context, state) {
        final orderCounts = state is OrdersLoaded ? state.orderCountByStatus : <int, int>{};
        final currentFilter = state is OrdersLoaded ? state.currentStatusFilter : null;

        return Container(
          height: 60,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: PosColors.surface,
            border: Border(
              bottom: BorderSide(color: PosColors.borderLight),
            ),
          ),
          child: Row(
            children: [
              _buildStatusTab(
                'Tất cả',
                _getTotalCount(orderCounts),
                _isTabSelected(null, currentFilter),
                () => _onTabTapped(context, null),
              ),
              const SizedBox(width: 16),
              _buildStatusTab(
                'Đang xử lý',
                _getActiveCount(orderCounts),
                _isTabSelected([0, 1, 2], currentFilter),
                () => _onTabTapped(context, [0, 1, 2]),
              ),
              const SizedBox(width: 16),
              _buildStatusTab(
                'Hoàn thành',
                orderCounts[3] ?? 0,
                _isTabSelected([3], currentFilter),
                () => _onTabTapped(context, [3]),
              ),
              const SizedBox(width: 16),
              _buildStatusTab(
                'Đã hủy',
                orderCounts[4] ?? 0,
                _isTabSelected([4], currentFilter),
                () => _onTabTapped(context, [4]),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusTab(
    String label,
    int count,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? PosColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? PosColors.primary : PosColors.borderLight,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: PosTypography.bodyMedium.copyWith(
                color: isSelected ? Colors.white : PosColors.textPrimary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected ? Colors.white.withValues(alpha: 0.2) : PosColors.background,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: PosTypography.bodySmall.copyWith(
                  color: isSelected ? Colors.white : PosColors.textSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onTabTapped(BuildContext context, List<int>? statuses) {
    context.read<OrdersBloc>().add(FilterOrdersByStatus(
      storeId: storeId,
      statuses: statuses ?? [0, 1, 2, 3, 4, 5],
    ));
    onStatusChanged(statuses);
  }

  bool _isTabSelected(List<int>? tabStatuses, List<int>? currentFilter) {
    if (tabStatuses == null && currentFilter == null) return true;
    if (tabStatuses == null || currentFilter == null) return false;
    
    if (tabStatuses.length != currentFilter.length) return false;
    
    for (int status in tabStatuses) {
      if (!currentFilter.contains(status)) return false;
    }
    
    return true;
  }

  int _getTotalCount(Map<int, int> orderCounts) {
    return orderCounts.values.fold(0, (sum, count) => sum + count);
  }

  int _getActiveCount(Map<int, int> orderCounts) {
    return (orderCounts[0] ?? 0) + (orderCounts[1] ?? 0) + (orderCounts[2] ?? 0);
  }
}
