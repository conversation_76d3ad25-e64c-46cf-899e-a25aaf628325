import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../domain/entities/order_entity.dart';

/// Widget hiển thị thông tin đơn hàng sử dụng OrderEntity
class OrderCardEntityWidget extends StatelessWidget {
  final OrderEntity order;
  final VoidCallback? onTap;
  final bool isSelected;

  const OrderCardEntityWidget({
    super.key,
    required this.order,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: PosColors.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _getStatusBorderColor(),
            width: 1.5,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildHeader(),
              _buildTotal(),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '#${order.orderId}${order.tableInfo != null ? '-${order.tableInfo!.tableName}' : ''}',
          style: PosTypography.bodyLarge,
        ),
        _buildStatusChip(order.status),
      ],
    );
  }

  Widget _buildTotal() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          NumberFormat.currency(locale: 'vi_VN', symbol: 'đ')
              .format(order.totalAmount),
          style: PosTypography.headingLarge
              .copyWith(color: PosColors.textSecondary),
        ),
        if (order.status == 3) // Completed
          const Icon(Icons.print_outlined, color: PosColors.textSecondary),
      ],
    );
  }

  Widget _buildFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          DateFormat('dd-MM-yyyy HH:mm').format(order.orderTime),
          style:
              PosTypography.bodyMedium.copyWith(color: PosColors.textSecondary),
        ),
        Text(
          '${order.products.length} đồ',
          style:
              PosTypography.bodyMedium.copyWith(color: PosColors.textSecondary),
        ),
      ],
    );
  }

  Widget _buildStatusChip(int status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusChipColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        _getStatusText(status),
        style: PosTypography.bodySmall
            .copyWith(color: _getStatusChipColor(status)),
      ),
    );
  }

  Color _getStatusBorderColor() {
    return isSelected ? PosColors.success : PosColors.borderLight;
  }

  Color _getStatusChipColor(int status) {
    switch (status) {
      case 0: // Chờ xác nhận
        return PosColors.warning;
      case 1: // Đang xử lý
        return PosColors.info;
      case 2: // Đang giao/Sẵn sàng
        return PosColors.primary;
      case 3: // Hoàn thành
        return PosColors.success;
      case 4: // Hủy
        return PosColors.error;
      case 5: // Trả hàng
        return PosColors.error;
      default:
        return PosColors.textSecondary;
    }
  }

  String _getStatusText(int status) {
    switch (status) {
      case 0:
        return 'Chờ xác nhận';
      case 1:
        return 'Đang xử lý';
      case 2:
        return 'Sẵn sàng';
      case 3:
        return 'PAID';
      case 4:
        return 'Đã hủy';
      case 5:
        return 'Trả hàng';
      default:
        return 'Không xác định';
    }
  }
}
