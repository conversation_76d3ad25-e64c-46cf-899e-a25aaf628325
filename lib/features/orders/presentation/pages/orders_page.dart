import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';

import '../../../do_uong/presentation/bloc/cart_bloc.dart';
import '../widgets/cart_sidebar.dart';
import '../../../../components/order_type_selection_modal.dart';
import '../bloc/orders_bloc.dart';
import '../bloc/orders_event.dart';
import '../bloc/orders_state.dart';
import '../../domain/entities/order_entity.dart';
import '../widgets/order_card_entity_widget.dart';
import '../widgets/order_status_tabs_widget.dart';

/// Orders Page - Feature-based structure
class OrdersPage extends StatefulWidget {
  const OrdersPage({super.key});

  @override
  State<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage> {
  final List<int> _selectedStatuses = [0, 1, 2]; // Default: active orders

  // Mock storeId - trong thực tế sẽ lấy từ user session
  final String _storeId = '623a97cdbe781e0ba814f227';



  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => CartBloc()),
        BlocProvider(create: (context) => GetIt.instance<OrdersBloc>()
          ..add(LoadOrdersWithTableInfo(
            storeId: _storeId,
            statuses: _selectedStatuses,
          ))),
      ],
      child: Scaffold(
        backgroundColor: PosColors.background,
        body: Row(
          children: [
            // Main Content
            Expanded(
              child: Column(
                children: [
                  // Status Tabs
                  OrderStatusTabsWidget(
                    storeId: _storeId,
                    onStatusChanged: (statuses) {
                      // Update selected statuses if needed
                    },
                  ),

                  _buildFilterSection(),

                  // Orders List
                  Expanded(
                    child: BlocConsumer<OrdersBloc, OrdersState>(
                      listener: (context, state) {
                        if (state is OrdersError) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(state.message),
                              backgroundColor: PosColors.error,
                            ),
                          );
                        } else if (state is OrderCreated) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Tạo đơn hàng thành công: ${state.order.orderId}'),
                              backgroundColor: PosColors.success,
                            ),
                          );
                          // Refresh orders list
                          context.read<OrdersBloc>().add(RefreshOrders(storeId: _storeId));
                        } else if (state is OrderStatusUpdated) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Cập nhật trạng thái đơn hàng thành công'),
                              backgroundColor: PosColors.success,
                            ),
                          );
                        }
                      },
                      builder: (context, state) {
                        if (state is OrdersLoading) {
                          return const Center(child: CircularProgressIndicator());
                        } else if (state is OrdersLoaded) {
                          return _buildOrdersGrid(state);
                        } else if (state is OrdersError) {
                          return _buildErrorWidget(state);
                        }
                        return const Center(child: Text('Chưa có dữ liệu'));
                      },
                    ),
                  ),
                ],
              ),
            ),
            // Cart Sidebar
            Container(
              width: 350,
              decoration: BoxDecoration(
                color: PosColors.surface,
                border: Border(
                  left: BorderSide(color: PosColors.borderLight),
                ),
              ),
              child: const CartSidebar(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build orders grid widget
  Widget _buildOrdersGrid(OrdersLoaded state) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 24,
        mainAxisSpacing: 24,
        childAspectRatio: 2.4,
      ),
      itemCount: state.filteredOrders.length + 1, // +1 for the add button
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildCreateOrderCard();
        }
        final order = state.filteredOrders[index - 1];
        return OrderCardEntityWidget(
          order: order,
          isSelected: state.selectedOrderId == order.id,
          onTap: () {
            context.read<OrdersBloc>().add(SelectOrder(orderId: order.id));
            // TODO: Load order items to cart
            _loadOrderToCart(order);
          },
        );
      },
    );
  }

  /// Build error widget
  Widget _buildErrorWidget(OrdersError state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: PosColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Có lỗi xảy ra',
            style: PosTypography.headingMedium,
          ),
          const SizedBox(height: 8),
          Text(
            state.message,
            style: PosTypography.bodyMedium.copyWith(color: PosColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<OrdersBloc>().add(RefreshOrders(storeId: _storeId));
            },
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  /// Load order items to cart
  void _loadOrderToCart(OrderEntity order) {
    context.read<CartBloc>().add(ClearCart());
    // TODO: Convert OrderItemEntity to DoUongProduct and add to cart
    // This will be implemented when we have proper product conversion
  }

  Widget _buildFilterSection() {
    return Container(
      color: PosColors.surface,
      padding: const EdgeInsets.symmetric(
        horizontal: PosSpacing.lg,
        vertical: PosSpacing.md,
      ),
      child: Row(
        children: [
          _buildStatusFilterDropdown(),
          const SizedBox(width: PosSpacing.lg),
          _buildDateFilterDropdown(),
          const Spacer(),
          SizedBox(
            width: 250,
            height: 40,
            child: TextField(
              onChanged: (value) {
                // Debounce search
                Future.delayed(const Duration(milliseconds: 500), () {
                  if (mounted && value.isNotEmpty) {
                    context.read<OrdersBloc>().add(SearchOrders(
                      storeId: _storeId,
                      keyword: value,
                    ));
                  }
                });
              },
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: PosSpacing.md),
                hintText: 'Tìm kiếm theo mã đơn, tên khách...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: PosColors.borderLight),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: PosColors.borderLight),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: PosColors.primary),
                ),
              ),
            ),
          ),
          const SizedBox(width: PosSpacing.sm),
          IconButton(
            onPressed: () {
              context.read<OrdersBloc>().add(RefreshOrders(storeId: _storeId));
            },
            icon: const Icon(Icons.refresh),
            style: IconButton.styleFrom(
              backgroundColor: PosColors.success,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build status filter dropdown
  Widget _buildStatusFilterDropdown() {
    return BlocBuilder<OrdersBloc, OrdersState>(
      builder: (context, state) {
        final currentFilter = state is OrdersLoaded ? state.currentStatusFilter : null;

        return DropdownButtonHideUnderline(
          child: DropdownButton<String>(
            value: _getStatusFilterLabel(currentFilter),
            items: [
              const DropdownMenuItem(value: 'TẤT CẢ', child: Text('TẤT CẢ')),
              const DropdownMenuItem(value: 'ĐANG XỬ LÝ', child: Text('ĐANG XỬ LÝ')),
              const DropdownMenuItem(value: 'HOÀN THÀNH', child: Text('HOÀN THÀNH')),
              const DropdownMenuItem(value: 'ĐÃ HỦY', child: Text('ĐÃ HỦY')),
            ],
            onChanged: (value) {
              List<int>? statuses;
              switch (value) {
                case 'ĐANG XỬ LÝ':
                  statuses = [0, 1, 2]; // Active orders
                  break;
                case 'HOÀN THÀNH':
                  statuses = [3];
                  break;
                case 'ĐÃ HỦY':
                  statuses = [4];
                  break;
                default:
                  statuses = null; // All orders
              }

              context.read<OrdersBloc>().add(FilterOrdersByStatus(
                storeId: _storeId,
                statuses: statuses ?? [0, 1, 2, 3, 4],
              ));
            },
            style: PosTypography.bodyMedium,
            icon: const Icon(Icons.keyboard_arrow_down),
          ),
        );
      },
    );
  }

  /// Build date filter dropdown
  Widget _buildDateFilterDropdown() {
    return DropdownButtonHideUnderline(
      child: DropdownButton<String>(
        value: 'HÔM NAY',
        items: const [
          DropdownMenuItem(value: 'HÔM NAY', child: Text('HÔM NAY')),
          DropdownMenuItem(value: 'HÔM QUA', child: Text('HÔM QUA')),
          DropdownMenuItem(value: 'TUẦN NÀY', child: Text('TUẦN NÀY')),
          DropdownMenuItem(value: 'THÁNG NÀY', child: Text('THÁNG NÀY')),
        ],
        onChanged: (value) {
          DateTime? fromDate;
          DateTime? toDate;
          final now = DateTime.now();

          switch (value) {
            case 'HÔM QUA':
              fromDate = DateTime(now.year, now.month, now.day - 1);
              toDate = DateTime(now.year, now.month, now.day);
              break;
            case 'TUẦN NÀY':
              final weekday = now.weekday;
              fromDate = DateTime(now.year, now.month, now.day - weekday + 1);
              toDate = now;
              break;
            case 'THÁNG NÀY':
              fromDate = DateTime(now.year, now.month, 1);
              toDate = now;
              break;
            default: // HÔM NAY
              fromDate = DateTime(now.year, now.month, now.day);
              toDate = now;
          }

          context.read<OrdersBloc>().add(SearchOrders(
            storeId: _storeId,
            fromDate: fromDate,
            toDate: toDate,
          ));
        },
        style: PosTypography.bodyMedium,
        icon: const Icon(Icons.keyboard_arrow_down),
      ),
    );
  }

  /// Get status filter label
  String _getStatusFilterLabel(List<int>? statuses) {
    if (statuses == null || statuses.isEmpty) return 'TẤT CẢ';
    if (statuses.length == 1) {
      switch (statuses.first) {
        case 3:
          return 'HOÀN THÀNH';
        case 4:
          return 'ĐÃ HỦY';
        default:
          return 'TẤT CẢ';
      }
    }
    if (statuses.contains(0) && statuses.contains(1) && statuses.contains(2)) {
      return 'ĐANG XỬ LÝ';
    }
    return 'TẤT CẢ';
  }



  Widget _buildCreateOrderCard() {
    return InkWell(
      onTap: () async {
        final result = await showDialog<OrderType>(
          context: context,
          barrierDismissible: false,
          builder: (context) => OrderTypeSelectionModal(
            onOrderTypeSelected: (orderType) {
              Navigator.of(context).pop(orderType);
            },
          ),
        );

        if (result != null) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Tạo order mới - Loại: ${result.label}'),
            ),
          );
        }
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        decoration: BoxDecoration(
          color: PosColors.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: PosColors.borderLight,
            width: 1.5,
          ),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.add, size: 48, color: PosColors.primary),
              SizedBox(height: PosSpacing.sm),
              Text('Tạo Order',
                  style: TextStyle(fontSize: 16, color: PosColors.primary)),
            ],
          ),
        ),
      ),
    );
  }
}
