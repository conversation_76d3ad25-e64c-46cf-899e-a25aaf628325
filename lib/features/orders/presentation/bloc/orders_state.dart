import 'package:equatable/equatable.dart';
import '../../domain/entities/order_entity.dart';

/// Base class cho tất cả Orders states
abstract class OrdersState extends Equatable {
  const OrdersState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class OrdersInitial extends OrdersState {
  const OrdersInitial();
}

/// Loading state
class OrdersLoading extends OrdersState {
  const OrdersLoading();
}

/// State khi đang refresh
class OrdersRefreshing extends OrdersState {
  final List<OrderEntity> currentOrders;

  const OrdersRefreshing({required this.currentOrders});

  @override
  List<Object?> get props => [currentOrders];
}

/// State khi load thành công
class OrdersLoaded extends OrdersState {
  final List<OrderEntity> orders;
  final List<OrderEntity> filteredOrders;
  final String? selectedOrderId;
  final List<int>? currentStatusFilter;
  final String? currentSearchKeyword;
  final DateTime? currentFromDate;
  final DateTime? currentToDate;
  final bool hasReachedMax;
  final int currentPage;

  const OrdersLoaded({
    required this.orders,
    required this.filteredOrders,
    this.selectedOrderId,
    this.currentStatusFilter,
    this.currentSearchKeyword,
    this.currentFromDate,
    this.currentToDate,
    this.hasReachedMax = false,
    this.currentPage = 1,
  });

  @override
  List<Object?> get props => [
        orders,
        filteredOrders,
        selectedOrderId,
        currentStatusFilter,
        currentSearchKeyword,
        currentFromDate,
        currentToDate,
        hasReachedMax,
        currentPage,
      ];

  /// Copy with method
  OrdersLoaded copyWith({
    List<OrderEntity>? orders,
    List<OrderEntity>? filteredOrders,
    String? selectedOrderId,
    List<int>? currentStatusFilter,
    String? currentSearchKeyword,
    DateTime? currentFromDate,
    DateTime? currentToDate,
    bool? hasReachedMax,
    int? currentPage,
    bool clearSelectedOrder = false,
    bool clearStatusFilter = false,
    bool clearSearchKeyword = false,
    bool clearFromDate = false,
    bool clearToDate = false,
  }) {
    return OrdersLoaded(
      orders: orders ?? this.orders,
      filteredOrders: filteredOrders ?? this.filteredOrders,
      selectedOrderId: clearSelectedOrder ? null : (selectedOrderId ?? this.selectedOrderId),
      currentStatusFilter: clearStatusFilter ? null : (currentStatusFilter ?? this.currentStatusFilter),
      currentSearchKeyword: clearSearchKeyword ? null : (currentSearchKeyword ?? this.currentSearchKeyword),
      currentFromDate: clearFromDate ? null : (currentFromDate ?? this.currentFromDate),
      currentToDate: clearToDate ? null : (currentToDate ?? this.currentToDate),
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
    );
  }

  /// Get selected order
  OrderEntity? get selectedOrder {
    if (selectedOrderId == null) return null;
    try {
      return orders.firstWhere((order) => order.id == selectedOrderId);
    } catch (e) {
      return null;
    }
  }

  /// Check if any filters are active
  bool get hasActiveFilters {
    return currentStatusFilter != null ||
           (currentSearchKeyword != null && currentSearchKeyword!.isNotEmpty) ||
           currentFromDate != null ||
           currentToDate != null;
  }

  /// Get orders count by status
  Map<int, int> get orderCountByStatus {
    final Map<int, int> counts = {};
    for (final order in orders) {
      counts[order.status] = (counts[order.status] ?? 0) + 1;
    }
    return counts;
  }

  /// Get active orders count (status 0, 1, 2)
  int get activeOrdersCount {
    return orders.where((order) => order.isActive).length;
  }

  /// Get completed orders count (status 3)
  int get completedOrdersCount {
    return orders.where((order) => order.isCompleted).length;
  }

  /// Get cancelled orders count (status 4)
  int get cancelledOrdersCount {
    return orders.where((order) => order.isCancelled).length;
  }
}

/// State khi có lỗi
class OrdersError extends OrdersState {
  final String message;
  final List<OrderEntity>? previousOrders;

  const OrdersError({
    required this.message,
    this.previousOrders,
  });

  @override
  List<Object?> get props => [message, previousOrders];
}

/// State khi đang tạo đơn hàng
class OrderCreating extends OrdersState {
  const OrderCreating();
}

/// State khi tạo đơn hàng thành công
class OrderCreated extends OrdersState {
  final OrderEntity order;

  const OrderCreated({required this.order});

  @override
  List<Object?> get props => [order];
}

/// State khi tạo đơn hàng thất bại
class OrderCreateError extends OrdersState {
  final String message;

  const OrderCreateError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// State khi đang cập nhật trạng thái đơn hàng
class OrderStatusUpdating extends OrdersState {
  final String orderId;

  const OrderStatusUpdating({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

/// State khi cập nhật trạng thái thành công
class OrderStatusUpdated extends OrdersState {
  final String orderId;
  final int newStatus;

  const OrderStatusUpdated({
    required this.orderId,
    required this.newStatus,
  });

  @override
  List<Object?> get props => [orderId, newStatus];
}

/// State khi cập nhật trạng thái thất bại
class OrderStatusUpdateError extends OrdersState {
  final String message;
  final String orderId;

  const OrderStatusUpdateError({
    required this.message,
    required this.orderId,
  });

  @override
  List<Object?> get props => [message, orderId];
}
