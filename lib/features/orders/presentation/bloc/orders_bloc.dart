import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/get_orders_with_table_info_usecase.dart';
import '../../domain/usecases/get_orders_by_table_usecase.dart';
import '../../domain/usecases/create_order_with_table_usecase.dart';
import '../../domain/usecases/update_order_status_usecase.dart';
import '../../domain/entities/order_entity.dart';
import 'orders_event.dart';
import 'orders_state.dart';

/// BLoC cho Orders feature
class OrdersBloc extends Bloc<OrdersEvent, OrdersState> {
  final GetOrdersWithTableInfoUseCase getOrdersWithTableInfoUseCase;
  final GetOrdersByTableUseCase getOrdersByTableUseCase;
  final CreateOrderWithTableUseCase createOrderWithTableUseCase;
  final UpdateOrderStatusUseCase updateOrderStatusUseCase;

  OrdersBloc({
    required this.getOrdersWithTableInfoUseCase,
    required this.getOrdersByTableUseCase,
    required this.createOrderWithTableUseCase,
    required this.updateOrderStatusUseCase,
  }) : super(const OrdersInitial()) {
    on<LoadOrdersWithTableInfo>(_onLoadOrdersWithTableInfo);
    on<LoadOrdersByTable>(_onLoadOrdersByTable);
    on<CreateOrderWithTable>(_onCreateOrderWithTable);
    on<UpdateOrderStatus>(_onUpdateOrderStatus);
    on<FilterOrdersByStatus>(_onFilterOrdersByStatus);
    on<SearchOrders>(_onSearchOrders);
    on<SelectOrder>(_onSelectOrder);
    on<ClearFilters>(_onClearFilters);
    on<RefreshOrders>(_onRefreshOrders);
  }

  /// Handler cho LoadOrdersWithTableInfo event
  Future<void> _onLoadOrdersWithTableInfo(
    LoadOrdersWithTableInfo event,
    Emitter<OrdersState> emit,
  ) async {
    if (event.isRefresh && state is OrdersLoaded) {
      emit(OrdersRefreshing(currentOrders: (state as OrdersLoaded).orders));
    } else {
      emit(const OrdersLoading());
    }

    final result = await getOrdersWithTableInfoUseCase(
      GetOrdersWithTableInfoParams(
        storeId: event.storeId,
        statuses: event.statuses,
        page: event.page,
        limit: event.limit,
      ),
    );

    result.fold(
      (failure) => emit(OrdersError(
        message: failure.message,
        previousOrders: state is OrdersLoaded ? (state as OrdersLoaded).orders : null,
      )),
      (orders) {
        emit(OrdersLoaded(
          orders: orders,
          filteredOrders: orders,
          currentPage: event.page,
          hasReachedMax: orders.length < event.limit,
        ));
      },
    );
  }

  /// Handler cho LoadOrdersByTable event
  Future<void> _onLoadOrdersByTable(
    LoadOrdersByTable event,
    Emitter<OrdersState> emit,
  ) async {
    emit(const OrdersLoading());

    final result = await getOrdersByTableUseCase(
      GetOrdersByTableParams(
        areaId: event.areaId,
        tableId: event.tableId,
        storeId: event.storeId,
        statuses: event.statuses,
      ),
    );

    result.fold(
      (failure) => emit(OrdersError(message: failure.message)),
      (orders) {
        emit(OrdersLoaded(
          orders: orders,
          filteredOrders: orders,
        ));
      },
    );
  }

  /// Handler cho CreateOrderWithTable event
  Future<void> _onCreateOrderWithTable(
    CreateOrderWithTable event,
    Emitter<OrdersState> emit,
  ) async {
    emit(const OrderCreating());

    final result = await createOrderWithTableUseCase(
      CreateOrderWithTableParams(
        storeId: event.storeId,
        areaId: event.areaId,
        tableId: event.tableId,
        serviceType: event.serviceType,
        products: event.products,
        customerName: event.customerName,
        phone: event.phone,
        totalAmount: event.totalAmount,
        email: event.email,
        subtotal: event.subtotal,
        discountAmount: event.discountAmount,
        coupon: event.coupon,
        note: event.note,
        paymentMethod: event.paymentMethod,
        bankCode: event.bankCode,
        numberOfGuests: event.numberOfGuests,
        estimatedDuration: event.estimatedDuration,
        specialRequests: event.specialRequests,
      ),
    );

    result.fold(
      (failure) => emit(OrderCreateError(message: failure.message)),
      (order) => emit(OrderCreated(order: order)),
    );
  }

  /// Handler cho UpdateOrderStatus event
  Future<void> _onUpdateOrderStatus(
    UpdateOrderStatus event,
    Emitter<OrdersState> emit,
  ) async {
    emit(OrderStatusUpdating(orderId: event.orderId));

    final result = await updateOrderStatusUseCase(
      UpdateOrderStatusParams(
        orderId: event.orderId,
        newStatus: event.newStatus,
        storeId: event.storeId,
      ),
    );

    result.fold(
      (failure) => emit(OrderStatusUpdateError(
        message: failure.message,
        orderId: event.orderId,
      )),
      (success) {
        if (success) {
          emit(OrderStatusUpdated(
            orderId: event.orderId,
            newStatus: event.newStatus,
          ));
          
          // Refresh orders after successful update
          add(RefreshOrders(storeId: event.storeId));
        } else {
          emit(OrderStatusUpdateError(
            message: 'Failed to update order status',
            orderId: event.orderId,
          ));
        }
      },
    );
  }

  /// Handler cho FilterOrdersByStatus event
  void _onFilterOrdersByStatus(
    FilterOrdersByStatus event,
    Emitter<OrdersState> emit,
  ) {
    if (state is OrdersLoaded) {
      final currentState = state as OrdersLoaded;
      final filteredOrders = _filterOrders(
        currentState.orders,
        statusFilter: event.statuses,
        searchKeyword: currentState.currentSearchKeyword,
        fromDate: currentState.currentFromDate,
        toDate: currentState.currentToDate,
      );

      emit(currentState.copyWith(
        filteredOrders: filteredOrders,
        currentStatusFilter: event.statuses,
      ));
    }
  }

  /// Handler cho SearchOrders event
  void _onSearchOrders(
    SearchOrders event,
    Emitter<OrdersState> emit,
  ) {
    if (state is OrdersLoaded) {
      final currentState = state as OrdersLoaded;
      final filteredOrders = _filterOrders(
        currentState.orders,
        statusFilter: currentState.currentStatusFilter,
        searchKeyword: event.keyword,
        fromDate: event.fromDate,
        toDate: event.toDate,
      );

      emit(currentState.copyWith(
        filteredOrders: filteredOrders,
        currentSearchKeyword: event.keyword,
        currentFromDate: event.fromDate,
        currentToDate: event.toDate,
      ));
    }
  }

  /// Handler cho SelectOrder event
  void _onSelectOrder(
    SelectOrder event,
    Emitter<OrdersState> emit,
  ) {
    if (state is OrdersLoaded) {
      final currentState = state as OrdersLoaded;
      emit(currentState.copyWith(selectedOrderId: event.orderId));
    }
  }

  /// Handler cho ClearFilters event
  void _onClearFilters(
    ClearFilters event,
    Emitter<OrdersState> emit,
  ) {
    if (state is OrdersLoaded) {
      final currentState = state as OrdersLoaded;
      emit(currentState.copyWith(
        filteredOrders: currentState.orders,
        clearStatusFilter: true,
        clearSearchKeyword: true,
        clearFromDate: true,
        clearToDate: true,
      ));
    }
  }

  /// Handler cho RefreshOrders event
  void _onRefreshOrders(
    RefreshOrders event,
    Emitter<OrdersState> emit,
  ) {
    add(LoadOrdersWithTableInfo(
      storeId: event.storeId,
      isRefresh: true,
    ));
  }

  /// Helper method để filter orders
  List<OrderEntity> _filterOrders(
    List<OrderEntity> orders, {
    List<int>? statusFilter,
    String? searchKeyword,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    var filtered = orders;

    // Filter by status
    if (statusFilter != null && statusFilter.isNotEmpty) {
      filtered = filtered.where((order) => statusFilter.contains(order.status)).toList();
    }

    // Filter by search keyword
    if (searchKeyword != null && searchKeyword.isNotEmpty) {
      final keyword = searchKeyword.toLowerCase();
      filtered = filtered.where((order) {
        return order.orderId.toLowerCase().contains(keyword) ||
               order.customerName.toLowerCase().contains(keyword) ||
               order.phone.contains(keyword) ||
               (order.tableInfo?.tableName.toLowerCase().contains(keyword) ?? false);
      }).toList();
    }

    // Filter by date range
    if (fromDate != null) {
      filtered = filtered.where((order) => order.orderTime.isAfter(fromDate)).toList();
    }
    if (toDate != null) {
      filtered = filtered.where((order) => order.orderTime.isBefore(toDate)).toList();
    }

    return filtered;
  }
}
