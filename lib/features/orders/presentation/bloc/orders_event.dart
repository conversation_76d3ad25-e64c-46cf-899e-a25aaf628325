import 'package:equatable/equatable.dart';

/// Base class cho tất cả Orders events
abstract class OrdersEvent extends Equatable {
  const OrdersEvent();

  @override
  List<Object?> get props => [];
}

/// Event để load danh sách đơn hàng với thông tin bàn ăn
class LoadOrdersWithTableInfo extends OrdersEvent {
  final String storeId;
  final List<int>? statuses;
  final int page;
  final int limit;
  final bool isRefresh;

  const LoadOrdersWithTableInfo({
    required this.storeId,
    this.statuses,
    this.page = 1,
    this.limit = 20,
    this.isRefresh = false,
  });

  @override
  List<Object?> get props => [storeId, statuses, page, limit, isRefresh];
}

/// Event để load đơn hàng theo bàn ăn cụ thể
class LoadOrdersByTable extends OrdersEvent {
  final String areaId;
  final String tableId;
  final String storeId;
  final List<int>? statuses;

  const LoadOrdersByTable({
    required this.areaId,
    required this.tableId,
    required this.storeId,
    this.statuses,
  });

  @override
  List<Object?> get props => [areaId, tableId, storeId, statuses];
}

/// Event để tạo đơn hàng với thông tin bàn ăn
class CreateOrderWithTable extends OrdersEvent {
  final String storeId;
  final String areaId;
  final String tableId;
  final String serviceType;
  final List<Map<String, dynamic>> products;
  final String customerName;
  final String phone;
  final double totalAmount;
  final String? email;
  final double? subtotal;
  final double? discountAmount;
  final String? coupon;
  final String? note;
  final int? paymentMethod;
  final String? bankCode;
  final int? numberOfGuests;
  final int? estimatedDuration;
  final String? specialRequests;

  const CreateOrderWithTable({
    required this.storeId,
    required this.areaId,
    required this.tableId,
    required this.serviceType,
    required this.products,
    required this.customerName,
    required this.phone,
    required this.totalAmount,
    this.email,
    this.subtotal,
    this.discountAmount,
    this.coupon,
    this.note,
    this.paymentMethod,
    this.bankCode,
    this.numberOfGuests,
    this.estimatedDuration,
    this.specialRequests,
  });

  @override
  List<Object?> get props => [
        storeId,
        areaId,
        tableId,
        serviceType,
        products,
        customerName,
        phone,
        totalAmount,
        email,
        subtotal,
        discountAmount,
        coupon,
        note,
        paymentMethod,
        bankCode,
        numberOfGuests,
        estimatedDuration,
        specialRequests,
      ];
}

/// Event để cập nhật trạng thái đơn hàng
class UpdateOrderStatus extends OrdersEvent {
  final String orderId;
  final int newStatus;
  final String storeId;

  const UpdateOrderStatus({
    required this.orderId,
    required this.newStatus,
    required this.storeId,
  });

  @override
  List<Object?> get props => [orderId, newStatus, storeId];
}

/// Event để filter đơn hàng theo trạng thái
class FilterOrdersByStatus extends OrdersEvent {
  final String storeId;
  final List<int> statuses;

  const FilterOrdersByStatus({
    required this.storeId,
    required this.statuses,
  });

  @override
  List<Object?> get props => [storeId, statuses];
}

/// Event để search đơn hàng
class SearchOrders extends OrdersEvent {
  final String storeId;
  final String? keyword;
  final List<int>? statuses;
  final DateTime? fromDate;
  final DateTime? toDate;

  const SearchOrders({
    required this.storeId,
    this.keyword,
    this.statuses,
    this.fromDate,
    this.toDate,
  });

  @override
  List<Object?> get props => [storeId, keyword, statuses, fromDate, toDate];
}

/// Event để select/deselect đơn hàng
class SelectOrder extends OrdersEvent {
  final String? orderId;

  const SelectOrder({this.orderId});

  @override
  List<Object?> get props => [orderId];
}

/// Event để clear tất cả filters
class ClearFilters extends OrdersEvent {
  const ClearFilters();
}

/// Event để refresh danh sách đơn hàng
class RefreshOrders extends OrdersEvent {
  final String storeId;

  const RefreshOrders({required this.storeId});

  @override
  List<Object?> get props => [storeId];
}
