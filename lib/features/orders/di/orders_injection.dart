import 'package:get_it/get_it.dart';
import '../domain/repositories/orders_repository.dart';
import '../data/repositories/orders_repository_impl.dart';
import '../data/datasources/orders_remote_data_source.dart';
import '../domain/usecases/get_orders_with_table_info_usecase.dart';
import '../domain/usecases/get_orders_by_table_usecase.dart';
import '../domain/usecases/create_order_with_table_usecase.dart';
import '../domain/usecases/update_order_status_usecase.dart';
import '../presentation/bloc/orders_bloc.dart';

final sl = GetIt.instance;

/// Khởi tạo dependency injection cho Orders feature
void initOrdersInjection() {
  // Bloc
  sl.registerFactory(() => OrdersBloc(
        getOrdersWithTableInfoUseCase: sl(),
        getOrdersByTableUseCase: sl(),
        createOrderWithTableUseCase: sl(),
        updateOrderStatusUseCase: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => GetOrdersWithTableInfoUseCase(sl()));
  sl.registerLazySingleton(() => GetOrdersByTableUseCase(sl()));
  sl.registerLazySingleton(() => CreateOrderWithTableUseCase(sl()));
  sl.registerLazySingleton(() => UpdateOrderStatusUseCase(sl()));

  // Repository
  sl.registerLazySingleton<OrdersRepository>(
    () => OrdersRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<OrdersRemoteDataSource>(
    () => OrdersRemoteDataSourceImpl(
      apiClient: sl(),
    ),
  );
}
