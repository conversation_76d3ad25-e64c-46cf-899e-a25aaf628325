import 'package:equatable/equatable.dart';

/// Entity cho Order trong domain layer
class OrderEntity extends Equatable {
  final String id;
  final String orderId;
  final String paymentId;
  final int code;
  final String serviceType;
  final String userId;
  final String storeId;
  final String customerName;
  final String phone;
  final String email;
  final List<OrderItemEntity> products;
  final double subtotal;
  final double discountAmount;
  final double totalAmount;
  final double transportFee;
  final int status;
  final int paymentMethod;
  final String bankCode;
  final int isPayOnline;
  final String note;
  final String coupon;
  final DateTime orderTime;
  final DateTime? completedTime;
  final OrderTableInfoEntity? tableInfo;

  const OrderEntity({
    required this.id,
    required this.orderId,
    required this.paymentId,
    required this.code,
    required this.serviceType,
    required this.userId,
    required this.storeId,
    required this.customerName,
    required this.phone,
    required this.email,
    required this.products,
    required this.subtotal,
    required this.discountAmount,
    required this.totalAmount,
    required this.transportFee,
    required this.status,
    required this.paymentMethod,
    required this.bankCode,
    required this.isPayOnline,
    required this.note,
    required this.coupon,
    required this.orderTime,
    this.completedTime,
    this.tableInfo,
  });

  @override
  List<Object?> get props => [
        id,
        orderId,
        paymentId,
        code,
        serviceType,
        userId,
        storeId,
        customerName,
        phone,
        email,
        products,
        subtotal,
        discountAmount,
        totalAmount,
        transportFee,
        status,
        paymentMethod,
        bankCode,
        isPayOnline,
        note,
        coupon,
        orderTime,
        completedTime,
        tableInfo,
      ];

  /// Chuyển đổi status number sang text
  String get statusText {
    switch (status) {
      case 0:
        return 'Chờ xác nhận';
      case 1:
        return 'Đang xử lý';
      case 2:
        return 'Đang giao/Sẵn sàng';
      case 3:
        return 'Hoàn thành';
      case 4:
        return 'Hủy';
      case 5:
        return 'Trả hàng';
      default:
        return 'Không xác định';
    }
  }

  /// Kiểm tra đơn hàng có active không (chưa hoàn thành/hủy)
  bool get isActive => status >= 0 && status <= 2;

  /// Kiểm tra đơn hàng có hoàn thành không
  bool get isCompleted => status == 3;

  /// Kiểm tra đơn hàng có bị hủy không
  bool get isCancelled => status == 4;

  /// Copy with method
  OrderEntity copyWith({
    String? id,
    String? orderId,
    String? paymentId,
    int? code,
    String? serviceType,
    String? userId,
    String? storeId,
    String? customerName,
    String? phone,
    String? email,
    List<OrderItemEntity>? products,
    double? subtotal,
    double? discountAmount,
    double? totalAmount,
    double? transportFee,
    int? status,
    int? paymentMethod,
    String? bankCode,
    int? isPayOnline,
    String? note,
    String? coupon,
    DateTime? orderTime,
    DateTime? completedTime,
    OrderTableInfoEntity? tableInfo,
  }) {
    return OrderEntity(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      paymentId: paymentId ?? this.paymentId,
      code: code ?? this.code,
      serviceType: serviceType ?? this.serviceType,
      userId: userId ?? this.userId,
      storeId: storeId ?? this.storeId,
      customerName: customerName ?? this.customerName,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      products: products ?? this.products,
      subtotal: subtotal ?? this.subtotal,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      transportFee: transportFee ?? this.transportFee,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      bankCode: bankCode ?? this.bankCode,
      isPayOnline: isPayOnline ?? this.isPayOnline,
      note: note ?? this.note,
      coupon: coupon ?? this.coupon,
      orderTime: orderTime ?? this.orderTime,
      completedTime: completedTime ?? this.completedTime,
      tableInfo: tableInfo ?? this.tableInfo,
    );
  }
}

/// Entity cho Order Item
class OrderItemEntity extends Equatable {
  final String productId;
  final String productName;
  final double productPrice;
  final String thumbnail;
  final int count;
  final double price;
  final String noteProduct;
  final String weight;
  final List<ClassifyActiveEntity> classifyActive;

  const OrderItemEntity({
    required this.productId,
    required this.productName,
    required this.productPrice,
    required this.thumbnail,
    required this.count,
    required this.price,
    required this.noteProduct,
    required this.weight,
    required this.classifyActive,
  });

  @override
  List<Object?> get props => [
        productId,
        productName,
        productPrice,
        thumbnail,
        count,
        price,
        noteProduct,
        weight,
        classifyActive,
      ];
}

/// Entity cho Classify Active (Size, topping, options)
class ClassifyActiveEntity extends Equatable {
  final String id;
  final String name;
  final String value;
  final String price;
  final String mass;

  const ClassifyActiveEntity({
    required this.id,
    required this.name,
    required this.value,
    required this.price,
    required this.mass,
  });

  @override
  List<Object?> get props => [id, name, value, price, mass];
}

/// Entity cho Table Info
class OrderTableInfoEntity extends Equatable {
  final String areaId;
  final String tableId;
  final String tableName;
  final String areaName;
  final int capacity;
  final DateTime reservedAt;
  final int estimatedDuration;
  final DateTime? actualStartTime;
  final DateTime? actualEndTime;
  final String tableStatus;
  final int numberOfGuests;
  final String specialRequests;

  const OrderTableInfoEntity({
    required this.areaId,
    required this.tableId,
    required this.tableName,
    required this.areaName,
    required this.capacity,
    required this.reservedAt,
    required this.estimatedDuration,
    this.actualStartTime,
    this.actualEndTime,
    required this.tableStatus,
    required this.numberOfGuests,
    required this.specialRequests,
  });

  @override
  List<Object?> get props => [
        areaId,
        tableId,
        tableName,
        areaName,
        capacity,
        reservedAt,
        estimatedDuration,
        actualStartTime,
        actualEndTime,
        tableStatus,
        numberOfGuests,
        specialRequests,
      ];
}
