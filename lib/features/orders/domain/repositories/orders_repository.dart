import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/order_entity.dart';

/// Repository interface cho Orders feature
abstract class OrdersRepository {
  /// L<PERSON>y danh sách đơn hàng với thông tin bàn ăn
  Future<Either<Failure, List<OrderEntity>>> getOrdersWithTableInfo({
    required String storeId,
    List<int>? statuses,
    int page = 1,
    int limit = 20,
  });

  /// Lấy đơn hàng theo bàn ăn cụ thể
  Future<Either<Failure, List<OrderEntity>>> getOrdersByTable({
    required String areaId,
    required String tableId,
    required String storeId,
    List<int>? statuses,
  });

  /// Lấy đơn hàng hiện tại của bàn (đơn hàng active)
  Future<Either<Failure, OrderEntity?>> getCurrentOrderByTable({
    required String areaId,
    required String tableId,
    required String storeId,
  });

  /// Tạo đơn hàng với thông tin bàn ăn
  Future<Either<Failure, OrderEntity>> createOrderWithTable({
    required String storeId,
    required String areaId,
    required String tableId,
    required String serviceType,
    required List<Map<String, dynamic>> products,
    required String customerName,
    required String phone,
    required double totalAmount,
    String? email,
    double? subtotal,
    double? discountAmount,
    String? coupon,
    String? note,
    int? paymentMethod,
    String? bankCode,
    int? numberOfGuests,
    int? estimatedDuration,
    String? specialRequests,
  });

  /// Cập nhật trạng thái đơn hàng và bàn ăn
  Future<Either<Failure, bool>> updateOrderAndTableStatus({
    required String orderId,
    required int newStatus,
    required String storeId,
  });

  /// Lấy thống kê đơn hàng theo bàn ăn
  Future<Either<Failure, Map<String, dynamic>>> getTableOrderStats({
    required String storeId,
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Lấy chi tiết đơn hàng theo ID
  Future<Either<Failure, OrderEntity>> getOrderById({
    required String orderId,
  });

  /// Hủy đơn hàng
  Future<Either<Failure, bool>> cancelOrder({
    required String orderId,
    required String storeId,
    String? reason,
  });

  /// Lấy danh sách đơn hàng theo trạng thái
  Future<Either<Failure, List<OrderEntity>>> getOrdersByStatus({
    required String storeId,
    required List<int> statuses,
    int page = 1,
    int limit = 20,
  });

  /// Tìm kiếm đơn hàng
  Future<Either<Failure, List<OrderEntity>>> searchOrders({
    required String storeId,
    String? keyword,
    List<int>? statuses,
    DateTime? fromDate,
    DateTime? toDate,
    int page = 1,
    int limit = 20,
  });
}
