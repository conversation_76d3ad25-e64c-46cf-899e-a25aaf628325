import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/order_entity.dart';
import '../repositories/orders_repository.dart';

/// UseCase để lấy đơn hàng theo bàn ăn cụ thể
class GetOrdersByTableUseCase implements UseCase<List<OrderEntity>, GetOrdersByTableParams> {
  final OrdersRepository repository;

  GetOrdersByTableUseCase(this.repository);

  @override
  Future<Either<Failure, List<OrderEntity>>> call(GetOrdersByTableParams params) async {
    return await repository.getOrdersByTable(
      areaId: params.areaId,
      tableId: params.tableId,
      storeId: params.storeId,
      statuses: params.statuses,
    );
  }
}

/// Parameters cho GetOrdersByTableUseCase
class GetOrdersByTableParams {
  final String areaId;
  final String tableId;
  final String storeId;
  final List<int>? statuses;

  const GetOrdersByTableParams({
    required this.areaId,
    required this.tableId,
    required this.storeId,
    this.statuses,
  });
}
