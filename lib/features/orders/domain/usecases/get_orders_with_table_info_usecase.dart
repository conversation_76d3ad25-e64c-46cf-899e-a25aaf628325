import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/order_entity.dart';
import '../repositories/orders_repository.dart';

/// UseCase để lấy danh sách đơn hàng với thông tin bàn ăn
class GetOrdersWithTableInfoUseCase implements UseCase<List<OrderEntity>, GetOrdersWithTableInfoParams> {
  final OrdersRepository repository;

  GetOrdersWithTableInfoUseCase(this.repository);

  @override
  Future<Either<Failure, List<OrderEntity>>> call(GetOrdersWithTableInfoParams params) async {
    return await repository.getOrdersWithTableInfo(
      storeId: params.storeId,
      statuses: params.statuses,
      page: params.page,
      limit: params.limit,
    );
  }
}

/// Parameters cho GetOrdersWithTableInfoUseCase
class GetOrdersWithTableInfoParams {
  final String storeId;
  final List<int>? statuses;
  final int page;
  final int limit;

  const GetOrdersWithTableInfoParams({
    required this.storeId,
    this.statuses,
    this.page = 1,
    this.limit = 20,
  });
}
