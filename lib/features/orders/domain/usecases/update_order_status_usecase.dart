import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/orders_repository.dart';

/// UseCase để cập nhật trạng thái đơn hàng và bàn ăn
class UpdateOrderStatusUseCase implements UseCase<bool, UpdateOrderStatusParams> {
  final OrdersRepository repository;

  UpdateOrderStatusUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(UpdateOrderStatusParams params) async {
    return await repository.updateOrderAndTableStatus(
      orderId: params.orderId,
      newStatus: params.newStatus,
      storeId: params.storeId,
    );
  }
}

/// Parameters cho UpdateOrderStatusUseCase
class UpdateOrderStatusParams {
  final String orderId;
  final int newStatus;
  final String storeId;

  const UpdateOrderStatusParams({
    required this.orderId,
    required this.newStatus,
    required this.storeId,
  });
}
