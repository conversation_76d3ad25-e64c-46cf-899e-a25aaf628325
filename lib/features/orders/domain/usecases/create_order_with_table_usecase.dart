import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/order_entity.dart';
import '../repositories/orders_repository.dart';

/// UseCase để tạo đơn hàng với thông tin bàn ăn
class CreateOrderWithTableUseCase implements UseCase<OrderEntity, CreateOrderWithTableParams> {
  final OrdersRepository repository;

  CreateOrderWithTableUseCase(this.repository);

  @override
  Future<Either<Failure, OrderEntity>> call(CreateOrderWithTableParams params) async {
    // Validate business rules trước khi tạo đơn hàng
    final validationResult = _validateOrderData(params);
    if (validationResult != null) {
      return Left(ValidationFailure(validationResult));
    }

    return await repository.createOrderWithTable(
      storeId: params.storeId,
      areaId: params.areaId,
      tableId: params.tableId,
      serviceType: params.serviceType,
      products: params.products,
      customerName: params.customerName,
      phone: params.phone,
      totalAmount: params.totalAmount,
      email: params.email,
      subtotal: params.subtotal,
      discountAmount: params.discountAmount,
      coupon: params.coupon,
      note: params.note,
      paymentMethod: params.paymentMethod,
      bankCode: params.bankCode,
      numberOfGuests: params.numberOfGuests,
      estimatedDuration: params.estimatedDuration,
      specialRequests: params.specialRequests,
    );
  }

  /// Validate dữ liệu đơn hàng
  String? _validateOrderData(CreateOrderWithTableParams params) {
    // Validate required fields
    if (params.storeId.isEmpty) {
      return 'ID cửa hàng không được để trống';
    }
    
    if (params.areaId.isEmpty) {
      return 'ID khu vực không được để trống';
    }
    
    if (params.tableId.isEmpty) {
      return 'ID bàn không được để trống';
    }
    
    if (params.customerName.trim().isEmpty) {
      return 'Tên khách hàng không được để trống';
    }
    
    if (params.phone.trim().isEmpty) {
      return 'Số điện thoại không được để trống';
    }
    
    // Validate phone number format
    if (!_isValidPhoneNumber(params.phone)) {
      return 'Số điện thoại không hợp lệ';
    }
    
    if (params.products.isEmpty) {
      return 'Đơn hàng phải có ít nhất 1 sản phẩm';
    }
    
    if (params.totalAmount <= 0) {
      return 'Tổng tiền phải lớn hơn 0';
    }
    
    // Validate numberOfGuests
    if (params.numberOfGuests != null && params.numberOfGuests! <= 0) {
      return 'Số khách phải lớn hơn 0';
    }
    
    // Validate estimatedDuration
    if (params.estimatedDuration != null && params.estimatedDuration! <= 0) {
      return 'Thời gian dự kiến phải lớn hơn 0';
    }

    return null; // No validation errors
  }

  /// Kiểm tra định dạng số điện thoại
  bool _isValidPhoneNumber(String phone) {
    // Remove all non-digit characters
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check if it's a valid Vietnamese phone number
    // Vietnamese phone numbers: 10-11 digits, starting with 0
    if (cleanPhone.length < 10 || cleanPhone.length > 11) {
      return false;
    }
    
    if (!cleanPhone.startsWith('0')) {
      return false;
    }
    
    return true;
  }
}

/// Parameters cho CreateOrderWithTableUseCase
class CreateOrderWithTableParams {
  final String storeId;
  final String areaId;
  final String tableId;
  final String serviceType;
  final List<Map<String, dynamic>> products;
  final String customerName;
  final String phone;
  final double totalAmount;
  final String? email;
  final double? subtotal;
  final double? discountAmount;
  final String? coupon;
  final String? note;
  final int? paymentMethod;
  final String? bankCode;
  final int? numberOfGuests;
  final int? estimatedDuration;
  final String? specialRequests;

  const CreateOrderWithTableParams({
    required this.storeId,
    required this.areaId,
    required this.tableId,
    required this.serviceType,
    required this.products,
    required this.customerName,
    required this.phone,
    required this.totalAmount,
    this.email,
    this.subtotal,
    this.discountAmount,
    this.coupon,
    this.note,
    this.paymentMethod,
    this.bankCode,
    this.numberOfGuests,
    this.estimatedDuration,
    this.specialRequests,
  });
}

/// Custom Failure cho validation errors
class ValidationFailure extends Failure {
  const ValidationFailure(String message) : super(message: message);
}
