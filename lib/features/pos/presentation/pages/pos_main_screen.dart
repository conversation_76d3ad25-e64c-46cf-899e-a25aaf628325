import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../menu/presentation/pages/menu_page.dart';
import '../../../orders/presentation/pages/orders_page.dart';
import '../../../tables/presentation/pages/tables_page.dart';
import '../../../payment/presentation/pages/payment_page.dart';
import '../../../reservations/presentation/pages/reservations_page.dart';
import '../../../buffet/presentation/pages/buffet_page.dart';
import '../../../cho_hai_san/presentation/pages/cho_hai_san_page.dart';
import '../../../cho_hai_san/presentation/bloc/cho_hai_san_bloc.dart';
import '../../../invoice/presentation/pages/invoice_page.dart';
import '../../../invoice/presentation/bloc/invoice_bloc.dart';
import '../../../bar_kitchen/presentation/pages/bar_kitchen_page.dart';
import '../../../bar_kitchen/presentation/bloc/bar_kitchen_bloc.dart';

import '../../../do_uong/presentation/pages/do_uong_page.dart';
import '../../../do_uong/presentation/bloc/do_uong_bloc.dart';
import '../../../ticket/presentation/pages/ticket_page.dart';
import '../../../ticket/presentation/bloc/ticket_bloc.dart';
import '../../../ticket/presentation/bloc/cart_bloc.dart' as ticket_cart;
import '../../../menu/presentation/bloc/cart_bloc.dart' as menu_cart;
import '../../../payment/presentation/bloc/payment_bloc.dart';
import '../../../exit/presentation/pages/exit_page.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../auth/presentation/bloc/auth_event.dart';
import '../../../../core/di/injection_container.dart' as di;
import '../../../../core/widgets/custom_pos_tab_bar.dart';
import '../../../../core/theme/pos_colors.dart';
import '../../../../core/services/pos_navigation_service.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';

final GlobalKey<PosMainScreenState> posMainScreenKey =
    GlobalKey<PosMainScreenState>();

/// Main POS Screen with Top Tab Navigation
/// Similar to main.dart structure but with top tabs instead of bottom tabs
class PosMainScreen extends StatefulWidget {
  const PosMainScreen({super.key});

  @override
  State<PosMainScreen> createState() => PosMainScreenState();
}

class PosMainScreenState extends State<PosMainScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedTableName;

  // Persistent CartBlocs for each feature
  late menu_cart.CartBloc _menuCartBloc;
  late ticket_cart.CartBloc _ticketCartBloc;

  void switchToMenuTab(String tableName) {
    setState(() {
      _selectedTableName = tableName;
      _tabController.animateTo(
          1); // Index 1 is for the Menu tab (after commenting out Order)
    });
  }

  void switchToTablesTab() {
    _tabController.animateTo(0); // Index 0 is for the Tables tab
  }

  void switchToPaymentTab() {
    _tabController.animateTo(2); // Index 2 is for the Payment tab
  }

  final List<Tab> _tabs = const [
    Tab(
      icon: Icon(Icons.table_restaurant),
      text: 'Bàn',
    ),
    // Tab(
    //   icon: Icon(Icons.shopping_cart),
    //   text: 'Order',
    // ),
    Tab(
      icon: Icon(Icons.restaurant_menu),
      text: 'Menu',
    ),
    // Tab(
    //   icon: Icon(Icons.restaurant),
    //   text: 'Buffet',
    // ),
    // Tab(
    //   icon: Icon(Icons.store),
    //   text: 'Siêu thị',
    // ),
    // Tab "Thanh toán" đã được ẩn khỏi navigation bar
    // nhưng vẫn accessible thông qua button trong màn Vé
    // Tab(
    //   icon: Icon(Icons.payment),
    //   text: 'Thanh toán',
    // ),
    Tab(
      icon: Icon(Icons.event_seat),
      text: 'Lịch hẹn',
    ),
    Tab(
      icon: Icon(Icons.receipt_long),
      text: 'Hoá đơn',
    ),
    Tab(
      icon: Icon(Icons.restaurant_outlined),
      text: 'Bar/Bếp',
    ),
    // Tab(
    //   icon: Icon(Icons.local_drink),
    //   text: 'Đồ uống',
    // ),
    Tab(
      icon: Icon(Icons.confirmation_number),
      text: 'Vé',
    ),
    Tab(
      icon: Icon(Icons.exit_to_app),
      text: 'Thoát',
    ),
  ];

  List<Widget> _getTabViews() {
    return [
      const TablesPage(), // Bàn
      // const OrdersPage(), // Order
      BlocProvider.value(
        value: _menuCartBloc,
        child: MenuPage(tableName: _selectedTableName),
      ), // Menu
      // const BuffetPage(), // Buffet
      // BlocProvider(
      //   create: (context) => di.sl<ChoHaiSanBloc>(),
      //   child: const ChoHaiSanPage(),
      // ), // Siêu thị
      MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: _ticketCartBloc, // Share the same CartBloc with TicketPage
          ),
          BlocProvider(
            create: (context) => PaymentBloc(
              ticketRepository: di.sl(),
              cartBloc: _ticketCartBloc, // Use the shared CartBloc instance
            ),
          ),
        ],
        child: const PaymentPage(),
      ), // Thanh toán
      const ReservationsPage(), // Lịch hẹn
      BlocProvider(
        create: (context) => di.sl<InvoiceBloc>(),
        child: const InvoicePage(),
      ), // Hoá đơn
      BlocProvider(
        create: (context) => di.sl<BarKitchenBloc>(),
        child: const BarKitchenPage(),
      ), // Bar/Bếp

      // BlocProvider(
      //   create: (context) => di.sl<DoUongBloc>(),
      //   child: const DoUongPage(),
      // ), // Đồ uống
      MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => di.sl<TicketBloc>(),
          ),
          BlocProvider.value(
            value: _ticketCartBloc,
          ),
        ],
        child: const TicketPage(),
      ), // Vé
      const ExitPage(), // Thoát
    ];
  }

  @override
  void initState() {
    super.initState();

    // Initialize persistent CartBlocs
    _menuCartBloc = menu_cart.CartBloc();
    _ticketCartBloc = ticket_cart.CartBloc();

    // TabController needs to match TabBarView children count (8)
    // even though TabBar only shows 7 tabs (Payment tab is hidden)
    _tabController = TabController(
      length: _getTabViews().length, // 8 tabs to match TabBarView
      vsync: this,
      initialIndex: 0,
    );
    _tabController.addListener(() {
      // Rebuild the widget to update the highlighted tab in CustomPosTabBar
      if (mounted) {
        setState(() {});
      }
    });

    // Register navigation callback
    PosNavigationService().registerNavigateToPaymentTab(switchToPaymentTab);
  }

  @override
  void dispose() {
    // Dispose persistent CartBlocs
    _menuCartBloc.close();
    _ticketCartBloc.close();

    _tabController.dispose();
    PosNavigationService().dispose();
    super.dispose();
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: PosColors.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.logout,
                color: PosColors.primary,
                size: 28,
              ),
              const SizedBox(width: PosSpacing.sm),
              Text(
                'Xác nhận đăng xuất',
                style: PosTypography.headingSmall.copyWith(
                  color: PosColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Text(
            'Bạn có chắc chắn muốn đăng xuất khỏi tài khoản hiện tại không?\n\nSau khi đăng xuất, bạn sẽ được chuyển về màn hình đăng nhập.',
            style: PosTypography.bodyMedium.copyWith(
              color: PosColors.textSecondary,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Hủy',
                style: PosTypography.bodyMedium.copyWith(
                  color: PosColors.textSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _logout();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: PosColors.primary,
                foregroundColor: PosColors.textOnDark,
              ),
              child: const Text('Đăng xuất'),
            ),
          ],
        );
      },
    );
  }

  void _logout() {
    // Hiển thị loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: PosColors.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: PosSpacing.md),
              Text(
                'Đang đăng xuất...',
                style: PosTypography.bodyMedium.copyWith(
                  color: PosColors.textPrimary,
                ),
              ),
            ],
          ),
        );
      },
    );

    try {
      // Đăng xuất bằng AuthBloc
      context.read<AuthBloc>().add(AuthLogoutRequested());

      // Đóng loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Hiển thị thông báo thành công
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đăng xuất thành công! Vui lòng đăng nhập lại.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }

      // Navigation sẽ được xử lý tự động bởi AuthenticationWrapper
      // khi AuthBloc emit AuthUnauthenticated state
    } catch (e) {
      // Đóng loading dialog nếu có lỗi
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Hiển thị thông báo lỗi
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi đăng xuất: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PosColors.background,
      body: Column(
        children: [
          // Custom tab bar without AppBar
          Container(
            color: PosColors.primary,
            child: SafeArea(
              bottom: false,
              child: CustomPosTabBar(
                tabController: _tabController,
                tabs: _tabs,
                onTap: (index) {
                  // Handle special case for Exit tab (index 6 in visible tabs)
                  if (index == 6) {
                    // Don't change tab, just trigger logout dialog
                    _showLogoutDialog();
                    return;
                  }

                  // Map visible tab index to actual TabBarView index
                  // Since Payment tab (index 2) is hidden, we need to adjust
                  int actualIndex = index;
                  if (index >= 2) {
                    // Skip the hidden Payment tab at index 2
                    actualIndex = index + 1;
                  }
                  _tabController.animateTo(actualIndex);
                },
              ),
            ),
          ),
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: _getTabViews(),
            ),
          ),
        ],
      ),
    );
  }
}
