import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../ticket/domain/repositories/ticket_repository.dart';
import '../../../ticket/presentation/bloc/cart_bloc.dart';
import '../../../../core/di/injection_container.dart' as sl;

part 'payment_event.dart';
part 'payment_state.dart';

/// BLoC quản lý state cho Payment feature
class PaymentBloc extends Bloc<PaymentEvent, PaymentState> {
  final TicketRepository ticketRepository;
  final CartBloc cartBloc;

  PaymentBloc({
    required this.ticketRepository,
    required this.cartBloc,
  }) : super(const PaymentState.initial()) {
    // Đăng ký event handlers
    on<CreatePaymentQRCode>(_onCreatePaymentQRCode);
    on<ResetPaymentState>(_onResetPaymentState);
    on<ClearPaymentData>(_onClearPaymentData);
  }

  /// Xử lý event tạo QR code thanh toán
  Future<void> _onCreatePaymentQRCode(
    CreatePaymentQRCode event,
    Emitter<PaymentState> emit,
  ) async {
    try {
      // Emit processing state
      emit(const PaymentState.processingPayment());

      // Lấy cart state hiện tại
      final cartState = cartBloc.state;

      print('🔍 PaymentBloc - Cart items count: ${cartState.items.length}');
      print('🔍 PaymentBloc - Cart total: ${cartState.totalPrice}');

      if (cartState.items.isEmpty) {
        print('❌ PaymentBloc - Cart is empty, emitting error');
        emit(PaymentState.error('Giỏ hàng trống'));
        return;
      }

      // Chuẩn bị dữ liệu products cho API
      final products = cartState.items
          .map((item) => {
                'id': item.product.id,
                'name': item.product.name,
                'price': item.product.price,
                'quantity': item.quantity,
                'thumbnail': item.product.thumbnail,
                'note': item.note ?? '',
              })
          .toList();

      // Tính tổng tiền cuối cùng (bao gồm thuế VAT và trừ khuyến mãi)
      final subtotal = cartState.totalPrice;
      final vat = subtotal * 0.1; // 10% VAT
      final discount = subtotal * 0.05; // 5% discount
      final totalAmount = subtotal + vat - discount;

      // Gọi API tạo ticket order
      final result = await ticketRepository.createTicketOrder(
        products: products,
        customerName: event.customerName,
        phone: event.phone,
        totalAmount: totalAmount,
        email: event.email,
        address: event.address,
        paymentMethod: event.paymentMethod,
        bankCode: event.bankCode,
        ticketType: event.ticketType,
        eventLocation: event.eventLocation,
      );

      result.fold(
        (failure) {
          emit(PaymentState.error('Lỗi tạo đơn hàng: ${failure.message}'));
        },
        (orderResponse) {
          try {
            // Tạo PaymentData từ response
            final paymentData = PaymentData.fromApiResponse(orderResponse);

            if (paymentData.hasPaymentMethod) {
              emit(PaymentState.success(
                paymentData: paymentData,
                message: 'Tạo đơn hàng thành công',
              ));
            } else {
              emit(PaymentState.error('Không có phương thức thanh toán'));
            }
          } catch (e) {
            emit(PaymentState.error('Lỗi xử lý dữ liệu thanh toán: $e'));
          }
        },
      );
    } catch (e) {
      emit(PaymentState.error('Lỗi không xác định: $e'));
    }
  }

  /// Xử lý event reset payment state
  void _onResetPaymentState(
    ResetPaymentState event,
    Emitter<PaymentState> emit,
  ) {
    emit(const PaymentState.initial());
  }

  /// Xử lý event clear payment data
  void _onClearPaymentData(
    ClearPaymentData event,
    Emitter<PaymentState> emit,
  ) {
    emit(state.copyWith(
      paymentData: null,
      successMessage: null,
      errorMessage: null,
    ));
  }
}
