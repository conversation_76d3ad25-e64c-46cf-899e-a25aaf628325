part of 'payment_bloc.dart';

/// State cho Payment feature
class PaymentState extends Equatable {
  final bool isLoading;
  final bool isProcessingPayment;
  final String? errorMessage;
  final PaymentData? paymentData;
  final String? successMessage;

  const PaymentState({
    this.isLoading = false,
    this.isProcessingPayment = false,
    this.errorMessage,
    this.paymentData,
    this.successMessage,
  });

  /// Initial state
  const PaymentState.initial() : this();

  /// Loading state
  const PaymentState.loading() : this(isLoading: true);

  /// Processing payment state
  const PaymentState.processingPayment() : this(isProcessingPayment: true);

  /// Success state với payment data
  PaymentState.success({
    required PaymentData paymentData,
    String? message,
  }) : this(
          paymentData: paymentData,
          successMessage: message,
        );

  /// Error state
  PaymentState.error(String message) : this(errorMessage: message);

  /// Copy with method
  PaymentState copyWith({
    bool? isLoading,
    bool? isProcessingPayment,
    String? errorMessage,
    PaymentData? paymentData,
    String? successMessage,
  }) {
    return PaymentState(
      isLoading: isLoading ?? this.isLoading,
      isProcessingPayment: isProcessingPayment ?? this.isProcessingPayment,
      errorMessage: errorMessage,
      paymentData: paymentData ?? this.paymentData,
      successMessage: successMessage,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        isProcessingPayment,
        errorMessage,
        paymentData,
        successMessage,
      ];
}

/// Model chứa dữ liệu payment response
class PaymentData extends Equatable {
  final String orderId;
  final String? qrCode;
  final String? paymentUrl;
  final double totalAmount;
  final Map<String, dynamic>? orderData;
  final Map<String, dynamic>? baoKimData;

  const PaymentData({
    required this.orderId,
    this.qrCode,
    this.paymentUrl,
    required this.totalAmount,
    this.orderData,
    this.baoKimData,
  });

  /// Tạo PaymentData từ API response
  factory PaymentData.fromApiResponse(Map<String, dynamic> response) {
    final data = response['data'];
    
    return PaymentData(
      orderId: data?['orderId']?.toString() ?? '',
      qrCode: data?['qrCode']?.toString(),
      paymentUrl: data?['paymentUrl']?.toString(),
      totalAmount: (data?['orderData']?['totalAmount'] ?? 0).toDouble(),
      orderData: data?['orderData'],
      baoKimData: data?['baoKimData'],
    );
  }

  /// Kiểm tra có QR code không
  bool get hasQrCode => qrCode != null && qrCode!.isNotEmpty;

  /// Kiểm tra có payment URL không
  bool get hasPaymentUrl => paymentUrl != null && paymentUrl!.isNotEmpty;

  /// Kiểm tra có phương thức thanh toán nào không
  bool get hasPaymentMethod => hasQrCode || hasPaymentUrl;

  @override
  List<Object?> get props => [
        orderId,
        qrCode,
        paymentUrl,
        totalAmount,
        orderData,
        baoKimData,
      ];
}
