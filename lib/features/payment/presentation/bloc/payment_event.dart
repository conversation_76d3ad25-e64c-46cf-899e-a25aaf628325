part of 'payment_bloc.dart';

/// Base class cho tất cả Payment events
abstract class PaymentEvent extends Equatable {
  const PaymentEvent();

  @override
  List<Object?> get props => [];
}

/// Event để tạo QR code thanh toán từ cart items
class CreatePaymentQRCode extends PaymentEvent {
  final String customerName;
  final String phone;
  final String? email;
  final String? address;
  final String? note;
  final int? paymentMethod;
  final String? bankCode;
  final String? ticketType;
  final String? eventLocation;

  const CreatePaymentQRCode({
    required this.customerName,
    required this.phone,
    this.email,
    this.address,
    this.note,
    this.paymentMethod = 1, // Default to online payment
    this.bankCode = 'BAOKIM',
    this.ticketType = 'tour',
    this.eventLocation,
  });

  @override
  List<Object?> get props => [
        customerName,
        phone,
        email,
        address,
        note,
        paymentMethod,
        bankCode,
        ticketType,
        eventLocation,
      ];
}

/// Event để reset payment state
class ResetPaymentState extends PaymentEvent {
  const ResetPaymentState();
}

/// Event để clear payment data
class ClearPaymentData extends PaymentEvent {
  const ClearPaymentData();
}
