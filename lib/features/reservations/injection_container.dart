import 'package:get_it/get_it.dart';
import '../../core/network/api_client.dart';
import 'data/datasources/reservations_remote_datasource.dart';
import 'data/repositories/reservations_repository_impl.dart';
import 'domain/repositories/reservations_repository.dart';
import 'domain/usecases/get_booking_tables_usecase.dart';
import 'presentation/bloc/reservations_bloc.dart';

/// Dependency injection container cho reservations feature
final sl = GetIt.instance;

/// Initialize reservations dependencies
Future<void> initReservationsDependencies() async {
  // Data sources
  sl.registerLazySingleton<ReservationsRemoteDataSource>(
    () => ReservationsRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
    ),
  );

  // Repositories
  sl.registerLazySingleton<ReservationsRepository>(
    () => ReservationsRepositoryImpl(
      remoteDataSource: sl<ReservationsRemoteDataSource>(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetBookingTablesUseCase(sl<ReservationsRepository>()));

  // BLoC
  sl.registerFactory(
    () => ReservationsBloc(
      getBookingTablesUseCase: sl<GetBookingTablesUseCase>(),
    ),
  );
}
