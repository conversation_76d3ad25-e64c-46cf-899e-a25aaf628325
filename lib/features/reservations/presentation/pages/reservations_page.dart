import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../domain/entities/booking_table.dart';
import '../bloc/reservations_bloc.dart';
import '../bloc/reservations_event.dart';
import '../bloc/reservations_state.dart';
import '../../../../core/di/injection_container.dart' as di;

import '../../../pos/presentation/pages/pos_main_screen.dart';

/// Reservations Page - Feature-based structure với Clean Architecture
class ReservationsPage extends StatelessWidget {
  const ReservationsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<ReservationsBloc>(),
      child: const _ReservationsPageContent(),
    );
  }
}

class _ReservationsPageContent extends StatefulWidget {
  const _ReservationsPageContent();

  @override
  State<_ReservationsPageContent> createState() =>
      _ReservationsPageContentState();
}

class _ReservationsPageContentState extends State<_ReservationsPageContent> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    // Load initial data
    context.read<ReservationsBloc>().add(const LoadBookingTables());

    // Setup scroll listener for load more
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle scroll events for load more
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // Trigger load more when user is 200px from bottom
      final bloc = context.read<ReservationsBloc>();
      final state = bloc.state;

      // Only trigger load more if we're in loaded state, have more data, and not currently loading more
      if (state is ReservationsLoaded &&
          state.hasMore &&
          bloc.state is! ReservationsLoadingMore) {
        bloc.add(const LoadMoreBookingTables());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PosColors.background,
      body: Column(
        children: [
          // Header với filter, search và action button
          _buildHeader(),

          // Content với BlocBuilder
          Expanded(
            child: BlocBuilder<ReservationsBloc, ReservationsState>(
              builder: (context, state) {
                if (state is ReservationsLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is ReservationsError) {
                  return _buildErrorWidget(state.message);
                } else if (state is ReservationsLoaded) {
                  return _buildBookingsList(state.filteredBookings);
                } else if (state is ReservationsLoadingMore) {
                  return _buildBookingsList(state.currentBookings,
                      isLoadingMore: true);
                }
                return const Center(child: Text('Không có dữ liệu'));
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Build header theo thiết kế: 1 hàng duy nhất với 2 cụm
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(PosSpacing.md),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Cụm bên trái: Date picker + Filter buttons
          Expanded(
            flex: 3,
            child: Row(
              children: [
                _buildDatePicker(),
                const SizedBox(
                    width: PosSpacing.sm), // Giảm từ lg xuống sm để sát gần hơn
                Expanded(child: _buildFilterSection()),
              ],
            ),
          ),

          const SizedBox(width: PosSpacing.lg),

          // Cụm bên phải: Search input + Action button
          Expanded(
            flex: 2,
            child: Row(
              children: [
                Expanded(child: _buildSearchInput()),
                const SizedBox(width: PosSpacing.md),
                _buildActionButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build filter section theo thiết kế: Tất cả, Alarcate, Buffet
  Widget _buildFilterSection() {
    return BlocBuilder<ReservationsBloc, ReservationsState>(
      builder: (context, state) {
        BookingStatus? selectedStatus;
        if (state is ReservationsLoaded) {
          selectedStatus = state.selectedStatus;
        }

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildFilterChip('Tất cả', null, selectedStatus),
            const SizedBox(width: PosSpacing.sm),
            _buildFilterChip(
                'Alarcate', BookingStatus.confirmed, selectedStatus),
            const SizedBox(width: PosSpacing.sm),
            _buildFilterChip('Buffet', BookingStatus.pending, selectedStatus),
          ],
        );
      },
    );
  }

  /// Build search input
  Widget _buildSearchInput() {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'Nhập số điện thoại để tìm kiếm',
        prefixIcon: const Icon(Icons.search, color: PosColors.textSecondary),
        suffixIcon: _searchController.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear, color: PosColors.textSecondary),
                onPressed: () {
                  _searchController.clear();
                  context.read<ReservationsBloc>().add(const ClearSearch());
                },
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: PosColors.primary),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: PosSpacing.md,
          vertical: PosSpacing.sm,
        ),
      ),
      onChanged: (query) {
        context.read<ReservationsBloc>().add(SearchBookingTables(query));
      },
    );
  }

  /// Navigate to Tables tab
  void _navigateToTablesTab() {
    posMainScreenKey.currentState?.switchToTablesTab();
  }

  /// Build action button theo thiết kế - không có icon, border radius cao hơn
  Widget _buildActionButton() {
    return ElevatedButton(
      onPressed: () {
        // Navigate to Tables tab for booking
        _navigateToTablesTab();
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF00BFFF), // Màu xanh cyan như thiết kế
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: PosSpacing.lg,
          vertical: PosSpacing.md,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16), // Tăng border radius
        ),
        elevation: 0, // Loại bỏ shadow
      ),
      child: const Text(
        'Đặt bàn',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  /// Build date picker
  Widget _buildDatePicker() {
    return Row(
      children: [
        const Icon(Icons.calendar_today,
            size: 20, color: PosColors.textSecondary),
        const SizedBox(width: PosSpacing.sm),
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: _selectedDate,
              firstDate: DateTime.now().subtract(const Duration(days: 30)),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );
            if (date != null && mounted) {
              setState(() {
                _selectedDate = date;
              });
              context.read<ReservationsBloc>().add(FilterByDate(date));
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: PosSpacing.md,
              vertical: PosSpacing.sm,
            ),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              '${_selectedDate.day.toString().padLeft(2, '0')}-'
              '${_selectedDate.month.toString().padLeft(2, '0')}-'
              '${_selectedDate.year}',
              style: PosTypography.bodyMedium,
            ),
          ),
        ),
        const SizedBox(width: PosSpacing.sm),
        if (_selectedDate != DateTime.now())
          TextButton(
            onPressed: () {
              setState(() {
                _selectedDate = DateTime.now();
              });
              context.read<ReservationsBloc>().add(const FilterByDate(null));
            },
            child: const Text('Hôm nay'),
          ),
      ],
    );
  }

  /// Build filter chip
  Widget _buildFilterChip(
      String label, BookingStatus? status, BookingStatus? selectedStatus) {
    final isSelected = selectedStatus == status;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        context
            .read<ReservationsBloc>()
            .add(FilterByStatus(selected ? status : null));
      },
      selectedColor: PosColors.primary.withValues(alpha: 0.2),
      checkmarkColor: PosColors.primary,
      labelStyle: TextStyle(
        color: isSelected ? PosColors.primary : PosColors.textSecondary,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: PosColors.error),
          const SizedBox(height: PosSpacing.md),
          Text(
            'Có lỗi xảy ra',
            style: PosTypography.headingSmall.copyWith(color: PosColors.error),
          ),
          const SizedBox(height: PosSpacing.sm),
          Text(
            message,
            style: PosTypography.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: PosSpacing.lg),
          ElevatedButton(
            onPressed: () {
              context
                  .read<ReservationsBloc>()
                  .add(const RefreshBookingTables());
            },
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  /// Build bookings list
  Widget _buildBookingsList(List<BookingTable> bookings,
      {bool isLoadingMore = false}) {
    if (bookings.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_busy, size: 64, color: PosColors.textSecondary),
            SizedBox(height: PosSpacing.md),
            Text(
              'Không có đặt bàn nào',
              style: PosTypography.headingSmall,
            ),
            SizedBox(height: PosSpacing.sm),
            Text(
              'Hãy tạo đặt bàn mới bằng cách bấm nút "Đặt bàn"',
              style: PosTypography.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<ReservationsBloc>().add(const RefreshBookingTables());
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(PosSpacing.md),
        itemCount: bookings.length + (isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == bookings.length && isLoadingMore) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(PosSpacing.md),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final booking = bookings[index];
          return _buildBookingCard(booking);
        },
      ),
    );
  }

  /// Build booking card theo thiết kế 4 cột như Bar/Kitchen
  Widget _buildBookingCard(BookingTable booking) {
    final statusColor = _getStatusColor(booking.status);
    final statusText = _getStatusText(booking.status);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: PosColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE0E0E0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Cột 1: Date + Time + Table (thu hẹp thêm ~50px)
            Expanded(
              flex: 1,
              child: _buildDateTimeTableColumn(booking),
            ),

            // Dashed line separator
            _buildDashedLine(),

            // Cột 2: Customer info (hẹp lại và sát trái)
            Expanded(
              flex: 1, // Giảm từ 2 xuống 1
              child: Padding(
                padding: const EdgeInsets.only(left: 4), // Sát gần về bên trái
                child: _buildCustomerInfoColumn(booking),
              ),
            ),

            // Dashed line separator
            _buildDashedLine(),

            // Cột 3: Guest count + Notes
            Expanded(
              flex: 3,
              child: _buildGuestNotesColumn(booking),
            ),

            // Dashed line separator
            _buildDashedLine(),

            // Cột 4: Status + Actions
            _buildStatusActionsColumn(booking, statusColor, statusText),
          ],
        ),
      ),
    );
  }

  /// Get status text
  String _getStatusText(BookingStatus status) {
    return status.displayName;
  }

  /// Build cột 1: Date + Time trên 1 hàng + Table
  Widget _buildDateTimeTableColumn(BookingTable booking) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Ngày giờ trên cùng 1 hàng
        Text(
          '${booking.formattedDate} ${booking.formattedTime.isNotEmpty ? booking.formattedTime : '--:--'}',
          style: PosTypography.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          booking.tableId ?? 'Chưa chọn bàn',
          style: PosTypography.bodySmall.copyWith(
            color: const Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  /// Build cột 2: Customer info
  Widget _buildCustomerInfoColumn(BookingTable booking) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          booking.customerName,
          style: PosTypography.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          booking.customerPhone,
          style: PosTypography.bodySmall.copyWith(
            color: const Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  /// Build cột 3: Guest count + Notes + Service type
  Widget _buildGuestNotesColumn(BookingTable booking) {
    // Xác định service type từ dữ liệu API hoặc default
    String serviceType = booking.storeName?.isNotEmpty == true
        ? (booking.storeName!.contains('Buffet') ? 'Buffet' : 'Alacarte')
        : 'Alacarte';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${booking.guestCount} người - Tạm ứng: ${booking.notes?.contains('Tạm ứng:') == true ? booking.notes!.split('Tạm ứng: ')[1].split(' +')[0] : '0đ'}',
          style: PosTypography.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            if (booking.hasNotes && booking.notes!.contains('+ Ghi chú'))
              Text(
                '+ Ghi chú',
                style: PosTypography.bodySmall.copyWith(
                  color: const Color(0xFF666666),
                ),
              ),
            const Spacer(), // Đẩy service type về cuối hàng
            Text(
              serviceType,
              style: PosTypography.bodySmall.copyWith(
                color: const Color(0xFF666666),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build cột 4: Actions only (bỏ status badge)
  Widget _buildStatusActionsColumn(
      BookingTable booking, Color statusColor, String statusText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Action buttons only
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _navigateToTablesTab(), // Navigate to Tables tab
              icon: const Icon(Icons.edit, size: 20, color: Color(0xFF03A9F4)),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            ),
            const SizedBox(width: 4),
            IconButton(
              onPressed: () => _navigateToTablesTab(), // Navigate to Tables tab
              icon: Icon(
                booking.status == BookingStatus.confirmed
                    ? Icons.check_circle
                    : Icons.warning,
                size: 20,
                color: booking.status == BookingStatus.confirmed
                    ? const Color(0xFF4CAF50)
                    : const Color(0xFFFF9800),
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            ),
          ],
        ),
      ],
    );
  }

  /// Build dashed line separator
  Widget _buildDashedLine() {
    return Container(
      width: 1,
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: CustomPaint(
        painter: DashedLinePainter(),
      ),
    );
  }

  /// Get status color
  Color _getStatusColor(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return PosColors.warning;
      case BookingStatus.confirmed:
        return PosColors.info;
      case BookingStatus.arrived:
        return PosColors.primary;
      case BookingStatus.seated:
        return PosColors.success;
      case BookingStatus.completed:
        return PosColors.success;
      case BookingStatus.cancelled:
        return PosColors.error;
      case BookingStatus.noShow:
        return PosColors.error;
    }
  }
}

/// Custom painter for dashed line
class DashedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFE0E0E0)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    const dashWidth = 4.0;
    const dashSpace = 4.0;
    double startY = 0;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashWidth),
        paint,
      );
      startY += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
