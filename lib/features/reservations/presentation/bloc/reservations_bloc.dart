import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/booking_table.dart';
import '../../domain/usecases/get_booking_tables_usecase.dart';
import 'reservations_event.dart';
import 'reservations_state.dart';

/// BLoC cho reservations feature
class ReservationsBloc extends Bloc<ReservationsEvent, ReservationsState> {
  final GetBookingTablesUseCase getBookingTablesUseCase;

  ReservationsBloc({
    required this.getBookingTablesUseCase,
  }) : super(const ReservationsInitial()) {
    on<LoadBookingTables>(_onLoadBookingTables);
    on<FilterByStatus>(_onFilterByStatus);
    on<FilterByDate>(_onFilterByDate);
    on<SearchBookingTables>(_onSearchBookingTables);
    on<ClearSearch>(_onClearSearch);
    on<RefreshBookingTables>(_onRefreshBookingTables);
    on<LoadMoreBookingTables>(_onLoadMoreBookingTables);
    on<UpdateBookingStatus>(_onUpdateBookingStatus);
    on<CreateBookingTable>(_onCreateBookingTable);
    on<DeleteBookingTable>(_onDeleteBookingTable);
  }

  /// Handle load booking tables
  Future<void> _onLoadBookingTables(
    LoadBookingTables event,
    Emitter<ReservationsState> emit,
  ) async {
    if (event.isRefresh && state is ReservationsLoaded) {
      // Keep current state while refreshing
      final currentState = state as ReservationsLoaded;
      emit(ReservationsLoading());
    } else if (event.page == 1) {
      emit(const ReservationsLoading());
    } else if (state is ReservationsLoaded) {
      // Loading more
      final currentState = state as ReservationsLoaded;
      emit(ReservationsLoadingMore(
        currentBookings: currentState.bookings,
        currentStatus: event.status,
        currentDate: event.date,
      ));
    }

    final result = await getBookingTablesUseCase(GetBookingTablesParams(
      page: event.page,
      limit: event.limit,
      status: event.status,
      date: event.date,
    ));

    result.fold(
      (failure) {
        if (state is ReservationsLoadingMore) {
          final currentState = state as ReservationsLoadingMore;
          emit(ReservationsError(
            message: failure.message,
            currentBookings: currentState.currentBookings,
          ));
        } else {
          emit(ReservationsError(message: failure.message));
        }
      },
      (newBookings) {
        List<BookingTable> allBookings;
        
        if (event.page == 1 || event.isRefresh) {
          allBookings = newBookings;
        } else if (state is ReservationsLoadingMore) {
          final currentState = state as ReservationsLoadingMore;
          allBookings = [...currentState.currentBookings, ...newBookings];
        } else {
          allBookings = newBookings;
        }

        final filteredBookings = _applyFilters(
          allBookings,
          status: event.status,
          date: event.date,
        );

        emit(ReservationsLoaded(
          bookings: allBookings,
          filteredBookings: filteredBookings,
          selectedStatus: event.status,
          selectedDate: event.date,
          hasMore: newBookings.length >= event.limit,
          currentPage: event.page,
        ));
      },
    );
  }

  /// Handle filter by status
  void _onFilterByStatus(
    FilterByStatus event,
    Emitter<ReservationsState> emit,
  ) {
    if (state is ReservationsLoaded) {
      final currentState = state as ReservationsLoaded;
      final filteredBookings = _applyFilters(
        currentState.bookings,
        status: event.status,
        date: currentState.selectedDate,
        query: currentState.searchQuery,
      );

      emit(currentState.copyWith(
        filteredBookings: filteredBookings,
        selectedStatus: event.status,
        clearStatus: event.status == null,
      ));
    }
  }

  /// Handle filter by date
  void _onFilterByDate(
    FilterByDate event,
    Emitter<ReservationsState> emit,
  ) {
    if (state is ReservationsLoaded) {
      final currentState = state as ReservationsLoaded;
      final filteredBookings = _applyFilters(
        currentState.bookings,
        status: currentState.selectedStatus,
        date: event.date,
        query: currentState.searchQuery,
      );

      emit(currentState.copyWith(
        filteredBookings: filteredBookings,
        selectedDate: event.date,
        clearDate: event.date == null,
      ));
    }
  }

  /// Handle search booking tables
  void _onSearchBookingTables(
    SearchBookingTables event,
    Emitter<ReservationsState> emit,
  ) {
    if (state is ReservationsLoaded) {
      final currentState = state as ReservationsLoaded;
      final filteredBookings = _applyFilters(
        currentState.bookings,
        status: currentState.selectedStatus,
        date: currentState.selectedDate,
        query: event.query,
      );

      emit(currentState.copyWith(
        filteredBookings: filteredBookings,
        searchQuery: event.query,
      ));
    }
  }

  /// Handle clear search
  void _onClearSearch(
    ClearSearch event,
    Emitter<ReservationsState> emit,
  ) {
    if (state is ReservationsLoaded) {
      final currentState = state as ReservationsLoaded;
      final filteredBookings = _applyFilters(
        currentState.bookings,
        status: currentState.selectedStatus,
        date: currentState.selectedDate,
      );

      emit(currentState.copyWith(
        filteredBookings: filteredBookings,
        clearQuery: true,
      ));
    }
  }

  /// Handle refresh booking tables
  void _onRefreshBookingTables(
    RefreshBookingTables event,
    Emitter<ReservationsState> emit,
  ) {
    BookingStatus? currentStatus;
    DateTime? currentDate;

    if (state is ReservationsLoaded) {
      final currentState = state as ReservationsLoaded;
      currentStatus = currentState.selectedStatus;
      currentDate = currentState.selectedDate;
    }

    add(LoadBookingTables(
      page: 1,
      status: currentStatus,
      date: currentDate,
      isRefresh: true,
    ));
  }

  /// Handle load more booking tables
  void _onLoadMoreBookingTables(
    LoadMoreBookingTables event,
    Emitter<ReservationsState> emit,
  ) {
    if (state is ReservationsLoaded) {
      final currentState = state as ReservationsLoaded;
      if (currentState.hasMore) {
        add(LoadBookingTables(
          page: currentState.currentPage + 1,
          status: currentState.selectedStatus,
          date: currentState.selectedDate,
        ));
      }
    }
  }

  /// Handle update booking status
  Future<void> _onUpdateBookingStatus(
    UpdateBookingStatus event,
    Emitter<ReservationsState> emit,
  ) async {
    if (state is ReservationsLoaded) {
      final currentState = state as ReservationsLoaded;
      
      emit(ReservationsActionLoading(
        currentBookings: currentState.bookings,
        currentFilteredBookings: currentState.filteredBookings,
        currentStatus: currentState.selectedStatus,
        currentDate: currentState.selectedDate,
        currentQuery: currentState.searchQuery,
      ));

      // TODO: Implement update booking status use case
      // For now, just simulate success
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Update the booking in the list
      final updatedBookings = currentState.bookings.map((booking) {
        if (booking.id == event.bookingId) {
          return booking.copyWith(status: event.status);
        }
        return booking;
      }).toList();

      final filteredBookings = _applyFilters(
        updatedBookings,
        status: currentState.selectedStatus,
        date: currentState.selectedDate,
        query: currentState.searchQuery,
      );

      emit(ReservationsActionSuccess(
        message: 'Cập nhật trạng thái thành công',
        bookings: updatedBookings,
        filteredBookings: filteredBookings,
        selectedStatus: currentState.selectedStatus,
        selectedDate: currentState.selectedDate,
        searchQuery: currentState.searchQuery,
      ));

      // Return to loaded state
      emit(ReservationsLoaded(
        bookings: updatedBookings,
        filteredBookings: filteredBookings,
        selectedStatus: currentState.selectedStatus,
        selectedDate: currentState.selectedDate,
        searchQuery: currentState.searchQuery,
        hasMore: currentState.hasMore,
        currentPage: currentState.currentPage,
      ));
    }
  }

  /// Handle create booking table
  Future<void> _onCreateBookingTable(
    CreateBookingTable event,
    Emitter<ReservationsState> emit,
  ) async {
    // TODO: Implement create booking table use case
    emit(const ReservationsLoading());
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Refresh the list
    add(const RefreshBookingTables());
  }

  /// Handle delete booking table
  Future<void> _onDeleteBookingTable(
    DeleteBookingTable event,
    Emitter<ReservationsState> emit,
  ) async {
    if (state is ReservationsLoaded) {
      final currentState = state as ReservationsLoaded;
      
      emit(ReservationsActionLoading(
        currentBookings: currentState.bookings,
        currentFilteredBookings: currentState.filteredBookings,
        currentStatus: currentState.selectedStatus,
        currentDate: currentState.selectedDate,
        currentQuery: currentState.searchQuery,
      ));

      // TODO: Implement delete booking table use case
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Remove the booking from the list
      final updatedBookings = currentState.bookings
          .where((booking) => booking.id != event.bookingId)
          .toList();

      final filteredBookings = _applyFilters(
        updatedBookings,
        status: currentState.selectedStatus,
        date: currentState.selectedDate,
        query: currentState.searchQuery,
      );

      emit(ReservationsActionSuccess(
        message: 'Xóa đặt bàn thành công',
        bookings: updatedBookings,
        filteredBookings: filteredBookings,
        selectedStatus: currentState.selectedStatus,
        selectedDate: currentState.selectedDate,
        searchQuery: currentState.searchQuery,
      ));

      // Return to loaded state
      emit(ReservationsLoaded(
        bookings: updatedBookings,
        filteredBookings: filteredBookings,
        selectedStatus: currentState.selectedStatus,
        selectedDate: currentState.selectedDate,
        searchQuery: currentState.searchQuery,
        hasMore: currentState.hasMore,
        currentPage: currentState.currentPage,
      ));
    }
  }

  /// Apply filters to booking list
  List<BookingTable> _applyFilters(
    List<BookingTable> bookings, {
    BookingStatus? status,
    DateTime? date,
    String? query,
  }) {
    var filtered = bookings;

    // Filter by status
    if (status != null) {
      filtered = filtered.where((booking) => booking.status == status).toList();
    }

    // Filter by date
    if (date != null) {
      filtered = filtered.where((booking) {
        return booking.reservationDate.year == date.year &&
               booking.reservationDate.month == date.month &&
               booking.reservationDate.day == date.day;
      }).toList();
    }

    // Filter by search query
    if (query?.isNotEmpty == true) {
      final lowerQuery = query!.toLowerCase();
      filtered = filtered.where((booking) {
        return booking.customerName.toLowerCase().contains(lowerQuery) ||
               booking.customerPhone.contains(lowerQuery) ||
               (booking.tableId?.toLowerCase().contains(lowerQuery) == true) ||
               (booking.notes?.toLowerCase().contains(lowerQuery) == true);
      }).toList();
    }

    return filtered;
  }
}
