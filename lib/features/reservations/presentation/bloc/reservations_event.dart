import 'package:equatable/equatable.dart';
import '../../domain/entities/booking_table.dart';

/// Base event cho ReservationsBloc
abstract class ReservationsEvent extends Equatable {
  const ReservationsEvent();

  @override
  List<Object?> get props => [];
}

/// Event để load danh sách booking tables
class LoadBookingTables extends ReservationsEvent {
  final int page;
  final int limit;
  final BookingStatus? status;
  final DateTime? date;
  final bool isRefresh;

  const LoadBookingTables({
    this.page = 1,
    this.limit = 10,
    this.status,
    this.date,
    this.isRefresh = false,
  });

  @override
  List<Object?> get props => [page, limit, status, date, isRefresh];
}

/// Event để filter theo status
class FilterByStatus extends ReservationsEvent {
  final BookingStatus? status;

  const FilterByStatus(this.status);

  @override
  List<Object?> get props => [status];
}

/// Event để filter theo date
class FilterByDate extends ReservationsEvent {
  final DateTime? date;

  const FilterByDate(this.date);

  @override
  List<Object?> get props => [date];
}

/// Event để search booking tables
class SearchBookingTables extends ReservationsEvent {
  final String query;

  const SearchBookingTables(this.query);

  @override
  List<Object?> get props => [query];
}

/// Event để clear search
class ClearSearch extends ReservationsEvent {
  const ClearSearch();
}

/// Event để refresh danh sách
class RefreshBookingTables extends ReservationsEvent {
  const RefreshBookingTables();
}

/// Event để load more (pagination)
class LoadMoreBookingTables extends ReservationsEvent {
  const LoadMoreBookingTables();
}

/// Event để update booking status
class UpdateBookingStatus extends ReservationsEvent {
  final String bookingId;
  final BookingStatus status;

  const UpdateBookingStatus({
    required this.bookingId,
    required this.status,
  });

  @override
  List<Object?> get props => [bookingId, status];
}

/// Event để create booking table
class CreateBookingTable extends ReservationsEvent {
  final String customerName;
  final String customerPhone;
  final int guestCount;
  final DateTime reservationDate;
  final String? reservationTime;
  final String? tableId;
  final String? notes;

  const CreateBookingTable({
    required this.customerName,
    required this.customerPhone,
    required this.guestCount,
    required this.reservationDate,
    this.reservationTime,
    this.tableId,
    this.notes,
  });

  @override
  List<Object?> get props => [
        customerName,
        customerPhone,
        guestCount,
        reservationDate,
        reservationTime,
        tableId,
        notes,
      ];
}

/// Event để delete booking table
class DeleteBookingTable extends ReservationsEvent {
  final String bookingId;

  const DeleteBookingTable(this.bookingId);

  @override
  List<Object?> get props => [bookingId];
}
