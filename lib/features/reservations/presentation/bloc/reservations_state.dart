import 'package:equatable/equatable.dart';
import '../../domain/entities/booking_table.dart';

/// Base state cho ReservationsBloc
abstract class ReservationsState extends Equatable {
  const ReservationsState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ReservationsInitial extends ReservationsState {
  const ReservationsInitial();
}

/// Loading state
class ReservationsLoading extends ReservationsState {
  const ReservationsLoading();
}

/// Loading more state (for pagination)
class ReservationsLoadingMore extends ReservationsState {
  final List<BookingTable> currentBookings;
  final BookingStatus? currentStatus;
  final DateTime? currentDate;
  final String? currentQuery;

  const ReservationsLoadingMore({
    required this.currentBookings,
    this.currentStatus,
    this.currentDate,
    this.currentQuery,
  });

  @override
  List<Object?> get props => [currentBookings, currentStatus, currentDate, currentQuery];
}

/// Loaded state
class ReservationsLoaded extends ReservationsState {
  final List<BookingTable> bookings;
  final List<BookingTable> filteredBookings;
  final BookingStatus? selectedStatus;
  final DateTime? selectedDate;
  final String? searchQuery;
  final bool hasMore;
  final int currentPage;

  const ReservationsLoaded({
    required this.bookings,
    required this.filteredBookings,
    this.selectedStatus,
    this.selectedDate,
    this.searchQuery,
    this.hasMore = true,
    this.currentPage = 1,
  });

  @override
  List<Object?> get props => [
        bookings,
        filteredBookings,
        selectedStatus,
        selectedDate,
        searchQuery,
        hasMore,
        currentPage,
      ];

  /// Copy with method
  ReservationsLoaded copyWith({
    List<BookingTable>? bookings,
    List<BookingTable>? filteredBookings,
    BookingStatus? selectedStatus,
    DateTime? selectedDate,
    String? searchQuery,
    bool? hasMore,
    int? currentPage,
    bool clearStatus = false,
    bool clearDate = false,
    bool clearQuery = false,
  }) {
    return ReservationsLoaded(
      bookings: bookings ?? this.bookings,
      filteredBookings: filteredBookings ?? this.filteredBookings,
      selectedStatus: clearStatus ? null : (selectedStatus ?? this.selectedStatus),
      selectedDate: clearDate ? null : (selectedDate ?? this.selectedDate),
      searchQuery: clearQuery ? null : (searchQuery ?? this.searchQuery),
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }

  /// Get active filters count
  int get activeFiltersCount {
    int count = 0;
    if (selectedStatus != null) count++;
    if (selectedDate != null) count++;
    if (searchQuery?.isNotEmpty == true) count++;
    return count;
  }

  /// Check if has active filters
  bool get hasActiveFilters => activeFiltersCount > 0;
}

/// Error state
class ReservationsError extends ReservationsState {
  final String message;
  final List<BookingTable>? currentBookings;

  const ReservationsError({
    required this.message,
    this.currentBookings,
  });

  @override
  List<Object?> get props => [message, currentBookings];
}

/// Success state for actions (create, update, delete)
class ReservationsActionSuccess extends ReservationsState {
  final String message;
  final List<BookingTable> bookings;
  final List<BookingTable> filteredBookings;
  final BookingStatus? selectedStatus;
  final DateTime? selectedDate;
  final String? searchQuery;

  const ReservationsActionSuccess({
    required this.message,
    required this.bookings,
    required this.filteredBookings,
    this.selectedStatus,
    this.selectedDate,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        message,
        bookings,
        filteredBookings,
        selectedStatus,
        selectedDate,
        searchQuery,
      ];
}

/// Action loading state (for create, update, delete)
class ReservationsActionLoading extends ReservationsState {
  final List<BookingTable> currentBookings;
  final List<BookingTable> currentFilteredBookings;
  final BookingStatus? currentStatus;
  final DateTime? currentDate;
  final String? currentQuery;

  const ReservationsActionLoading({
    required this.currentBookings,
    required this.currentFilteredBookings,
    this.currentStatus,
    this.currentDate,
    this.currentQuery,
  });

  @override
  List<Object?> get props => [
        currentBookings,
        currentFilteredBookings,
        currentStatus,
        currentDate,
        currentQuery,
      ];
}
