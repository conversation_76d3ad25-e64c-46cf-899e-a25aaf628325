import 'package:dio/dio.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/api_client.dart';
import '../models/booking_table_model.dart';
import '../../domain/entities/booking_table.dart';

/// Remote data source cho reservations
abstract class ReservationsRemoteDataSource {
  /// L<PERSON>y danh sách booking tables
  Future<List<BookingTableModel>> getBookingTables({
    int page = 1,
    int limit = 10,
    BookingStatus? status,
    DateTime? date,
  });

  /// Lấy chi tiết booking table
  Future<BookingTableModel> getBookingTableDetail(String id);

  /// Tạo booking table mới
  Future<BookingTableModel> createBookingTable({
    required String customerName,
    required String customerPhone,
    required int guestCount,
    required DateTime reservationDate,
    String? reservationTime,
    String? tableId,
    String? notes,
  });

  /// Cập nhật booking table
  Future<BookingTableModel> updateBookingTable({
    required String id,
    String? customerName,
    String? customerPhone,
    int? guestCount,
    DateTime? reservationDate,
    String? reservationTime,
    String? tableId,
    String? notes,
    BookingStatus? status,
  });

  /// Xóa booking table
  Future<void> deleteBookingTable(String id);

  /// Cập nhật trạng thái booking
  Future<BookingTableModel> updateBookingStatus({
    required String id,
    required BookingStatus status,
  });
}

/// Implementation của ReservationsRemoteDataSource
class ReservationsRemoteDataSourceImpl implements ReservationsRemoteDataSource {
  final ApiClient apiClient;

  ReservationsRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<BookingTableModel>> getBookingTables({
    int page = 1,
    int limit = 10,
    BookingStatus? status,
    DateTime? date,
  }) async {
    try {
      // API endpoint theo yêu cầu
      final response = await apiClient.post(
        '/user/api/pos-get-list-booking-table/1',
        data: {
          'page': page,
          'limit': limit,
          'output': 'json',
          if (status != null) 'status': _statusToApiValue(status),
          if (date != null) 'date': date.toIso8601String().split('T')[0],
        },
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;

        // Parse response data
        List<dynamic> bookingsData = [];

        if (responseData is Map<String, dynamic>) {
          // Thử các key có thể có
          bookingsData = responseData['data'] as List<dynamic>? ??
              responseData['bookings'] as List<dynamic>? ??
              responseData['list'] as List<dynamic>? ??
              responseData['items'] as List<dynamic>? ??
              [];
        } else if (responseData is List<dynamic>) {
          bookingsData = responseData;
        }

        return bookingsData
            .map((json) =>
                BookingTableModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException(
          message:
              'Lỗi tải danh sách đặt bàn: ${response.statusCode} ${response.statusMessage}',
        );
      }
    } on DioException catch (e) {
      throw ServerException(message: _handleDioError(e));
    } catch (e) {
      throw ServerException(
          message: 'Lỗi không xác định khi lấy danh sách đặt bàn: $e');
    }
  }

  @override
  Future<BookingTableModel> getBookingTableDetail(String id) async {
    try {
      final response = await apiClient.get('/user/api/booking-table/$id');

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;
        final bookingData = responseData is Map<String, dynamic>
            ? (responseData['data'] ?? responseData)
            : responseData;

        return BookingTableModel.fromJson(bookingData as Map<String, dynamic>);
      } else {
        throw ServerException(
          message:
              'Lỗi tải chi tiết đặt bàn: ${response.statusCode} ${response.statusMessage}',
        );
      }
    } on DioException catch (e) {
      throw ServerException(message: _handleDioError(e));
    } catch (e) {
      throw ServerException(
          message: 'Lỗi không xác định khi lấy chi tiết đặt bàn: $e');
    }
  }

  @override
  Future<BookingTableModel> createBookingTable({
    required String customerName,
    required String customerPhone,
    required int guestCount,
    required DateTime reservationDate,
    String? reservationTime,
    String? tableId,
    String? notes,
  }) async {
    try {
      final response = await apiClient.post(
        '/user/api/booking-table',
        data: {
          'customerName': customerName,
          'customerPhone': customerPhone,
          'guestCount': guestCount,
          'reservationDate': reservationDate.toIso8601String().split('T')[0],
          if (reservationTime != null) 'reservationTime': reservationTime,
          if (tableId != null) 'tableId': tableId,
          if (notes != null) 'notes': notes,
        },
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;
        final bookingData = responseData is Map<String, dynamic>
            ? (responseData['data'] ?? responseData)
            : responseData;

        return BookingTableModel.fromJson(bookingData as Map<String, dynamic>);
      } else {
        throw ServerException(
          message:
              'Lỗi tạo đặt bàn: ${response.statusCode} ${response.statusMessage}',
        );
      }
    } on DioException catch (e) {
      throw ServerException(message: _handleDioError(e));
    } catch (e) {
      throw ServerException(message: 'Lỗi không xác định khi tạo đặt bàn: $e');
    }
  }

  @override
  Future<BookingTableModel> updateBookingTable({
    required String id,
    String? customerName,
    String? customerPhone,
    int? guestCount,
    DateTime? reservationDate,
    String? reservationTime,
    String? tableId,
    String? notes,
    BookingStatus? status,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (customerName != null) data['customerName'] = customerName;
      if (customerPhone != null) data['customerPhone'] = customerPhone;
      if (guestCount != null) data['guestCount'] = guestCount;
      if (reservationDate != null) {
        data['reservationDate'] =
            reservationDate.toIso8601String().split('T')[0];
      }
      if (reservationTime != null) data['reservationTime'] = reservationTime;
      if (tableId != null) data['tableId'] = tableId;
      if (notes != null) data['notes'] = notes;
      if (status != null) data['status'] = _statusToApiValue(status);

      final response =
          await apiClient.put('/user/api/booking-table/$id', data: data);

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;
        final bookingData = responseData is Map<String, dynamic>
            ? (responseData['data'] ?? responseData)
            : responseData;

        return BookingTableModel.fromJson(bookingData as Map<String, dynamic>);
      } else {
        throw ServerException(
          message:
              'Lỗi cập nhật đặt bàn: ${response.statusCode} ${response.statusMessage}',
        );
      }
    } on DioException catch (e) {
      throw ServerException(message: _handleDioError(e));
    } catch (e) {
      throw ServerException(
          message: 'Lỗi không xác định khi cập nhật đặt bàn: $e');
    }
  }

  @override
  Future<void> deleteBookingTable(String id) async {
    try {
      final response = await apiClient.delete('/user/api/booking-table/$id');

      if (response.statusCode != 200) {
        throw ServerException(
          message:
              'Lỗi xóa đặt bàn: ${response.statusCode} ${response.statusMessage}',
        );
      }
    } on DioException catch (e) {
      throw ServerException(message: _handleDioError(e));
    } catch (e) {
      throw ServerException(message: 'Lỗi không xác định khi xóa đặt bàn: $e');
    }
  }

  @override
  Future<BookingTableModel> updateBookingStatus({
    required String id,
    required BookingStatus status,
  }) async {
    return updateBookingTable(id: id, status: status);
  }

  /// Convert BookingStatus to API value
  int _statusToApiValue(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return 0;
      case BookingStatus.confirmed:
        return 1;
      case BookingStatus.arrived:
        return 2;
      case BookingStatus.seated:
        return 3;
      case BookingStatus.completed:
        return 4;
      case BookingStatus.cancelled:
        return 5;
      case BookingStatus.noShow:
        return 6;
    }
  }

  /// Handle Dio errors
  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Kết nối timeout';
      case DioExceptionType.sendTimeout:
        return 'Gửi dữ liệu timeout';
      case DioExceptionType.receiveTimeout:
        return 'Nhận dữ liệu timeout';
      case DioExceptionType.badResponse:
        return 'Lỗi server: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Yêu cầu bị hủy';
      case DioExceptionType.connectionError:
        return 'Lỗi kết nối mạng';
      default:
        return 'Lỗi không xác định: ${e.message}';
    }
  }
}
