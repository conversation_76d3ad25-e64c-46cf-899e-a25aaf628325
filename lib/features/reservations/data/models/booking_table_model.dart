import '../../domain/entities/booking_table.dart';

/// Model cho booking table từ API
class BookingTableModel extends BookingTable {
  const BookingTableModel({
    required super.id,
    required super.customerName,
    required super.customerPhone,
    required super.guestCount,
    required super.reservationDate,
    super.reservationTime,
    super.tableId,
    super.notes,
    required super.status,
    super.storeId,
    super.storeName,
    required super.createdAt,
    super.updatedAt,
  });

  /// Factory constructor từ JSON
  factory BookingTableModel.fromJson(Map<String, dynamic> json) {
    try {
      return BookingTableModel(
        id: json['_id'] as String? ?? '',
        customerName: json['customerName'] as String? ?? json['name'] as String? ?? '',
        customerPhone: json['customerPhone'] as String? ?? json['phone'] as String? ?? '',
        guestCount: _parseGuestCount(json['guestCount'] ?? json['guests'] ?? json['people']),
        reservationDate: _parseDate(json['reservationDate'] ?? json['date'] ?? json['bookingDate']),
        reservationTime: json['reservationTime'] as String? ?? json['time'] as String?,
        tableId: json['tableId'] as String? ?? json['table'] as String?,
        notes: json['notes'] as String? ?? json['note'] as String? ?? json['comment'] as String?,
        status: _parseStatus(json['status']),
        storeId: json['storeId'] as String? ?? json['store'] as String?,
        storeName: json['storeName'] as String?,
        createdAt: _parseDate(json['createdAt'] ?? json['created_at'] ?? DateTime.now().toIso8601String()),
        updatedAt: json['updatedAt'] != null ? _parseDate(json['updatedAt']) : null,
      );
    } catch (e) {
      throw FormatException('Lỗi parse BookingTableModel: $e');
    }
  }

  /// Parse guest count từ dynamic value
  static int _parseGuestCount(dynamic value) {
    if (value == null) return 1;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value) ?? 1;
    }
    return 1;
  }

  /// Parse date từ string hoặc timestamp
  static DateTime _parseDate(dynamic value) {
    if (value == null) return DateTime.now();
    
    if (value is String) {
      // Thử parse ISO string
      try {
        return DateTime.parse(value);
      } catch (e) {
        // Thử parse timestamp string
        final timestamp = int.tryParse(value);
        if (timestamp != null) {
          return DateTime.fromMillisecondsSinceEpoch(timestamp);
        }
        return DateTime.now();
      }
    }
    
    if (value is int) {
      // Timestamp
      return DateTime.fromMillisecondsSinceEpoch(value);
    }
    
    return DateTime.now();
  }

  /// Parse status từ dynamic value
  static BookingStatus _parseStatus(dynamic value) {
    if (value == null) return BookingStatus.pending;
    
    if (value is int) {
      switch (value) {
        case 0:
          return BookingStatus.pending;
        case 1:
          return BookingStatus.confirmed;
        case 2:
          return BookingStatus.arrived;
        case 3:
          return BookingStatus.seated;
        case 4:
          return BookingStatus.completed;
        case 5:
          return BookingStatus.cancelled;
        case 6:
          return BookingStatus.noShow;
        default:
          return BookingStatus.pending;
      }
    }
    
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'pending':
        case 'waiting':
          return BookingStatus.pending;
        case 'confirmed':
        case 'confirm':
          return BookingStatus.confirmed;
        case 'arrived':
        case 'arrive':
          return BookingStatus.arrived;
        case 'seated':
        case 'seat':
          return BookingStatus.seated;
        case 'completed':
        case 'complete':
        case 'done':
          return BookingStatus.completed;
        case 'cancelled':
        case 'cancel':
          return BookingStatus.cancelled;
        case 'noshow':
        case 'no_show':
        case 'no-show':
          return BookingStatus.noShow;
        default:
          return BookingStatus.pending;
      }
    }
    
    return BookingStatus.pending;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'guestCount': guestCount,
      'reservationDate': reservationDate.toIso8601String(),
      'reservationTime': reservationTime,
      'tableId': tableId,
      'notes': notes,
      'status': _statusToInt(status),
      'storeId': storeId,
      'storeName': storeName,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Convert status to int
  static int _statusToInt(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return 0;
      case BookingStatus.confirmed:
        return 1;
      case BookingStatus.arrived:
        return 2;
      case BookingStatus.seated:
        return 3;
      case BookingStatus.completed:
        return 4;
      case BookingStatus.cancelled:
        return 5;
      case BookingStatus.noShow:
        return 6;
    }
  }

  /// Copy with method
  @override
  BookingTableModel copyWith({
    String? id,
    String? customerName,
    String? customerPhone,
    int? guestCount,
    DateTime? reservationDate,
    String? reservationTime,
    String? tableId,
    String? notes,
    BookingStatus? status,
    String? storeId,
    String? storeName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BookingTableModel(
      id: id ?? this.id,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      guestCount: guestCount ?? this.guestCount,
      reservationDate: reservationDate ?? this.reservationDate,
      reservationTime: reservationTime ?? this.reservationTime,
      tableId: tableId ?? this.tableId,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
