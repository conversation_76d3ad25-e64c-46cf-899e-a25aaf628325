import 'package:dartz/dartz.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/booking_table.dart';
import '../../domain/repositories/reservations_repository.dart';
import '../datasources/reservations_remote_datasource.dart';

/// Implementation của ReservationsRepository
class ReservationsRepositoryImpl implements ReservationsRepository {
  final ReservationsRemoteDataSource remoteDataSource;

  ReservationsRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, List<BookingTable>>> getBookingTables({
    int page = 1,
    int limit = 10,
    BookingStatus? status,
    DateTime? date,
  }) async {
    try {
      final bookingTables = await remoteDataSource.getBookingTables(
        page: page,
        limit: limit,
        status: status,
        date: date,
      );
      return Right(bookingTables);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Lỗi không xác định: $e'));
    }
  }

  @override
  Future<Either<Failure, BookingTable>> getBookingTableDetail(String id) async {
    try {
      final bookingTable = await remoteDataSource.getBookingTableDetail(id);
      return Right(bookingTable);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Lỗi không xác định: $e'));
    }
  }

  @override
  Future<Either<Failure, BookingTable>> createBookingTable({
    required String customerName,
    required String customerPhone,
    required int guestCount,
    required DateTime reservationDate,
    String? reservationTime,
    String? tableId,
    String? notes,
  }) async {
    try {
      final bookingTable = await remoteDataSource.createBookingTable(
        customerName: customerName,
        customerPhone: customerPhone,
        guestCount: guestCount,
        reservationDate: reservationDate,
        reservationTime: reservationTime,
        tableId: tableId,
        notes: notes,
      );
      return Right(bookingTable);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Lỗi không xác định: $e'));
    }
  }

  @override
  Future<Either<Failure, BookingTable>> updateBookingTable({
    required String id,
    String? customerName,
    String? customerPhone,
    int? guestCount,
    DateTime? reservationDate,
    String? reservationTime,
    String? tableId,
    String? notes,
    BookingStatus? status,
  }) async {
    try {
      final bookingTable = await remoteDataSource.updateBookingTable(
        id: id,
        customerName: customerName,
        customerPhone: customerPhone,
        guestCount: guestCount,
        reservationDate: reservationDate,
        reservationTime: reservationTime,
        tableId: tableId,
        notes: notes,
        status: status,
      );
      return Right(bookingTable);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Lỗi không xác định: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteBookingTable(String id) async {
    try {
      await remoteDataSource.deleteBookingTable(id);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Lỗi không xác định: $e'));
    }
  }

  @override
  Future<Either<Failure, BookingTable>> updateBookingStatus({
    required String id,
    required BookingStatus status,
  }) async {
    try {
      final bookingTable = await remoteDataSource.updateBookingStatus(
        id: id,
        status: status,
      );
      return Right(bookingTable);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Lỗi không xác định: $e'));
    }
  }
}
