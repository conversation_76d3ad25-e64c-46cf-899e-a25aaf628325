import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/booking_table.dart';
import '../repositories/reservations_repository.dart';

/// Use case để lấy danh sách booking tables
class GetBookingTablesUseCase
    implements UseCase<List<BookingTable>, GetBookingTablesParams> {
  final ReservationsRepository repository;

  GetBookingTablesUseCase(this.repository);

  @override
  Future<Either<Failure, List<BookingTable>>> call(
      GetBookingTablesParams params) async {
    return await repository.getBookingTables(
      page: params.page,
      limit: params.limit,
      status: params.status,
      date: params.date,
    );
  }
}

/// Parameters cho GetBookingTablesUseCase
class GetBookingTablesParams extends Equatable {
  final int page;
  final int limit;
  final BookingStatus? status;
  final DateTime? date;

  const GetBookingTablesParams({
    this.page = 1,
    this.limit = 10,
    this.status,
    this.date,
  });

  @override
  List<Object?> get props => [page, limit, status, date];

  GetBookingTablesParams copyWith({
    int? page,
    int? limit,
    BookingStatus? status,
    DateTime? date,
  }) {
    return GetBookingTablesParams(
      page: page ?? this.page,
      limit: limit ?? this.limit,
      status: status ?? this.status,
      date: date ?? this.date,
    );
  }
}
