import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/booking_table.dart';

/// Repository interface cho reservations
abstract class ReservationsRepository {
  /// L<PERSON>y danh sách booking tables
  Future<Either<Failure, List<BookingTable>>> getBookingTables({
    int page = 1,
    int limit = 10,
    BookingStatus? status,
    DateTime? date,
  });

  /// Lấy chi tiết booking table
  Future<Either<Failure, BookingTable>> getBookingTableDetail(String id);

  /// Tạo booking table mới
  Future<Either<Failure, BookingTable>> createBookingTable({
    required String customerName,
    required String customerPhone,
    required int guestCount,
    required DateTime reservationDate,
    String? reservationTime,
    String? tableId,
    String? notes,
  });

  /// Cập nhật booking table
  Future<Either<Failure, BookingTable>> updateBookingTable({
    required String id,
    String? customerName,
    String? customerPhone,
    int? guestCount,
    DateTime? reservationDate,
    String? reservationTime,
    String? tableId,
    String? notes,
    BookingStatus? status,
  });

  /// Xóa booking table
  Future<Either<Failure, void>> deleteBookingTable(String id);

  /// Cập nhật trạng thái booking
  Future<Either<Failure, BookingTable>> updateBookingStatus({
    required String id,
    required BookingStatus status,
  });
}
