import 'package:equatable/equatable.dart';

/// Entity cho booking table từ API
class BookingTable extends Equatable {
  final String id;
  final String customerName;
  final String customerPhone;
  final int guestCount;
  final DateTime reservationDate;
  final String? reservationTime;
  final String? tableId;
  final String? notes;
  final BookingStatus status;
  final String? storeId;
  final String? storeName;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const BookingTable({
    required this.id,
    required this.customerName,
    required this.customerPhone,
    required this.guestCount,
    required this.reservationDate,
    this.reservationTime,
    this.tableId,
    this.notes,
    required this.status,
    this.storeId,
    this.storeName,
    required this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        customerName,
        customerPhone,
        guestCount,
        reservationDate,
        reservationTime,
        tableId,
        notes,
        status,
        storeId,
        storeName,
        createdAt,
        updatedAt,
      ];

  /// Copy with method
  BookingTable copyWith({
    String? id,
    String? customerName,
    String? customerPhone,
    int? guestCount,
    DateTime? reservationDate,
    String? reservationTime,
    String? tableId,
    String? notes,
    BookingStatus? status,
    String? storeId,
    String? storeName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BookingTable(
      id: id ?? this.id,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      guestCount: guestCount ?? this.guestCount,
      reservationDate: reservationDate ?? this.reservationDate,
      reservationTime: reservationTime ?? this.reservationTime,
      tableId: tableId ?? this.tableId,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Format thời gian hiển thị
  String get formattedTime {
    if (reservationTime == null) return '';
    return reservationTime!;
  }

  /// Format ngày hiển thị
  String get formattedDate {
    return '${reservationDate.day.toString().padLeft(2, '0')}-'
           '${reservationDate.month.toString().padLeft(2, '0')}-'
           '${reservationDate.year}';
  }

  /// Kiểm tra có ghi chú không
  bool get hasNotes => notes?.isNotEmpty == true;
}

/// Enum cho trạng thái booking
enum BookingStatus {
  pending,     // Chờ xác nhận
  confirmed,   // Đã xác nhận
  arrived,     // Đã đến
  seated,      // Đã vào bàn
  completed,   // Hoàn thành
  cancelled,   // Đã hủy
  noShow,      // Không đến
}

/// Extension cho BookingStatus
extension BookingStatusExtension on BookingStatus {
  String get displayName {
    switch (this) {
      case BookingStatus.pending:
        return 'Chờ xác nhận';
      case BookingStatus.confirmed:
        return 'Đã xác nhận';
      case BookingStatus.arrived:
        return 'Đã đến';
      case BookingStatus.seated:
        return 'Đã vào bàn';
      case BookingStatus.completed:
        return 'Hoàn thành';
      case BookingStatus.cancelled:
        return 'Đã hủy';
      case BookingStatus.noShow:
        return 'Không đến';
    }
  }

  bool get isActive {
    return this == BookingStatus.pending ||
           this == BookingStatus.confirmed ||
           this == BookingStatus.arrived ||
           this == BookingStatus.seated;
  }

  bool get isCompleted {
    return this == BookingStatus.completed ||
           this == BookingStatus.cancelled ||
           this == BookingStatus.noShow;
  }
}
