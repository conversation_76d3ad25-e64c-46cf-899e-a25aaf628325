import 'package:equatable/equatable.dart';

/// Entity đại diện cho danh mục sản phẩm trong Chợ Hải Sản
///
/// Đ<PERSON><PERSON><PERSON> sử dụng để filter sản phẩm theo danh mục
class MenuCategory extends Equatable {
  /// ID duy nhất của danh mục
  final String id;

  /// Tên hiển thị của danh mục
  final String name;

  /// Loại danh mục (0,1,2 cho sản phẩm)
  final int type;

  /// URL ảnh của danh mục
  final String? picture;

  /// Trạng thái được chọn (dùng cho UI)
  final bool isSelected;

  const MenuCategory({
    required this.id,
    required this.name,
    required this.type,
    this.picture,
    this.isSelected = false,
  });

  /// Tạo bản sao với các thuộc tính được cập nhật
  MenuCategory copyWith({
    String? id,
    String? name,
    int? type,
    String? picture,
    bool? isSelected,
  }) {
    return MenuCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      picture: picture ?? this.picture,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  List<Object?> get props => [id, name, type, picture, isSelected];

  @override
  String toString() {
    return 'MenuCategory(id: $id, name: $name, type: $type, picture: $picture, isSelected: $isSelected)';
  }
}
