import 'package:equatable/equatable.dart';

/// Entity đại diện cho chi tiết sản phẩm Menu
///
/// Ch<PERSON>a thông tin đầy đủ của sản phẩm khi xem chi tiết
class MenuProductDetail extends Equatable {
  /// ID duy nhất của sản phẩm
  final String id;

  /// Tên sản phẩm
  final String name;

  /// Mô tả chi tiết
  final String description;

  /// Giá hiện tại
  final double price;

  /// Gi<PERSON> cũ (nếu có khuyến mãi)
  final double? priceOld;

  /// URL hình ảnh chính
  final String thumbnail;

  /// Danh sách hình ảnh
  final List<String> pictures;

  /// ID danh mục
  final String categoryId;

  /// ID cửa hàng
  final String storeId;

  /// Tên cửa hàng
  final String? storeName;

  /// Số lượt xem
  final int watched;

  /// <PERSON><PERSON>h thu
  final double revenue;

  /// Trạng thái sản phẩm
  final int status;

  /// Thương hiệu
  final String? trademark;

  /// Mã sản phẩm
  final int code;

  /// Loại vận chuyển
  final String? transport;

  /// Loại ship
  final int typeShip;

  /// Cân nặng
  final double weight;

  /// Phân loại sản phẩm (size, màu sắc, v.v.)
  final List<ProductClassification> classify;

  const MenuProductDetail({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.priceOld,
    required this.thumbnail,
    this.pictures = const [],
    required this.categoryId,
    required this.storeId,
    this.storeName,
    this.watched = 0,
    this.revenue = 0,
    this.status = 1,
    this.trademark,
    this.code = 0,
    this.transport,
    this.typeShip = 0,
    this.weight = 0,
    this.classify = const [],
  });

  /// Kiểm tra xem sản phẩm có đang giảm giá không
  bool get hasDiscount => priceOld != null && priceOld! > price;

  /// Tính phần trăm giảm giá
  double get discountPercentage {
    if (!hasDiscount) return 0;
    return ((priceOld! - price) / priceOld!) * 100;
  }

  /// Format giá tiền theo định dạng VND
  String get formattedPrice {
    return '${price.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        )}đ';
  }

  /// Format giá cũ theo định dạng VND
  String? get formattedPriceOld {
    if (priceOld == null) return null;
    return '${priceOld!.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        )}đ';
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        price,
        priceOld,
        thumbnail,
        pictures,
        categoryId,
        storeId,
        storeName,
        watched,
        revenue,
        status,
        trademark,
        code,
        transport,
        typeShip,
        weight,
        classify,
      ];
}

/// Entity đại diện cho phân loại sản phẩm (size, màu sắc, v.v.)
class ProductClassification extends Equatable {
  /// ID phân loại
  final String id;

  /// Tên phân loại (ví dụ: "Kích thước", "Màu sắc")
  final String name;

  /// Danh sách các lựa chọn
  final List<ClassificationOption> data;

  const ProductClassification({
    required this.id,
    required this.name,
    required this.data,
  });

  @override
  List<Object?> get props => [id, name, data];
}

/// Entity đại diện cho lựa chọn trong phân loại
class ClassificationOption extends Equatable {
  /// ID lựa chọn
  final String id;

  /// Tên lựa chọn (ví dụ: "Size L", "Màu đỏ")
  final String name;

  /// Giá của lựa chọn này
  final String price;

  /// Giá cũ (nếu có)
  final String? priceOld;

  /// Khối lượng/trọng lượng
  final String mass;

  const ClassificationOption({
    required this.id,
    required this.name,
    required this.price,
    this.priceOld,
    required this.mass,
  });

  @override
  List<Object?> get props => [id, name, price, priceOld, mass];
}
