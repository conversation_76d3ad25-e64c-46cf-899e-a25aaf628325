import 'package:equatable/equatable.dart';

/// Entity đại diện cho sản phẩm hải sản
/// 
/// Ch<PERSON>a thông tin cơ bản của sản phẩm để hiển thị trong grid
class MenuProduct extends Equatable {
  /// ID duy nhất của sản phẩm
  final String id;
  
  /// Tên sản phẩm
  final String name;
  
  /// Giá hiện tại
  final double price;
  
  /// <PERSON><PERSON><PERSON> (nếu có khuyến mãi)
  final double? priceOld;
  
  /// URL hình ảnh thumbnail
  final String thumbnail;
  
  /// Phần trăm giảm giá (nếu có)
  final String? discount;
  
  /// ID danh mục
  final String categoryId;
  
  /// ID cửa hàng
  final String storeId;
  
  /// Tên cửa hàng
  final String? storeName;
  
  /// Số lượt xem
  final int watched;
  
  /// Doanh thu
  final double revenue;
  
  /// Trạng thái sản phẩm
  final int status;

  const MenuProduct({
    required this.id,
    required this.name,
    required this.price,
    this.priceOld,
    required this.thumbnail,
    this.discount,
    required this.categoryId,
    required this.storeId,
    this.storeName,
    this.watched = 0,
    this.revenue = 0,
    this.status = 1,
  });

  /// Kiểm tra xem sản phẩm có đang giảm giá không
  bool get hasDiscount => priceOld != null && priceOld! > price;

  /// Tính phần trăm giảm giá
  double get discountPercentage {
    if (!hasDiscount) return 0;
    return ((priceOld! - price) / priceOld!) * 100;
  }

  /// Format giá tiền theo định dạng VND
  String get formattedPrice {
    return '${price.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}đ';
  }

  /// Format giá cũ theo định dạng VND
  String? get formattedPriceOld {
    if (priceOld == null) return null;
    return '${priceOld!.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}đ';
  }

  @override
  List<Object?> get props => [
        id,
        name,
        price,
        priceOld,
        thumbnail,
        discount,
        categoryId,
        storeId,
        storeName,
        watched,
        revenue,
        status,
      ];

  @override
  String toString() {
    return 'MenuProduct(id: $id, name: $name, price: $price, thumbnail: $thumbnail)';
  }
}
