import 'package:equatable/equatable.dart';
import 'menu_product.dart';

/// Entity cho item trong giỏ hàng
class CartItem extends Equatable {
  /// ID sản phẩm
  final String productId;
  
  /// Thông tin sản phẩm
  final MenuProduct product;
  
  /// Số lượng
  final int quantity;
  
  /// Ghi chú
  final String? note;
  
  /// Thời gian thêm vào giỏ
  final DateTime addedAt;

  const CartItem({
    required this.productId,
    required this.product,
    required this.quantity,
    this.note,
    required this.addedAt,
  });

  /// Tính tổng tiền cho item này
  double get totalPrice => product.price * quantity;

  /// Tạo bản sao với các thuộc tính mới
  CartItem copyWith({
    String? productId,
    MenuProduct? product,
    int? quantity,
    String? note,
    DateTime? addedAt,
  }) {
    return CartItem(
      productId: productId ?? this.productId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      note: note ?? this.note,
      addedAt: addedAt ?? this.addedAt,
    );
  }

  @override
  List<Object?> get props => [productId, product, quantity, note, addedAt];
}

/// Entity cho tổng kết giỏ hàng
class CartSummary extends Equatable {
  /// Danh sách items trong giỏ
  final List<CartItem> items;
  
  /// Tổng số lượng sản phẩm
  final int totalQuantity;
  
  /// Tổng tiền
  final double totalPrice;
  
  /// Số lượng items khác nhau
  final int itemCount;

  const CartSummary({
    required this.items,
    required this.totalQuantity,
    required this.totalPrice,
    required this.itemCount,
  });

  /// Tạo CartSummary rỗng
  const CartSummary.empty()
      : items = const [],
        totalQuantity = 0,
        totalPrice = 0,
        itemCount = 0;

  /// Tạo CartSummary từ danh sách items
  factory CartSummary.fromItems(List<CartItem> items) {
    final totalQuantity = items.fold<int>(0, (sum, item) => sum + item.quantity);
    final totalPrice = items.fold<double>(0, (sum, item) => sum + item.totalPrice);
    
    return CartSummary(
      items: items,
      totalQuantity: totalQuantity,
      totalPrice: totalPrice,
      itemCount: items.length,
    );
  }

  /// Kiểm tra giỏ hàng có rỗng không
  bool get isEmpty => items.isEmpty;

  /// Kiểm tra giỏ hàng có sản phẩm không
  bool get isNotEmpty => items.isNotEmpty;

  @override
  List<Object?> get props => [items, totalQuantity, totalPrice, itemCount];
}
