import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/menu_repository.dart';

/// Use case để lấy tất cả dữ liệu menu
class GetMenuDataUseCase implements UseCase<Map<String, dynamic>, NoParams> {
  final MenuRepository repository;

  GetMenuDataUseCase(this.repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(NoParams params) async {
    return await repository.getMenuData();
  }
}

