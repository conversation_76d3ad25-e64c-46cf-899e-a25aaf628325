import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/menu_product_detail.dart';
import '../repositories/menu_repository.dart';

/// Use case to get product details by ID for Menu
class GetMenuProductDetailUseCase implements UseCase<MenuProductDetail, GetMenuProductDetailParams> {
  final MenuRepository repository;

  GetMenuProductDetailUseCase(this.repository);

  @override
  Future<Either<Failure, MenuProductDetail>> call(GetMenuProductDetailParams params) async {
    return await repository.getProductDetail(params.productId);
  }
}

/// Parameters for GetMenuProductDetailUseCase
class GetMenuProductDetailParams extends Equatable {
  /// ID of the product to get details for
  final String productId;

  const GetMenuProductDetailParams({
    required this.productId,
  });

  @override
  List<Object?> get props => [productId];

  @override
  String toString() {
    return 'GetMenuProductDetailParams(productId: $productId)';
  }
}

