import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../entities/menu_product_detail.dart';

/// Repository interface cho tính năng Menu
///
/// Định nghĩa các phương thức để truy xuất dữ liệu từ API
abstract class MenuRepository {
  /// Lấy tất cả dữ liệu menu (danh mục và sản phẩm) từ một API endpoint duy nhất
  ///
  /// Returns:
  /// - Right: Dữ liệu [Map<String, dynamic>] nếu thành công
  /// - Left: [Failure] nếu có lỗi
  Future<Either<Failure, Map<String, dynamic>>> getMenuData();

  /// Lấy chi tiết một sản phẩm theo ID
  ///
  /// Params:
  /// - [productId]: ID của sản phẩm cần lấy chi tiết
  ///
  /// Returns:
  /// - Right: Dữ liệu [MenuProductDetail] nếu thành công
  /// - Left: [Failure] nếu có lỗi
  Future<Either<Failure, MenuProductDetail>> getProductDetail(String productId);
}
