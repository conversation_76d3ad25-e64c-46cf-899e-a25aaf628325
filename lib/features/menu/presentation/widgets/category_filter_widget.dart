import 'package:flutter/material.dart';

import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../../../shared/widgets/index.dart';
import '../../domain/entities/menu_category.dart';

/// Widget hiển thị bộ lọc danh mục chợ hải sản
class CategoryFilterWidget extends StatelessWidget {
  /// Danh sách danh mục
  final List<MenuCategory> categories;

  /// Callback khi chọn danh mục
  final Function(String? categoryId) onCategorySelected;

  /// ID danh mục được chọn hiện tại
  final String? selectedCategoryId;

  /// Có đang loading không
  final bool isLoading;

  const CategoryFilterWidget({
    Key? key,
    required this.categories,
    required this.onCategorySelected,
    this.selectedCategoryId,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (categories.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 110, // Tăng thêm để phù hợp với text 2 dòng
      padding: const EdgeInsets.only(
        left: PosSpacing.md + 16,
        right: PosSpacing.md,
      ),
      decoration: BoxDecoration(
        color: PosColors.surface,
        border: Border(
          bottom: BorderSide(color: PosColors.borderLight),
        ),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(vertical: PosSpacing.sm),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final bool isSelected = category.id.isEmpty
              ? selectedCategoryId == null
              : category.id == selectedCategoryId;

          return CategoryItemWidget(
            id: category.id,
            name: category.name,
            iconUrl: category.picture,
            fallbackIcon: _getFallbackIcon(category),
            isSelected: isSelected,
            width: 100,
            isLoading: isLoading && isSelected,
            onTap: () {
              final newCategoryId = category.id.isEmpty ? null : category.id;
              onCategorySelected(newCategoryId);
            },
          );
        },
      ),
    );
  }

  /// Lấy icon mặc định cho từng category hải sản
  IconData _getFallbackIcon(MenuCategory category) {
    final name = category.name.toLowerCase();

    if (name.contains('tất cả') || category.id.isEmpty) {
      return Icons.apps;
    } else if (name.contains('cá') || name.contains('fish')) {
      return Icons.set_meal;
    } else if (name.contains('tôm') || name.contains('shrimp')) {
      return Icons.restaurant;
    } else if (name.contains('cua') || name.contains('crab')) {
      return Icons.dining;
    } else if (name.contains('ốc') || name.contains('snail')) {
      return Icons.local_dining;
    } else if (name.contains('mực') || name.contains('squid')) {
      return Icons.restaurant_menu;
    } else if (name.contains('nghêu') || name.contains('clam')) {
      return Icons.local_restaurant;
    } else if (name.contains('sò') || name.contains('scallop')) {
      return Icons.food_bank;
    } else if (name.contains('hàu') || name.contains('oyster')) {
      return Icons.bakery_dining;
    } else {
      return Icons.restaurant_menu;
    }
  }
}
