import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

import '../../../../core/di/injection_container.dart';
import '../../domain/entities/menu_product.dart';
import '../../domain/entities/menu_product_detail.dart';
import '../../domain/usecases/get_menu_product_detail_usecase.dart';
import 'product_image_slider.dart';
import 'fullscreen_image_gallery.dart';

/// Dialog hiển thị chi tiết sản phẩm với image slider
class ProductDetailDialog extends StatefulWidget {
  final String productId;
  final MenuProduct?
      initialProduct; // Optional để hiển thị thông tin cơ bản ngay

  const ProductDetailDialog({
    Key? key,
    required this.productId,
    this.initialProduct,
  }) : super(key: key);

  @override
  State<ProductDetailDialog> createState() => _ProductDetailDialogState();
}

class _ProductDetailDialogState extends State<ProductDetailDialog> {
  int quantity = 1;
  MenuProductDetail? productDetail;
  bool isLoading = true;
  String? errorMessage;
  final TextEditingController _noteController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadProductDetail();
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  Future<void> _loadProductDetail() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final useCase = sl<GetMenuProductDetailUseCase>();

      final result = await useCase(
          GetMenuProductDetailParams(productId: widget.productId));

      result.fold(
        (failure) {
          setState(() {
            isLoading = false;
            errorMessage = failure.message;
          });
        },
        (detail) {
          setState(() {
            isLoading = false;
            productDetail = detail;
          });
        },
      );
    } catch (e) {
      setState(() {
        isLoading = false;
        errorMessage = 'Có lỗi xảy ra: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.all(isTablet ? 32 : 12),
      child: Container(
        width: isTablet ? 900 : double.infinity,
        height: isTablet ? 700 : screenSize.height * 0.9, // Tăng từ 0.8 lên 0.9
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            // Header
            _buildHeader(),

            // Content
            Expanded(
              child: _buildContent(isTablet),
            ),

            // Footer with actions
            if (!isLoading && productDetail != null) _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.restaurant_menu,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Chi tiết sản phẩm',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.close,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(bool isTablet) {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Đang tải thông tin sản phẩm...'),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            SizedBox(height: 16),
            Text(
              errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadProductDetail,
              child: Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (productDetail == null) {
      return const Center(
        child: Text('Không tìm thấy thông tin sản phẩm'),
      );
    }

    return isTablet ? _buildTabletLayout() : _buildMobileLayout();
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        // Left: Image
        Expanded(
          flex: 1,
          child: _buildImageSection(),
        ),

        // Right: Product Info
        Expanded(
          flex: 1,
          child: _buildProductInfo(),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        // Image section - Fixed height
        SizedBox(
          height: 250, // Giảm height để có thêm space cho content
          child: _buildImageSection(),
        ),

        // Product info - Scrollable với flex để tránh overflow
        Expanded(
          child: _buildProductInfo(),
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    // Hiển thị ảnh từ initialProduct nếu productDetail chưa load
    if (productDetail == null && widget.initialProduct != null) {
      return Padding(
        padding: const EdgeInsets.all(16),
        child: ProductImageSlider(
          mainImageUrl: widget.initialProduct!.thumbnail,
          imageUrls: const [], // Chưa có pictures từ basic product
          onImageTap: _showFullscreenGallery,
        ),
      );
    }

    if (productDetail == null) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: ProductImageSlider(
        mainImageUrl: productDetail!.thumbnail,
        imageUrls: productDetail!.pictures,
        onImageTap: _showFullscreenGallery,
      ),
    );
  }

  void _showFullscreenGallery(List<String> images, int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FullscreenImageGallery(
          images: images,
          initialIndex: initialIndex,
        ),
      ),
    );
  }

  Widget _buildProductInfo() {
    // Hiển thị thông tin từ initialProduct nếu productDetail chưa load
    if (productDetail == null && widget.initialProduct != null) {
      final product = widget.initialProduct!;
      return Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Name and Stock Status
            Row(
              children: [
                Expanded(
                  child: Text(
                    product.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Price
            Row(
              children: [
                Text(
                  '${_formatPrice(product.price)} đ',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            const Text(
              'Đang tải thông tin chi tiết...',
              style: TextStyle(
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),

            const SizedBox(height: 16),
          ],
        ),
      );
    }

    if (productDetail == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Product Name and Stock Status
          Row(
            children: [
              Expanded(
                child: Text(
                  productDetail!.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              _buildStockStatus(),
            ],
          ),

          const SizedBox(height: 12),

          // Price
          Row(
            children: [
              Text(
                '${_formatPrice(productDetail!.price)} đ',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              if (productDetail!.priceOld != null &&
                  productDetail!.priceOld! > productDetail!.price) ...[
                const SizedBox(width: 12),
                Text(
                  '${_formatPrice(productDetail!.priceOld!)} đ',
                  style: TextStyle(
                    fontSize: 16,
                    decoration: TextDecoration.lineThrough,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 16),

          // Description - Scrollable container
          if (productDetail!.description.isNotEmpty) ...[
            const Text(
              'Mô tả sản phẩm:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 200, // Tăng kích thước box mô tả
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[50],
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(12),
                child: Html(
                  data: productDetail!.description,
                  style: {
                    "body": Style(
                      fontSize: FontSize(14),
                      color: Colors.grey[600],
                      lineHeight: const LineHeight(1.5),
                      margin: Margins.zero,
                      padding: HtmlPaddings.zero,
                    ),
                    "p": Style(
                      margin: Margins.only(bottom: 8),
                    ),
                    "h1, h2, h3, h4, h5, h6": Style(
                      fontWeight: FontWeight.w600,
                      margin: Margins.only(top: 8, bottom: 4),
                    ),
                    "ul, ol": Style(
                      margin: Margins.only(left: 16, bottom: 8),
                    ),
                    "li": Style(
                      margin: Margins.only(bottom: 4),
                    ),
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Additional product info
          if (productDetail!.trademark?.isNotEmpty == true) ...[
            _buildInfoRow('Thương hiệu:', productDetail!.trademark!),
            const SizedBox(height: 8),
          ],

          if (productDetail!.weight > 0) ...[
            _buildInfoRow('Khối lượng:', '${productDetail!.weight} kg'),
            const SizedBox(height: 8),
          ],

          const SizedBox(height: 16),

          // Quantity selector centered
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildQuantitySelector(),
            ],
          ),

          const SizedBox(height: 16),

          // Note input
          _buildNoteInput(),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStockStatus() {
    // Sử dụng initialProduct nếu productDetail chưa có
    final status = productDetail?.status ?? 1;
    final isInStock = status == 1; // Assuming status 1 means available

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isInStock ? Colors.green[100] : Colors.red[100],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isInStock ? Colors.green[300]! : Colors.red[300]!,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isInStock ? Icons.check_circle : Icons.cancel,
            size: 16,
            color: isInStock ? Colors.green[700] : Colors.red[700],
          ),
          const SizedBox(width: 6),
          Text(
            isInStock ? 'Còn hàng' : 'Hết hàng',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: isInStock ? Colors.green[700] : Colors.red[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantitySelector() {
    return Row(
      children: [
        const Text(
          'Số lượng:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Decrease button
              InkWell(
                onTap: () {
                  if (quantity > 1) {
                    setState(() {
                      quantity--;
                    });
                  }
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: const Icon(
                    Icons.remove,
                    size: 16,
                    color: Colors.grey,
                  ),
                ),
              ),

              // Quantity display
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: Text(
                  quantity.toString(),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              // Increase button
              InkWell(
                onTap: () {
                  setState(() {
                    quantity++;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: const Icon(
                    Icons.add,
                    size: 16,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFooter() {
    final status = productDetail!.status;
    final price = productDetail!.price;
    final isInStock = status == 1; // Assuming status 1 means available

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          // Total Price
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Tổng tiền:',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  '${_formatPrice(price * quantity)} đ',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // Add to Cart Button
          ElevatedButton.icon(
            onPressed: isInStock ? _onAddToCart : null,
            icon: Icon(Icons.shopping_cart_outlined),
            label: Text('Thêm vào giỏ'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ghi chú:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _noteController,
          maxLines: 3,
          maxLength: 200,
          decoration: InputDecoration(
            hintText: 'Nhập ghi chú cho sản phẩm (tùy chọn)',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Theme.of(context).primaryColor),
            ),
            contentPadding: const EdgeInsets.all(12),
          ),
          style: const TextStyle(fontSize: 14),
        ),
      ],
    );
  }

  void _onAddToCart() {
    // Close dialog and return result
    Navigator.of(context).pop({
      'action': 'add_to_cart',
      'product': productDetail,
      'quantity': quantity,
      'note': _noteController.text.trim().isEmpty
          ? null
          : _noteController.text.trim(),
    });
  }

  String _formatPrice(double price) {
    return price.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
  }
}
