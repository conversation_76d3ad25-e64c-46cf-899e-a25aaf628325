import 'package:flutter/material.dart';

import '../../domain/entities/menu_product.dart';
import '../../../../src/common/widgets/product_item_widget.dart';

/// Widget hiển thị grid sản phẩm với responsive design
class ProductGridWidget extends StatelessWidget {
  /// Danh sách sản phẩm
  final List<MenuProduct> products;

  /// Callback khi tap vào sản phẩm
  final Function(MenuProduct product)? onProductTap;

  /// Callback khi thêm vào giỏ hàng
  final Function(MenuProduct product)? onAddToCart;

  const ProductGridWidget({
    Key? key,
    required this.products,
    this.onProductTap,
    this.onAddToCart,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (products.isEmpty) {
      return const _EmptyProductsWidget();
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4, // Thay đổi từ 3 thành 4 cột
        mainAxisExtent: 240, // Tăng thêm một chút để tránh overflow
        crossAxisSpacing: 16, // Giảm khoảng cách ngang để phù hợp với 4 cột
        mainAxisSpacing: 24, // Giảm khoảng cách dọc
      ),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return ProductItemWidget(
          title: product.name,
          imageUrl: product.thumbnail,
          price: product.formattedPrice,
          oldPrice: product.hasDiscount ? product.formattedPriceOld : null,
          discount: product.discount,
          onTap: () => onProductTap?.call(product),
          onAddToCart: () => onAddToCart?.call(product),
        );
      },
    );
  }
}

/// Widget hiển thị khi không có sản phẩm
class _EmptyProductsWidget extends StatelessWidget {
  const _EmptyProductsWidget();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Không có sản phẩm nào',
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
