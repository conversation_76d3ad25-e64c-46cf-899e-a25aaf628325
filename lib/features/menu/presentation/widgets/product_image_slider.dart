import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// Widget hiển thị slider ả<PERSON> sản phẩm với thumbnail selector
/// T<PERSON><PERSON>ng tự như implementation trong frontend
class ProductImageSlider extends StatefulWidget {
  /// URL ảnh chính (thumbnail)
  final String mainImageUrl;
  
  /// Danh sách URL các ảnh khác
  final List<String> imageUrls;
  
  /// Callback khi tap vào ảnh để xem fullscreen
  final Function(List<String> images, int initialIndex)? onImageTap;

  const ProductImageSlider({
    Key? key,
    required this.mainImageUrl,
    this.imageUrls = const [],
    this.onImageTap,
  }) : super(key: key);

  @override
  State<ProductImageSlider> createState() => _ProductImageSliderState();
}

class _ProductImageSliderState extends State<ProductImageSlider> {
  late PageController _pageController;
  int _currentIndex = 0;
  late List<String> _allImages;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _buildImageList();
  }

  @override
  void didUpdateWidget(ProductImageSlider oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.mainImageUrl != widget.mainImageUrl ||
        oldWidget.imageUrls != widget.imageUrls) {
      _buildImageList();
      _currentIndex = 0;
      if (_pageController.hasClients) {
        _pageController.animateToPage(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _buildImageList() {
    _allImages = [];
    
    // Thêm ảnh chính nếu có
    if (widget.mainImageUrl.isNotEmpty) {
      _allImages.add(widget.mainImageUrl);
    }
    
    // Thêm các ảnh khác, loại bỏ trùng lặp với ảnh chính
    for (final imageUrl in widget.imageUrls) {
      if (imageUrl.isNotEmpty && !_allImages.contains(imageUrl)) {
        _allImages.add(imageUrl);
      }
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_allImages.isEmpty) {
      return _buildPlaceholder();
    }

    return Column(
      children: [
        // Main image slider
        Expanded(
          child: _buildMainImageSlider(),
        ),
        
        // Thumbnail selector (chỉ hiển thị khi có nhiều hơn 1 ảnh)
        if (_allImages.length > 1) ...[
          const SizedBox(height: 12),
          _buildThumbnailSelector(),
        ],
      ],
    );
  }

  Widget _buildMainImageSlider() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[100],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: PageView.builder(
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          itemCount: _allImages.length,
          itemBuilder: (context, index) {
            return GestureDetector(
              onTap: () => widget.onImageTap?.call(_allImages, index),
              child: _buildImageWidget(_allImages[index]),
            );
          },
        ),
      ),
    );
  }

  Widget _buildImageWidget(String imageUrl) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        color: Colors.grey[100],
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        color: Colors.grey[100],
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 50,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              'Không thể tải ảnh',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThumbnailSelector() {
    return SizedBox(
      height: 60,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _allImages.length,
        itemBuilder: (context, index) {
          final isSelected = index == _currentIndex;
          
          return GestureDetector(
            onTap: () => _selectImage(index),
            child: Container(
              width: 60,
              height: 60,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? Colors.blue : Colors.grey.shade300,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: CachedNetworkImage(
                  imageUrl: _allImages[index],
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image_not_supported,
                      size: 20,
                      color: Colors.grey[400],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[100],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image,
              size: 50,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              'Không có hình ảnh',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _selectImage(int index) {
    setState(() {
      _currentIndex = index;
    });
    
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }
}
