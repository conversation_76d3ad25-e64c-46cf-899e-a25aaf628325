import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../../../core/di/injection_container.dart';
import '../../../../core/theme/pos_typography.dart';
import '../bloc/cart_bloc.dart';

import '../../domain/entities/cart_item.dart';
import '../../../../components/order_type_selection_modal.dart';

import '../bloc/menu_bloc.dart';
import '../widgets/category_filter_widget.dart';
import '../widgets/product_grid_widget.dart';
import '../widgets/product_detail_dialog.dart';
import '../../domain/entities/menu_product.dart';
import '../../domain/entities/menu_product_detail.dart';
import '../../../tables/presentation/widgets/change_table_modal.dart';
import '../../../../models/pos_models.dart';

/// Trang chính của tính năng Menu
class MenuPage extends StatelessWidget {
  final String? tableName;

  const MenuPage({Key? key, this.tableName}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<MenuBloc>()..add(FetchMenuData()),
      child: _MenuPageContent(tableName: tableName),
    );
  }
}

/// Content của MenuPage
class _MenuPageContent extends StatefulWidget {
  final String? tableName;

  const _MenuPageContent({Key? key, this.tableName}) : super(key: key);

  @override
  State<_MenuPageContent> createState() => _MenuPageContentState();
}

class _MenuPageContentState extends State<_MenuPageContent> {
  String? _currentTableName;

  @override
  void initState() {
    super.initState();
    _currentTableName = widget.tableName;
  }

  @override
  void didUpdateWidget(covariant _MenuPageContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.tableName != oldWidget.tableName) {
      setState(() {
        _currentTableName = widget.tableName;
      });
    }
  }

  void _updateTableName(String newTableName) {
    setState(() {
      _currentTableName = newTableName;
    });
  }

  /// Xử lý tap vào sản phẩm
  void _onProductTap(MenuProduct product) {
    print('Product clicked: ${product.name}');

    // Thêm nhanh vào giỏ hàng với quantity = 1
    context.read<CartBloc>().add(AddToCart(product: product, quantity: 1));

    // Hiển thị snackbar thông báo
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đã thêm ${product.name} vào giỏ hàng'),
        duration: const Duration(seconds: 1),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PosColors.background,
      body: BlocBuilder<MenuBloc, MenuState>(
        builder: (context, state) {
          if (state is MenuLoading || state is MenuInitial) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is MenuError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Lỗi: ${state.message}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<MenuBloc>().add(FetchMenuData());
                    },
                    child: const Text('Thử lại'),
                  )
                ],
              ),
            );
          }

          if (state is MenuLoaded) {
            return Row(
              children: [
                // Main Content Area (Left)
                Expanded(
                  flex: 3,
                  child: Column(
                    children: [
                      // Horizontal Categories
                      CategoryFilterWidget(
                        categories: state.categories,
                        selectedCategoryId: state.selectedCategoryId,
                        onCategorySelected: (categoryId) {
                          context
                              .read<MenuBloc>()
                              .add(SelectMenuCategory(categoryId: categoryId));
                        },
                      ),

                      // Products Grid
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(PosSpacing.md),
                          child: state.filteredProducts.isEmpty
                              ? Center(
                                  child: Text(
                                    'Không có sản phẩm nào trong danh mục này.',
                                    style: PosTypography.bodyLarge.copyWith(
                                      color: PosColors.textSecondary,
                                    ),
                                  ),
                                )
                              : ProductGridWidget(
                                  products: state.filteredProducts,
                                  onProductTap: _onProductTap,
                                  onAddToCart: (product) {
                                    context
                                        .read<CartBloc>()
                                        .add(AddToCart(product: product));
                                  },
                                ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Cart Sidebar (Right)
                Container(
                  width: 350,
                  decoration: BoxDecoration(
                    color: PosColors.surface,
                    border: Border(
                      left: BorderSide(color: PosColors.borderLight),
                    ),
                  ),
                  child: _CartSidebar(
                    tableName: _currentTableName,
                    onTableChanged: _updateTableName,
                  ),
                ),
              ],
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}

/// Widget hiển thị giỏ hàng bên phải
class _CartSidebar extends StatefulWidget {
  final String? tableName;
  final Function(String) onTableChanged;

  const _CartSidebar({this.tableName, required this.onTableChanged});
  @override
  State<_CartSidebar> createState() => _CartSidebarState();
}

class _CartSidebarState extends State<_CartSidebar> {
  bool _isProcessingPayment = false;
  OrderType _selectedOrderType = OrderType.dineIn;

  void _onCartItemClick(CartItem cartItem) async {
    print('Cart item clicked: ${cartItem.product.name}');

    // Hiển thị dialog chi tiết sản phẩm từ cart item
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => ProductDetailDialog(
        productId: cartItem.productId,
        initialProduct: cartItem.product,
      ),
    );

    // Xử lý kết quả trả về từ dialog
    if (result != null && result['action'] == 'add_to_cart' && mounted) {
      final productDetail = result['product'] as MenuProductDetail;
      final quantity = result['quantity'] as int;
      final cartProduct = MenuProduct(
        id: productDetail.id,
        name: productDetail.name,
        price: productDetail.price,
        thumbnail: productDetail.thumbnail,
        categoryId: productDetail.categoryId,
        storeId: productDetail.storeId,
      );

      // Thêm vào cart thông qua CartBloc
      context.read<CartBloc>().add(AddToCart(
            product: cartProduct,
            quantity: quantity,
          ));

      // Hiển thị snackbar thông báo
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Đã thêm ${cartProduct.name} vào giỏ hàng'),
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// Xử lý khi nhấn vào button "Tại chỗ" để mở modal chọn loại order
  Future<void> _handleOrderTypeSelection() async {
    final selectedType = await showDialog<OrderType>(
      context: context,
      barrierDismissible: false,
      builder: (context) => OrderTypeSelectionModal(
        selectedOrderType: _selectedOrderType,
        onOrderTypeSelected: (orderType) {
          Navigator.of(context).pop(orderType);
        },
      ),
    );

    // Sau khi dialog đóng, kiểm tra xem widget có còn trong cây không trước khi cập nhật state.
    // Đây là bước quan trọng để tránh crash.
    if (selectedType != null && mounted) {
      setState(() {
        _selectedOrderType = selectedType;
      });
    }
  }

  Future<void> _handlePayment() async {
    setState(() {
      _isProcessingPayment = true;
    });

    // Simulate payment processing
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isProcessingPayment = false;
    });

    // TODO: Implement actual payment logic
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, cartState) {
        return Column(
          children: [
            // Cart Header - New design with "Tại chỗ" button, quantity and ID
            Container(
              padding: const EdgeInsets.all(PosSpacing.md),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: PosColors.borderLight),
                ),
              ),
              child: Row(
                children: [
                  // Order type button (Tại chỗ/Mang về/Giao hàng)
                  InkWell(
                    onTap: _handleOrderTypeSelection,
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      height: 36, // Fixed height for consistency
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                      ),
                      decoration: BoxDecoration(
                        color: _selectedOrderType.color,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _selectedOrderType.icon,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            _selectedOrderType.label,
                            style: const TextStyle(
                              fontFamily: 'Varela',
                              fontSize: 15,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFFFFFFFF),
                              height: 1.0,
                              letterSpacing: 0,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: PosSpacing.sm),
                  // Table number display and change table button
                  InkWell(
                    onTap: () async {
                      final selectedTable = await showDialog<RestaurantTable>(
                        context: context,
                        builder: (context) => const ChangeTableModal(),
                      );
                      if (selectedTable != null && mounted) {
                        widget.onTableChanged(selectedTable.name);
                      }
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      height: 36, // Fixed height for consistency
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF9800),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          // Extract number from table name like "Bàn 1" -> "1"
                          widget.tableName?.replaceAll(RegExp(r'[^0-9]'), '') ??
                              '0',
                          style: const TextStyle(
                            fontFamily: 'Varela',
                            fontSize: 15,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFFFFFFFF),
                            height: 1.0,
                            letterSpacing: 0,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  const Spacer(),
                  // ID display with new typography
                  Text(
                    '#0345',
                    style: const TextStyle(
                      fontFamily: 'Varela',
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF966021),
                      height: 1.0,
                      letterSpacing: 0,
                    ),
                  ),
                ],
              ),
            ),

            // Cart Content
            Expanded(
              child: cartState.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.shopping_cart_outlined, // Changed icon
                            size: 64,
                            color: PosColors.textSecondary,
                          ),
                          const SizedBox(height: PosSpacing.md),
                          Text(
                            'Giỏ hàng trống',
                            style: PosTypography.bodyLarge.copyWith(
                              color: PosColors.textSecondary,
                            ),
                          ),
                          const SizedBox(height: PosSpacing.sm),
                          Text(
                            'Thêm món vào giỏ hàng', // Changed text
                            style: PosTypography.bodyMedium.copyWith(
                              color: PosColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(PosSpacing.sm),
                      itemCount: cartState.items.length,
                      itemBuilder: (context, index) {
                        final item = cartState.items[index];
                        return GestureDetector(
                          onTap: () => _onCartItemClick(item),
                          child: _CartItemWidget(item: item),
                        );
                      },
                    ),
            ),

            // Cart Footer
            if (cartState.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(PosSpacing.md),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(color: PosColors.borderLight),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Thanh tiền:',
                          style: PosTypography.bodyLarge.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${cartState.totalPrice.toStringAsFixed(0).replaceAllMapped(
                                RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                (Match m) => '${m[1]},',
                              )} đ',
                          style: PosTypography.headingSmall.copyWith(
                            color: PosColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: PosSpacing.md),
                    Row(
                      children: [
                        // Thanh toán button
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.only(right: 20),
                            child: ElevatedButton(
                              onPressed:
                                  _isProcessingPayment ? null : _handlePayment,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF27C7FF),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: PosSpacing.md,
                                ),
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                disabledBackgroundColor: const Color(0xFF27C7FF)
                                    .withValues(alpha: 0.7),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'THANH TOÁN',
                                    style: PosTypography.bodyMedium.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                  if (_isProcessingPayment) ...[
                                    const SizedBox(width: 8),
                                    const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: PosSpacing.sm),
                        // Trash button
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFF9800),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: IconButton(
                            onPressed: () {
                              context.read<CartBloc>().add(const ClearCart());
                            },
                            icon: const Icon(
                              Icons.delete_outline,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }
}

/// Widget hiển thị từng item trong giỏ hàng
class _CartItemWidget extends StatelessWidget {
  final CartItem item;

  const _CartItemWidget({
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: PosSpacing.sm),
      padding: const EdgeInsets.all(PosSpacing.sm),
      decoration: BoxDecoration(
        color: PosColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: PosColors.borderLight),
      ),
      child: Row(
        children: [
          // Product Image
          ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Image.network(
              item.product.thumbnail,
              width: 50,
              height: 50,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 50,
                  height: 50,
                  color: PosColors.borderLight,
                  child: Icon(
                    Icons.image_not_supported_outlined, // Changed icon
                    color: PosColors.textSecondary,
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: PosSpacing.sm),

          // Product Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.name,
                  style: PosTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2), // Giảm từ 4 xuống 2
                Text(
                  item.product.formattedPrice,
                  style: PosTypography.bodySmall.copyWith(
                    color: PosColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 6), // Tăng khoảng cách với bottom
                // Ghi chú
                Text(
                  'Ghi chú: ${item.note ?? ''}',
                  style: PosTypography.bodySmall.copyWith(
                    color: PosColors.textSecondary,
                    fontStyle:
                        item.note == null ? FontStyle.italic : FontStyle.normal,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Quantity Controls
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: PosColors.borderLight),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Decrease button
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(color: PosColors.borderLight),
                    ),
                  ),
                  child: InkWell(
                    onTap: () {
                      context.read<CartBloc>().add(
                            UpdateQuantity(
                              productId: item.productId,
                              quantity: item.quantity - 1,
                            ),
                          );
                    },
                    child: const Icon(
                      Icons.remove,
                      size: 16,
                      color: PosColors.textSecondary,
                    ),
                  ),
                ),
                // Quantity display
                Container(
                  width: 40,
                  height: 32,
                  alignment: Alignment.center,
                  child: Text(
                    '${item.quantity}',
                    style: PosTypography.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                // Increase button
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(color: PosColors.borderLight),
                    ),
                  ),
                  child: InkWell(
                    onTap: () {
                      context.read<CartBloc>().add(
                            UpdateQuantity(
                              productId: item.productId,
                              quantity: item.quantity + 1,
                            ),
                          );
                    },
                    child: const Icon(
                      Icons.add,
                      size: 16,
                      color: PosColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
