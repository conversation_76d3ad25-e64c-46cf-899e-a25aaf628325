import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/usecases/usecase.dart';
import '../../domain/usecases/get_menu_data_usecase.dart';
import '../../domain/entities/menu_category.dart';
import '../../domain/entities/menu_product.dart';
import '../../data/models/menu_product_model.dart';

part 'menu_event.dart';
part 'menu_state.dart';

class MenuBloc extends Bloc<MenuEvent, MenuState> {
  final GetMenuDataUseCase getMenuDataUseCase;

  MenuBloc({required this.getMenuDataUseCase}) : super(MenuInitial()) {
    on<FetchMenuData>(_onFetchMenuData);
    on<SelectMenuCategory>(_onSelectMenuCategory);
  }

  Future<void> _onFetchMenuData(
    FetchMenuData event,
    Emitter<MenuState> emit,
  ) async {
    emit(MenuLoading());
    final failureOrMenuData = await getMenuDataUseCase(NoParams());

    failureOrMenuData.fold(
      (failure) => emit(MenuError(message: failure.message)),
      (menuData) {
        // Safely parse data with guaranteed non-null lists
        List<MenuCategory> categoriesData = [];
        final categoriesResponse =
            menuData['categories'] as Map<String, dynamic>?;
        if (categoriesResponse != null && categoriesResponse['data'] is Map) {
          final categoriesList =
              (categoriesResponse['data'] as Map)['categories'] as List?;
          if (categoriesList != null) {
            categoriesData = categoriesList.map((json) {
              final map = json as Map<String, dynamic>;
              final pictureRaw = map['picture']?.toString();
              final pictureUrl = _buildFullImageUrl(pictureRaw);

              return MenuCategory(
                id: map['_id']?.toString() ?? map['id']?.toString() ?? '',
                name: map['name']?.toString() ?? 'N/A',
                type: int.tryParse(map['type']?.toString() ?? '0') ?? 0,
                picture: pictureUrl,
              );
            }).toList();
          }
        }

        List<MenuProduct> productsData = [];
        final productsResponse = menuData['products'] as Map<String, dynamic>?;
        if (productsResponse != null && productsResponse['data'] is Map) {
          final productsList =
              (productsResponse['data'] as Map)['data'] as List?;
          if (productsList != null) {
            productsData = productsList.map((json) {
              final map = json as Map<String, dynamic>;
              // Use MenuProductModel.fromJson to properly handle image URL building
              final model = MenuProductModel.fromJson(map);
              return model.toEntity();
            }).toList();
          }
        }

        emit(MenuLoaded(
          categories: categoriesData,
          allProducts: productsData,
          filteredProducts: productsData, // Initially, show all products
          selectedCategoryId:
              categoriesData.isNotEmpty ? categoriesData.first.id : null,
        ));
      },
    );
  }

  void _onSelectMenuCategory(
    SelectMenuCategory event,
    Emitter<MenuState> emit,
  ) {
    final currentState = state;
    if (currentState is MenuLoaded) {
      final categoryId = event.categoryId;
      final filteredProducts = categoryId == null
          ? currentState.allProducts
          : currentState.allProducts
              .where((product) => product.categoryId == categoryId)
              .toList();

      emit(currentState.copyWith(
        selectedCategoryId: categoryId,
        filteredProducts: filteredProducts,
        clearSelectedCategory: categoryId == null,
      ));
    }
  }

  /// Build full URL cho image từ relative path (sao chép từ DoUong)
  String? _buildFullImageUrl(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) return null;

    // Nếu đã là full URL, trả về nguyên
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }

    // Firebase Storage URL - giống như Đồ uống
    const imageBaseUrl =
        'https://storage.googleapis.com/lnbvd-75473.firebasestorage.app';

    // Ensure no double slashes và encode URI
    final cleanPath =
        imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;
    final encodedPath = Uri.encodeComponent(cleanPath);
    final fullUrl = '$imageBaseUrl/$encodedPath';

    return fullUrl;
  }
}
