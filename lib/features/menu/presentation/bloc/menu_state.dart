part of 'menu_bloc.dart';

abstract class MenuState extends Equatable {
  const MenuState();

  @override
  List<Object?> get props => [];
}

class MenuInitial extends MenuState {}

class MenuLoading extends MenuState {}

class MenuLoaded extends MenuState {
  final List<MenuCategory> categories;
  final List<MenuProduct> allProducts;
  final List<MenuProduct> filteredProducts;
  final String? selectedCategoryId;

  const MenuLoaded({
    required this.categories,
    required this.allProducts,
    required this.filteredProducts,
    this.selectedCategoryId,
  });

  @override
  List<Object?> get props => [
        categories,
        allProducts,
        filteredProducts,
        selectedCategoryId,
      ];

  MenuLoaded copyWith({
    List<MenuCategory>? categories,
    List<MenuProduct>? allProducts,
    List<MenuProduct>? filteredProducts,
    String? selectedCategoryId,
    bool clearSelectedCategory = false,
  }) {
    return MenuLoaded(
      categories: categories ?? this.categories,
      allProducts: allProducts ?? this.allProducts,
      filteredProducts: filteredProducts ?? this.filteredProducts,
      selectedCategoryId: clearSelectedCategory
          ? null
          : selectedCategoryId ?? this.selectedCategoryId,
    );
  }
}

class MenuError extends MenuState {
  final String message;

  const MenuError({required this.message});

  @override
  List<Object?> get props => [message];
}
