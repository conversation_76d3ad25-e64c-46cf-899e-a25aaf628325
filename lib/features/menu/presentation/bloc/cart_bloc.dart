import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../domain/entities/cart_item.dart';
import '../../domain/entities/menu_product.dart';

part 'cart_event.dart';
part 'cart_state.dart';

/// BLoC quản lý state cho giỏ hàng
class CartBloc extends Bloc<CartEvent, CartState> {
  CartBloc() : super(const CartState.initial()) {
    // Đăng ký event handlers
    on<AddToCart>(_onAddToCart);
    on<RemoveFromCart>(_onRemoveFromCart);
    on<UpdateQuantity>(_onUpdateQuantity);
    on<ClearCart>(_onClearCart);
    on<LoadCart>(_onLoadCart);
  }

  /// Xử lý thêm sản phẩm vào giỏ hàng
  void _onAddToCart(AddToCart event, Emitter<CartState> emit) {
    final currentItems = List<CartItem>.from(state.summary.items);

    // Kiểm tra xem sản phẩm đã có trong giỏ chưa
    final existingIndex = currentItems.indexWhere(
      (item) => item.productId == event.product.id,
    );

    if (existingIndex >= 0) {
      // Nếu đã có, tăng số lượng
      final existingItem = currentItems[existingIndex];
      final updatedItem = existingItem.copyWith(
        quantity: existingItem.quantity + event.quantity,
      );
      currentItems[existingIndex] = updatedItem;
    } else {
      // Nếu chưa có, thêm mới
      final newItem = CartItem(
        productId: event.product.id,
        product: event.product,
        quantity: event.quantity,
        note: event.note,
        addedAt: DateTime.now(),
      );
      currentItems.insert(0, newItem); // Thêm vào đầu danh sách
    }

    final newSummary = CartSummary.fromItems(currentItems);
    emit(state.copyWith(summary: newSummary));
  }

  /// Xử lý xóa sản phẩm khỏi giỏ hàng
  void _onRemoveFromCart(RemoveFromCart event, Emitter<CartState> emit) {
    final currentItems = List<CartItem>.from(state.summary.items);
    currentItems.removeWhere((item) => item.productId == event.productId);

    final newSummary = CartSummary.fromItems(currentItems);
    emit(state.copyWith(summary: newSummary));
  }

  /// Xử lý cập nhật số lượng sản phẩm
  void _onUpdateQuantity(UpdateQuantity event, Emitter<CartState> emit) {
    final currentItems = List<CartItem>.from(state.summary.items);

    final itemIndex = currentItems.indexWhere(
      (item) => item.productId == event.productId,
    );

    if (itemIndex >= 0) {
      if (event.quantity <= 0) {
        // Nếu số lượng <= 0, xóa sản phẩm
        currentItems.removeAt(itemIndex);
      } else {
        // Cập nhật số lượng
        final updatedItem = currentItems[itemIndex].copyWith(
          quantity: event.quantity,
        );
        currentItems[itemIndex] = updatedItem;
      }

      final newSummary = CartSummary.fromItems(currentItems);
      emit(state.copyWith(summary: newSummary));
    }
  }

  /// Xử lý xóa toàn bộ giỏ hàng
  void _onClearCart(ClearCart event, Emitter<CartState> emit) {
    emit(state.copyWith(summary: const CartSummary.empty()));
  }

  /// Xử lý load giỏ hàng (có thể từ storage)
  void _onLoadCart(LoadCart event, Emitter<CartState> emit) {
    // TODO: Implement load from storage if needed
    emit(state.copyWith(summary: const CartSummary.empty()));
  }

  /// Kiểm tra sản phẩm có trong giỏ hàng không
  bool isProductInCart(String productId) {
    return state.summary.items.any((item) => item.productId == productId);
  }

  /// Lấy số lượng của sản phẩm trong giỏ hàng
  int getProductQuantity(String productId) {
    final item = state.summary.items.firstWhere(
      (item) => item.productId == productId,
      orElse: () => CartItem(
        productId: '',
        product: const MenuProduct(
          id: '',
          name: '',
          price: 0,
          thumbnail: '',
          categoryId: '',
          storeId: '',
        ),
        quantity: 0,
        addedAt: DateTime.now(),
      ),
    );
    return item.quantity;
  }
}
