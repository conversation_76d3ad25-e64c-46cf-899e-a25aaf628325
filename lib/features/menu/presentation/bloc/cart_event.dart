part of 'cart_bloc.dart';

/// Base class cho tất cả cart events
abstract class CartEvent extends Equatable {
  const CartEvent();

  @override
  List<Object?> get props => [];
}

/// Event thêm sản phẩm vào giỏ hàng
class AddToCart extends CartEvent {
  /// Sản phẩm cần thêm
  final MenuProduct product;
  
  /// Số lượng thêm vào
  final int quantity;
  
  /// Ghi chú (tùy chọn)
  final String? note;

  const AddToCart({
    required this.product,
    this.quantity = 1,
    this.note,
  });

  @override
  List<Object?> get props => [product, quantity, note];
}

/// Event xóa sản phẩm khỏi giỏ hàng
class RemoveFromCart extends CartEvent {
  /// ID sản phẩm cần xóa
  final String productId;

  const RemoveFromCart({required this.productId});

  @override
  List<Object?> get props => [productId];
}

/// Event cập nhật số lượng sản phẩm trong giỏ hàng
class UpdateQuantity extends CartEvent {
  /// ID sản phẩm cần cập nhật
  final String productId;
  
  /// Số lượng mới
  final int quantity;

  const UpdateQuantity({
    required this.productId,
    required this.quantity,
  });

  @override
  List<Object?> get props => [productId, quantity];
}

/// Event xóa toàn bộ giỏ hàng
class ClearCart extends CartEvent {
  const ClearCart();
}

/// Event load giỏ hàng (từ storage hoặc khởi tạo)
class LoadCart extends CartEvent {
  const LoadCart();
}
