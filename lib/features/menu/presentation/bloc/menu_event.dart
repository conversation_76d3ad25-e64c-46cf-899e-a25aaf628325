part of 'menu_bloc.dart';

abstract class MenuEvent extends Equatable {
  const MenuEvent();

  @override
  List<Object?> get props => [];
}

/// Event để tải dữ liệu menu ban đầu
class FetchMenuData extends MenuEvent {}

/// Event khi chọn một danh mục
class SelectMenuCategory extends MenuEvent {
  final String? categoryId;

  const SelectMenuCategory({this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}
