part of 'cart_bloc.dart';

/// State cho giỏ hàng
class CartState extends Equatable {
  /// Tổng kết giỏ hàng
  final CartSummary summary;
  
  /// Trạng thái loading
  final bool isLoading;
  
  /// Thông báo lỗi
  final String? errorMessage;

  const CartState({
    required this.summary,
    this.isLoading = false,
    this.errorMessage,
  });

  /// Tạo state ban đầu
  const CartState.initial()
      : summary = const CartSummary.empty(),
        isLoading = false,
        errorMessage = null;

  /// Tạo state loading
  CartState loading() {
    return copyWith(isLoading: true, errorMessage: null);
  }

  /// Tạo state error
  CartState error(String message) {
    return copyWith(isLoading: false, errorMessage: message);
  }

  /// Tạo bản sao với các thuộc tính mới
  CartState copyWith({
    CartSummary? summary,
    bool? isLoading,
    String? errorMessage,
  }) {
    return CartState(
      summary: summary ?? this.summary,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  /// Kiểm tra giỏ hàng có rỗng không
  bool get isEmpty => summary.isEmpty;

  /// Kiểm tra giỏ hàng có sản phẩm không
  bool get isNotEmpty => summary.isNotEmpty;

  /// Lấy tổng số lượng sản phẩm
  int get totalQuantity => summary.totalQuantity;

  /// Lấy tổng tiền
  double get totalPrice => summary.totalPrice;

  /// Lấy số lượng items khác nhau
  int get itemCount => summary.itemCount;

  /// Lấy danh sách items
  List<CartItem> get items => summary.items;

  @override
  List<Object?> get props => [summary, isLoading, errorMessage];
}
