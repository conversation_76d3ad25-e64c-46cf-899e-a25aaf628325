import 'package:get_it/get_it.dart';

import '../../../core/network/network_info.dart';
import '../../../services/api_service.dart';
import '../data/datasources/menu_remote_datasource.dart';
import '../data/repositories/menu_repository_impl.dart';
import '../domain/repositories/menu_repository.dart';
import '../domain/usecases/get_menu_data_usecase.dart';
import '../domain/usecases/get_menu_product_detail_usecase.dart';
import '../presentation/bloc/menu_bloc.dart';

final sl = GetIt.instance;

void initMenuFeature() {
  // Bloc
  sl.registerFactory(() => MenuBloc(getMenuDataUseCase: sl()));

  // Use cases
  sl.registerLazySingleton(() => GetMenuDataUseCase(sl()));
  sl.registerLazySingleton(() => GetMenuProductDetailUseCase(sl()));

  // Repository
  sl.registerLazySingleton<MenuRepository>(
    () => MenuRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<MenuRemoteDataSource>(
    () => MenuRemoteDataSourceImpl(apiService: sl()),
  );
}
