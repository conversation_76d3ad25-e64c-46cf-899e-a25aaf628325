import '../../domain/entities/menu_product.dart';

/// Model cho sản phẩm hải sản với khả năng serialize/deserialize JSON
class MenuProductModel extends MenuProduct {
  const MenuProductModel({
    required String id,
    required String name,
    required double price,
    double? priceOld,
    required String thumbnail,
    String? discount,
    required String categoryId,
    required String storeId,
    String? storeName,
    int watched = 0,
    double revenue = 0,
    int status = 1,
  }) : super(
          id: id,
          name: name,
          price: price,
          priceOld: priceOld,
          thumbnail: thumbnail,
          discount: discount,
          categoryId: categoryId,
          storeId: storeId,
          storeName: storeName,
          watched: watched,
          revenue: revenue,
          status: status,
        );

  /// Helper method để build full URL cho ảnh
  static String _buildImageUrl(String imagePath) {
    if (imagePath.isEmpty) return '';
    if (imagePath.startsWith('http')) return imagePath;

    // Base URL của API
    const baseUrl =
        'https://storage.googleapis.com/lnbvd-75473.firebasestorage.app';

    // Ensure no double slashes
    final cleanPath =
        imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;
    final fullUrl = '$baseUrl/$cleanPath';

    return fullUrl;
  }

  /// Tạo model từ JSON response của API
  ///
  /// API response format từ search-v2:
  /// ```json
  /// {
  ///   "_id": "product_id",
  ///   "name": "Tên sản phẩm",
  ///   "price": 100000,
  ///   "priceOld": 120000,
  ///   "thumbail": "image_url",
  ///   "categoryId": "category_id",
  ///   "storeId": "store_id",
  ///   "storeName": "Tên cửa hàng",
  ///   "watched": 100,
  ///   "revenue": 1000000,
  ///   "status": 1
  /// }
  /// ```
  factory MenuProductModel.fromJson(Map<String, dynamic> json) {
    try {
      // Parse price - có thể là int hoặc double
      double parsePrice(dynamic value) {
        if (value == null) return 0.0;
        if (value is double) return value;
        if (value is int) return value.toDouble();
        if (value is String) {
          return double.tryParse(value) ?? 0.0;
        }
        return 0.0;
      }

      final price = parsePrice(json['price']);
      final priceOld =
          json['priceOld'] != null ? parsePrice(json['priceOld']) : null;

      // Tính discount nếu có priceOld
      String? discount;
      if (priceOld != null && priceOld > price) {
        final discountPercent = ((priceOld - price) / priceOld * 100).round();
        discount = discountPercent.toString();
      }

      return MenuProductModel(
        id: json['_id'] as String? ?? '',
        name: json['name'] as String? ?? '',
        price: price,
        priceOld: priceOld,
        thumbnail: _buildImageUrl(json['thumbail'] as String? ??
            json['thumbnail'] as String? ??
            ''), // Handle both "thumbail" (Menu) and "thumbnail" (Service) fields
        discount: discount,
        categoryId: json['categoryId'] as String? ?? '',
        storeId: json['storeId'] as String? ?? '',
        storeName: json['storeName'] as String?,
        watched: json['watched'] as int? ?? 0,
        revenue: parsePrice(json['revenue']),
        status: json['status'] as int? ?? 1,
      );
    } catch (e) {
      throw FormatException('Lỗi parse MenuProductModel: $e');
    }
  }

  /// Chuyển model thành JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'price': price,
      'priceOld': priceOld,
      'thumbail': thumbnail,
      'categoryId': categoryId,
      'storeId': storeId,
      'storeName': storeName,
      'watched': watched,
      'revenue': revenue,
      'status': status,
    };
  }

  /// Tạo entity từ model
  MenuProduct toEntity() {
    return MenuProduct(
      id: id,
      name: name,
      price: price,
      priceOld: priceOld,
      thumbnail: thumbnail,
      discount: discount,
      categoryId: categoryId,
      storeId: storeId,
      storeName: storeName,
      watched: watched,
      revenue: revenue,
      status: status,
    );
  }

  /// Tạo model từ entity
  factory MenuProductModel.fromEntity(MenuProduct entity) {
    return MenuProductModel(
      id: entity.id,
      name: entity.name,
      price: entity.price,
      priceOld: entity.priceOld,
      thumbnail: entity.thumbnail,
      discount: entity.discount,
      categoryId: entity.categoryId,
      storeId: entity.storeId,
      storeName: entity.storeName,
      watched: entity.watched,
      revenue: entity.revenue,
      status: entity.status,
    );
  }

  @override
  String toString() {
    return 'MenuProductModel(id: $id, name: $name, price: $price, thumbnail: $thumbnail)';
  }
}
