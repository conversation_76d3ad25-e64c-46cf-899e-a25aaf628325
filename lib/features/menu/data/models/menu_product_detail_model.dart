import '../../domain/entities/menu_product_detail.dart';

/// Model cho chi tiết sản phẩm <PERSON>u với khả năng serialize/deserialize JSON
class MenuProductDetailModel extends MenuProductDetail {
  const MenuProductDetailModel({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    super.priceOld,
    required super.thumbnail,
    super.pictures = const [],
    required super.categoryId,
    required super.storeId,
    super.storeName,
    super.watched = 0,
    super.revenue = 0,
    super.status = 1,
    super.trademark,
    super.code = 0,
    super.transport,
    super.typeShip = 0,
    super.weight = 0,
    super.classify = const [],
  });

  /// Helper method to build full URL for an image
  static String _buildImageUrl(String imagePath) {
    if (imagePath.isEmpty) return '';
    if (imagePath.startsWith('http')) return imagePath;

    const baseUrl =
        'https://storage.googleapis.com/lnbvd-75473.firebasestorage.app';

    final cleanPath =
        imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;
    return '$baseUrl/$cleanPath';
  }

  /// Tạo model từ JSON response của API
  ///
  /// API response format từ chi-tiet-san-pham:
  /// ```json
  /// {
  ///   "_id": "product_id",
  ///   "name": "Tên sản phẩm",
  ///   "description": "Mô tả chi tiết",
  ///   "price": 100000,
  ///   "priceOld": 120000,
  ///   "thumbail": "image_url",
  ///   "pictures": ["url1", "url2"],
  ///   "categoryId": "category_id",
  ///   "storeId": "store_id",
  ///   "storeName": "Tên cửa hàng",
  ///   "watched": 100,
  ///   "revenue": 1000000,
  ///   "status": 1,
  ///   "trademark": "Thương hiệu",
  ///   "code": 12345,
  ///   "transport": "Giao hàng nhanh",
  ///   "typeShip": 1,
  ///   "weight": 1.5,
  ///   "classify": [...]
  /// }
  /// ```
  factory MenuProductDetailModel.fromJson(Map<String, dynamic> json) {
    try {
      // Parse price - có thể là int hoặc double
      double parsePrice(dynamic value) {
        if (value == null) return 0.0;
        if (value is double) return value;
        if (value is int) return value.toDouble();
        if (value is String) {
          return double.tryParse(value) ?? 0.0;
        }
        return 0.0;
      }

      // Parse pictures array - API mới đã trả về URL đầy đủ
      List<String> parsePictures(dynamic value) {
        if (value == null) return [];
        if (value is List) {
          return value.map((e) {
            final url = e.toString();
            // Nếu đã là URL đầy đủ thì dùng luôn, không thì build URL
            return url.startsWith('http') ? url : _buildImageUrl(url);
          }).toList();
        }
        return [];
      }

      // Parse classify array
      List<ProductClassification> parseClassify(dynamic value) {
        if (value == null) return [];
        if (value is List) {
          return value
              .map((item) => ProductClassificationModel.fromJson(item))
              .toList();
        }
        return [];
      }

      return MenuProductDetailModel(
        id: json['_id'] as String? ?? '',
        name: json['name'] as String? ?? '',
        description: json['description'] as String? ?? '',
        price: parsePrice(json['price']),
        priceOld:
            json['priceOld'] != null ? parsePrice(json['priceOld']) : null,
        // API mới trả về thumbailUrl và picturesUrls
        thumbnail: json['thumbailUrl'] as String? ??
            json['thumbail'] as String? ??
            '', // fallback cho API cũ
        pictures: parsePictures(json['picturesUrls'] ?? json['pictures']),
        categoryId: json['categoryId'] as String? ?? '',
        storeId: json['storeId'] as String? ?? '',
        storeName: json['storeName'] as String?,
        watched: json['watched'] as int? ?? 0,
        revenue: parsePrice(json['revenue']),
        status: json['status'] as int? ?? 1,
        trademark: json['trademark'] as String?,
        code: json['code'] as int? ?? 0,
        transport: json['transport'] as String?,
        typeShip: json['typeShip'] as int? ?? 0,
        weight: parsePrice(json['weight']),
        classify: parseClassify(json['classify']),
      );
    } catch (e) {
      throw FormatException('Lỗi parse MenuProductDetailModel: $e');
    }
  }

  /// Chuyển model thành JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'description': description,
      'price': price,
      'priceOld': priceOld,
      'thumbail': thumbnail,
      'pictures': pictures,
      'categoryId': categoryId,
      'storeId': storeId,
      'storeName': storeName,
      'watched': watched,
      'revenue': revenue,
      'status': status,
      'trademark': trademark,
      'code': code,
      'transport': transport,
      'typeShip': typeShip,
      'weight': weight,
      'classify': classify
          .map((c) => (c as ProductClassificationModel).toJson())
          .toList(),
    };
  }

  /// Tạo entity từ model
  MenuProductDetail toEntity() {
    return MenuProductDetail(
      id: id,
      name: name,
      description: description,
      price: price,
      priceOld: priceOld,
      thumbnail: thumbnail,
      pictures: pictures,
      categoryId: categoryId,
      storeId: storeId,
      storeName: storeName,
      watched: watched,
      revenue: revenue,
      status: status,
      trademark: trademark,
      code: code,
      transport: transport,
      typeShip: typeShip,
      weight: weight,
      classify: classify,
    );
  }
}

/// Model cho phân loại sản phẩm
class ProductClassificationModel extends ProductClassification {
  const ProductClassificationModel({
    required super.id,
    required super.name,
    required super.data,
  });

  factory ProductClassificationModel.fromJson(Map<String, dynamic> json) {
    try {
      List<ClassificationOption> parseData(dynamic value) {
        if (value == null) return [];
        if (value is List) {
          return value
              .map((item) => ClassificationOptionModel.fromJson(item))
              .toList();
        }
        return [];
      }

      return ProductClassificationModel(
        id: json['_id'] as String? ?? '',
        name: json['name'] as String? ?? '',
        data: parseData(json['data']),
      );
    } catch (e) {
      throw FormatException('Lỗi parse ProductClassificationModel: $e');
    }
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'data':
          data.map((d) => (d as ClassificationOptionModel).toJson()).toList(),
    };
  }
}

/// Model cho lựa chọn trong phân loại
class ClassificationOptionModel extends ClassificationOption {
  const ClassificationOptionModel({
    required super.id,
    required super.name,
    required super.price,
    super.priceOld,
    required super.mass,
  });

  factory ClassificationOptionModel.fromJson(Map<String, dynamic> json) {
    try {
      return ClassificationOptionModel(
        id: json['_id'] as String? ?? '',
        name: json['name'] as String? ?? '',
        price: json['price'] as String? ?? '0',
        priceOld: json['priceOld'] as String?,
        mass: json['mass'] as String? ?? '',
      );
    } catch (e) {
      throw FormatException('Lỗi parse ClassificationOptionModel: $e');
    }
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'price': price,
      'priceOld': priceOld,
      'mass': mass,
    };
  }
}
