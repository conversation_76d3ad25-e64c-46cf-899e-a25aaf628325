import '../../domain/entities/menu_category.dart';

/// Model cho danh mục sản phẩm với khả năng serialize/deserialize JSON
class MenuCategoryModel extends MenuCategory {
  const MenuCategoryModel({
    required super.id,
    required super.name,
    required super.type,
    super.picture,
    super.isSelected = false,
  });

  /// Tạo model từ JSON response của API
  factory MenuCategoryModel.fromJson(Map<String, dynamic> json) {
    try {
      return MenuCategoryModel(
        id: json['_id'] as String? ?? '',
        name: json['name'] as String? ?? '',
        type: _parseIntFromDynamic(json['type']) ?? 0,
        picture: json['picture'] as String?,
        isSelected: false, // Mặc định không được chọn
      );
    } catch (e) {
      throw FormatException('Lỗi parse MenuCategoryModel: $e');
    }
  }

  /// Helper method để parse int từ dynamic (có thể là String hoặc int)
  static int? _parseIntFromDynamic(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    return null;
  }

  /// Chuyển model thành JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'type': type,
      'picture': picture,
    };
  }

  /// Tạo bản sao với các thuộc tính được cập nhật
  @override
  MenuCategoryModel copyWith({
    String? id,
    String? name,
    int? type,
    String? picture,
    bool? isSelected,
  }) {
    return MenuCategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      picture: picture ?? this.picture,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  /// Tạo entity từ model
  MenuCategory toEntity() {
    return MenuCategory(
      id: id,
      name: name,
      type: type,
      picture: picture,
      isSelected: isSelected,
    );
  }

  /// Tạo model từ entity
  factory MenuCategoryModel.fromEntity(MenuCategory entity) {
    return MenuCategoryModel(
      id: entity.id,
      name: entity.name,
      type: entity.type,
      picture: entity.picture,
      isSelected: entity.isSelected,
    );
  }

  @override
  String toString() {
    return 'MenuCategoryModel(id: $id, name: $name, type: $type, picture: $picture, isSelected: $isSelected)';
  }
}
