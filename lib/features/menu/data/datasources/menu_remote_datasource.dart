import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'dart:io';

import '../../../../core/errors/exceptions.dart';
import '../../../../services/api_service.dart';
import '../models/menu_product_detail_model.dart';

/// Remote data source cho tính năng Menu
///
/// Thực hiện các API calls để lấy dữ liệu từ server
abstract class MenuRemoteDataSource {
  /// Lấy danh sách sản phẩm từ API
  Future<Map<String, dynamic>> getMenuProducts();

  /// Lấy danh sách danh mục từ API
  Future<Map<String, dynamic>> getMenuCategories();

  /// Lấy tất cả dữ liệu menu (danh mục và sản phẩm) - method tương thích với code cũ
  Future<Map<String, dynamic>> getMenuData();

  /// Lấy chi tiết sản phẩm theo ID
  Future<MenuProductDetailModel> getProductDetail(String productId);
}

/// Implementation của MenuRemoteDataSource
class MenuRemoteDataSourceImpl implements MenuRemoteDataSource {
  final ApiService apiService;
  late final Dio _menuDio;

  MenuRemoteDataSourceImpl({required this.apiService}) {
    // Tạo Dio instance riêng cho API Menu
    _menuDio = Dio(BaseOptions(
      baseUrl: 'https://api.langnuoibienvandon.com',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
      sendTimeout: const Duration(seconds: 60),
    ));

    // Bypass SSL certificate validation for development
    (_menuDio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      final client = HttpClient();
      client.badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
      return client;
    };
  }

  @override
  Future<Map<String, dynamic>> getMenuProducts() async {
    try {
      final response = await _menuDio
          .get('/v1/user/api/services-of-brand/623a9950be781e0ba814f22a/menu');

      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;

        return data;
      } else {
        throw ServerException(
            message:
                'Lỗi tải danh sách sản phẩm: ${response.statusCode} ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw ServerException(message: _handleDioError(e));
    } catch (e) {
      throw ServerException(
          message: 'Lỗi không xác định khi lấy danh sách sản phẩm: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getMenuCategories() async {
    try {
      final response =
          await _menuDio.get('/v1/user/api/categories-by-type?cat=0');

      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        return response.data as Map<String, dynamic>;
      } else {
        throw ServerException(
            message:
                'Lỗi tải danh mục: ${response.statusCode} ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw ServerException(message: _handleDioError(e));
    } catch (e) {
      throw ServerException(message: 'Lỗi không xác định khi lấy danh mục: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getMenuData() async {
    try {
      // Gọi products API trước
      final productsResponse = await getMenuProducts();

      // Thử gọi categories API, nhưng không fail nếu lỗi
      Map<String, dynamic>? categoriesResponse;
      try {
        categoriesResponse = await getMenuCategories();
      } catch (e) {
        // Tạo categories từ products data thay vì fail
        categoriesResponse = _createCategoriesFromProducts(productsResponse);
      }

      // Kết hợp dữ liệu từ hai API
      return {
        'products': productsResponse,
        'categories': categoriesResponse,
      };
    } catch (e) {
      throw ServerException(message: 'Lỗi không xác định khi lấy menu: $e');
    }
  }

  /// Tạo categories từ products data khi categories API fail
  Map<String, dynamic> _createCategoriesFromProducts(
      Map<String, dynamic> productsResponse) {
    try {
      final productsData = productsResponse['data']?['data'] as List?;
      if (productsData == null) {
        return {
          'data': {'categories': []}
        };
      }

      // Extract unique categories từ products
      final Map<String, Map<String, dynamic>> uniqueCategories = {};

      for (final product in productsData) {
        final categoryId = product['categoryId']?.toString();
        final categoryName = product['categoryName']?.toString();

        if (categoryId != null &&
            categoryName != null &&
            categoryId.isNotEmpty) {
          uniqueCategories[categoryId] = {
            '_id': categoryId,
            'name': categoryName,
            'type': 0,
            'picture': null, // Không có ảnh từ products data
          };
        }
      }

      // Thêm category "Tất cả" ở đầu
      final allCategories = [
        {
          '_id': '',
          'name': 'Tất cả',
          'type': 0,
          'picture': null,
        },
        ...uniqueCategories.values.toList(),
      ];

      return {
        'data': {
          'categories': allCategories,
        }
      };
    } catch (e) {
      return {
        'data': {'categories': []}
      };
    }
  }

  @override
  Future<MenuProductDetailModel> getProductDetail(String productId) async {
    try {
      // Sử dụng URL chuẩn mới cho chi tiết sản phẩm
      final response = await _menuDio.get(
        '/v1/user/api/classify-detail/$productId',
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data['data'] ?? response.data;
        // API trả về dữ liệu sản phẩm trong key 'classify'
        final productData = responseData['classify'] ?? responseData;

        print('🔍 DEBUG: Product data to parse: $productData');

        return MenuProductDetailModel.fromJson(productData);
      } else {
        throw ServerException(
            message:
                'Lỗi tải chi tiết sản phẩm: ${response.statusCode} ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw ServerException(message: _handleDioError(e));
    } catch (e) {
      throw ServerException(
          message: 'Lỗi không xác định khi lấy chi tiết sản phẩm: $e');
    }
  }

  /// Xử lý lỗi từ Dio
  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Kết nối timeout. Vui lòng thử lại.';
      case DioExceptionType.sendTimeout:
        return 'Gửi dữ liệu timeout. Vui lòng thử lại.';
      case DioExceptionType.receiveTimeout:
        return 'Nhận dữ liệu timeout. Vui lòng thử lại.';
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        if (statusCode == 404) {
          return 'Không tìm thấy dữ liệu.';
        } else if (statusCode == 500) {
          return 'Lỗi server. Vui lòng thử lại sau.';
        }
        return 'Lỗi server: ${e.response?.statusMessage ?? 'Không xác định'}';
      case DioExceptionType.cancel:
        return 'Yêu cầu đã bị hủy.';
      case DioExceptionType.connectionError:
        return 'Lỗi kết nối. Vui lòng kiểm tra mạng.';
      default:
        return 'Lỗi không xác định: ${e.message}';
    }
  }
}
