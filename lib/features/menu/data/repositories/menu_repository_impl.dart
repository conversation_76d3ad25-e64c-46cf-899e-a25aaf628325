import 'package:dartz/dartz.dart';

import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/repositories/menu_repository.dart';
import '../../domain/entities/menu_product_detail.dart';
import '../datasources/menu_remote_datasource.dart';

/// Implementation của MenuRepository
///
/// Xử lý logic business và error handling cho tính năng Menu
class MenuRepositoryImpl implements MenuRepository {
  final MenuRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  MenuRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, Map<String, dynamic>>> getMenuData() async {
    if (await networkInfo.isConnected) {
      try {
        final menuData = await remoteDataSource.getMenuData();
        return Right(menuData);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Lỗi không xác định: $e'));
      }
    } else {
      return Left(const NetworkFailure(message: 'Không có kết nối mạng'));
    }
  }

  @override
  Future<Either<Failure, MenuProductDetail>> getProductDetail(
      String productId) async {
    if (await networkInfo.isConnected) {
      try {
        final productDetail =
            await remoteDataSource.getProductDetail(productId);
        return Right(productDetail);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Lỗi không xác định: $e'));
      }
    } else {
      return Left(const NetworkFailure(message: 'Không có kết nối mạng'));
    }
  }
}
