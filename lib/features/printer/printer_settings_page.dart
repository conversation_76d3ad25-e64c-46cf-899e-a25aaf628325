import 'package:flutter/material.dart';
import '../../models/bluetooth_device_simple.dart';
import '../../services/native_printer_service.dart';
import '../../core/theme/pos_colors.dart';
import '../../core/theme/pos_typography.dart';
import 'package:shared_preferences/shared_preferences.dart';


class PrinterSettingsPage extends StatefulWidget {
  const PrinterSettingsPage({super.key});

  @override
  State<PrinterSettingsPage> createState() => _PrinterSettingsPageState();
}

class _PrinterSettingsPageState extends State<PrinterSettingsPage> {
  final NativePrinterService _nativePrinterService = NativePrinterService();

  List<BluetoothDeviceSimple> _discoveredPrinters = [];
  bool _isScanning = false;
  bool _isConnecting = false;
  String _statusMessage = 'Chưa kết nối máy in';

  bool _isConnected = false;
  BluetoothDeviceSimple? _connectedDevice;

  @override
  void initState() {
    super.initState();
    _loadAndConnectPrinter();
  }

  Future<void> _savePrinter(String name, String address) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('printer_name', name);
    await prefs.setString('printer_address', address);
  }

  Future<void> _loadAndConnectPrinter() async {
    final prefs = await SharedPreferences.getInstance();
    final name = prefs.getString('printer_name');
    final address = prefs.getString('printer_address');

    if (name == null || address == null) {
      setState(() {
        _statusMessage = 'Chưa kết nối máy in';
      });
      return;
    }

    final savedDevice = BluetoothDeviceSimple(name: name, address: address);

    try {
      final bool alreadyConnected = await _nativePrinterService.isConnected();
      if (alreadyConnected) {
        // Nếu native đã kết nối, chỉ cần cập nhật UI
        setState(() {
          _isConnected = true;
          _connectedDevice = savedDevice;
          _statusMessage = 'Đã kết nối: ${savedDevice.name}';
        });
      } else {
        // Nếu chưa, thực hiện kết nối lại
        await _connectToPrinter(savedDevice, isAutoConnect: true);
      }
    } catch (e) {
      // Xử lý lỗi nếu không thể kiểm tra hoặc kết nối
      setState(() {
        _statusMessage = 'Lỗi kết nối lại: ${e.toString()}';
      });
    }
  }



  Future<void> _scanForPrinters() async {
    setState(() {
      _isScanning = true;
      _discoveredPrinters.clear();
    });

    try {
      final printers = await _nativePrinterService.getBondedDevices();
      setState(() {
        _discoveredPrinters = printers;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tìm thấy ${printers.length} máy in đã ghép nối'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi tìm kiếm: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isScanning = false;
        });
      }
    }
  }

  Future<void> _connectToPrinter(BluetoothDeviceSimple printer, {bool isAutoConnect = false}) async {
    setState(() {
      _isConnecting = true;
      if (isAutoConnect) {
        _statusMessage = 'Đang tự động kết nối đến ${printer.name}...';
      }
    });

    try {
      await _nativePrinterService.connect(printer.address);
      await _savePrinter(printer.name, printer.address);
      setState(() {
        _isConnected = true;
        _connectedDevice = printer;
        _statusMessage = 'Đã kết nối: ${printer.name}';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Kết nối thành công: ${printer.name}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isConnected = false;
        _connectedDevice = null;
        _statusMessage = 'Lỗi kết nối';
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi kết nối: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isConnecting = false;
        });
      }
    }
  }

  Future<void> _disconnect() async {
    try {
      await _nativePrinterService.disconnect();

    } catch (e) {
      // Log or ignore
    }
    setState(() {
      _isConnected = false;
      _connectedDevice = null;
      _statusMessage = 'Chưa kết nối máy in';
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Đã ngắt kết nối máy in'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  Future<void> _testPrintTSPL() async {
    if (!_isConnected) return;
    try {
      await _nativePrinterService.testPrintTSPL();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã gửi lệnh TSPL!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi test TSPL: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _testPrintCPCL() async {
    if (!_isConnected) return;
    try {
      await _nativePrinterService.testPrintCPCL();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã gửi lệnh CPCL!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi test CPCL: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }




  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cài đặt máy in'),
        backgroundColor: PosColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Trạng thái máy in',
                      style: PosTypography.headingMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _isConnected
                              ? Icons.print
                              : Icons.print_disabled,
                          color: _isConnected
                              ? Colors.green
                              : Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _statusMessage,
                            style: PosTypography.bodyMedium,
                          ),
                        ),
                      ],
                    ),
                    if (_isConnected) ...[
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _testPrintTSPL,
                              icon: const Icon(Icons.label),
                              label: const Text('Test TSPL'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _testPrintCPCL,
                              icon: const Icon(Icons.receipt),
                              label: const Text('Test CPCL'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _disconnect,
                              icon: const Icon(Icons.bluetooth_disabled),
                              label: const Text('Ngắt kết nối'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Scan Section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Máy in Bluetooth',
                  style: PosTypography.headingMedium,
                ),
                ElevatedButton.icon(
                  onPressed: _isScanning ? null : _scanForPrinters,
                  icon: _isScanning
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.search),
                  label: Text(_isScanning ? 'Đang tìm...' : 'Tìm máy in'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: PosColors.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Printers List
            Expanded(
              child: _discoveredPrinters.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.bluetooth_searching,
                            size: 64,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Nhấn "Tìm kiếm" để lấy danh sách máy in đã ghép nối',
                            style: PosTypography.bodyMedium.copyWith(
                              color: Colors.grey.shade600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _discoveredPrinters.length,
                      itemBuilder: (context, index) {
                        final printer = _discoveredPrinters[index];
                        final isCurrentPrinter = _isConnected &&
                            _connectedDevice?.address == printer.address;

                        return Card(
                          child: ListTile(
                            leading: Icon(
                              isCurrentPrinter
                                  ? Icons.print
                                  : Icons.print_outlined,
                              color:
                                  isCurrentPrinter ? Colors.green : Colors.grey,
                            ),
                            title: Text(printer.name),
                            subtitle: Text(printer.address),
                            trailing: isCurrentPrinter
                                ? const Chip(
                                    label: Text('Đã kết nối'),
                                    backgroundColor: Colors.green,
                                    labelStyle: TextStyle(color: Colors.white),
                                  )
                                : ElevatedButton(
                                    onPressed: _isConnecting
                                        ? null
                                        : () => _connectToPrinter(printer),
                                    child: _isConnecting
                                        ? const SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(
                                                strokeWidth: 2),
                                          )
                                        : const Text('Kết nối'),
                                  ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
