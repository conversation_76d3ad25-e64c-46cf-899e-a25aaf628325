part of 'search_bloc.dart';

abstract class SearchEvent extends Equatable {
  const SearchEvent();

  @override
  List<Object?> get props => [];
}

class SearchQueryChanged extends SearchEvent {
  final String query;

  const SearchQueryChanged(this.query);

  @override
  List<Object?> get props => [query];
}

class SearchFilterApplied extends SearchEvent {
  final SearchFilter filter;

  const SearchFilterApplied(this.filter);

  @override
  List<Object?> get props => [filter];
}

class SearchTabChanged extends SearchEvent {
  final int tabIndex;

  const SearchTabChanged(this.tabIndex);

  @override
  List<Object?> get props => [tabIndex];
}

class LoadCategories extends SearchEvent {
  final String? propertyType;

  const LoadCategories({this.propertyType});

  @override
  List<Object?> get props => [propertyType];
}

class PropertyTypeSelected extends SearchEvent {
  final String propertyType;

  const PropertyTypeSelected(this.propertyType);

  @override
  List<Object?> get props => [propertyType];
}

class LoadServiceCategories extends SearchEvent {}

class LoadMoreResults extends SearchEvent {}

class ClearFilter extends SearchEvent {}

class HideSearchResult extends SearchEvent {
  final String resultId;
  final int tabIndex; // 0 for properties, 1 for services

  const HideSearchResult(this.resultId, this.tabIndex);

  @override
  List<Object?> get props => [resultId, tabIndex];
}

class ShowSearchResult extends SearchEvent {
  final String resultId;
  final int tabIndex; // 0 for properties, 1 for services

  const ShowSearchResult(this.resultId, this.tabIndex);

  @override
  List<Object?> get props => [resultId, tabIndex];
}

class ToggleHiddenResultsVisibility extends SearchEvent {}
