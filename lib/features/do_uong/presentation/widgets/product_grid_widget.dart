import 'package:flutter/material.dart';

import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../domain/entities/do_uong_product.dart';
import '../../../../src/common/widgets/product_item_widget.dart';

/// Widget hiển thị grid sản phẩm đồ uống với responsive design
class ProductGridWidget extends StatelessWidget {
  /// Danh sách sản phẩm
  final List<DoUongProduct> products;

  /// Callback khi tap vào sản phẩm
  final Function(DoUongProduct product)? onProductTap;

  /// Callback khi thêm vào giỏ hàng
  final Function(DoUongProduct product)? onAddToCart;

  /// <PERSON><PERSON> đang loading không
  final bool isLoading;

  /// ScrollController để handle pagination
  final ScrollController? scrollController;

  const ProductGridWidget({
    Key? key,
    required this.products,
    this.onProductTap,
    this.onAddToCart,
    this.isLoading = false,
    this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (products.isEmpty && !isLoading) {
      return const _EmptyProductsWidget();
    }

    return GridView.builder(
      controller: scrollController,
      padding: const EdgeInsets.symmetric(
        horizontal: PosSpacing.md + 16,
        vertical: PosSpacing.md,
      ),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4, // Thay đổi từ 3 thành 4 cột
        mainAxisExtent: 240, // Tăng thêm một chút để tránh overflow
        crossAxisSpacing: 16, // Giảm khoảng cách ngang để phù hợp với 4 cột
        mainAxisSpacing: 24, // Giảm khoảng cách dọc
      ),
      itemCount: products.length + (isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= products.length) {
          // Loading indicator at the end
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final product = products[index];
        return ProductItemWidget(
          title: product.name,
          imageUrl: product.thumbnail,
          price: product.formattedPrice,
          oldPrice: product.formattedPriceOld,
          discount: product.discount,
          onTap: () => onProductTap?.call(product),
          onAddToCart: () => onAddToCart?.call(product),
        );
      },
    );
  }
}

/// Widget hiển thị khi không có sản phẩm
class _EmptyProductsWidget extends StatelessWidget {
  const _EmptyProductsWidget();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_drink_outlined,
            size: 80,
            color: PosColors.textSecondary,
          ),
          const SizedBox(height: PosSpacing.lg),
          Text(
            'Không có đồ uống nào',
            style: PosTypography.headingMedium.copyWith(
              color: PosColors.textSecondary,
            ),
          ),
          const SizedBox(height: PosSpacing.sm),
          Text(
            'Thử thay đổi bộ lọc hoặc tìm kiếm khác',
            style: PosTypography.bodyMedium.copyWith(
              color: PosColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
