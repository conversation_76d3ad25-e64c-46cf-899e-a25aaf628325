import 'package:get_it/get_it.dart';

import '../data/datasources/ticket_remote_datasource.dart';
import '../data/repositories/ticket_repository_impl.dart';
import '../domain/repositories/ticket_repository.dart';
import '../domain/usecases/get_categories_usecase.dart';
import '../domain/usecases/get_products_usecase.dart';
import '../domain/usecases/get_product_detail_usecase.dart';
import '../domain/usecases/create_ticket_order_usecase.dart';
import '../presentation/bloc/ticket_bloc.dart';
import '../presentation/bloc/cart_bloc.dart';
import '../../payment/presentation/bloc/payment_bloc.dart';

final sl = GetIt.instance;

/// Khởi tạo dependency injection cho tính năng Ticket
void initTicketInjection() {
  // ============================================================================
  // PRESENTATION LAYER
  // ============================================================================

  // BLoC - Factory registration để tạo instance mới mỗi lần cần
  sl.registerFactory(
    () => TicketBloc(
      getCategoriesUseCase: sl(),
      getProductsUseCase: sl(),
    ),
  );

  // CartBloc - Factory registration để tạo instance mới mỗi lần cần
  sl.registerFactory(() => CartBloc());

  // PaymentBloc - Factory registration
  sl.registerFactory(
    () => PaymentBloc(
      ticketRepository: sl(),
      cartBloc: sl(),
    ),
  );

  // ============================================================================
  // DOMAIN LAYER
  // ============================================================================

  // Use Cases - Lazy Singleton
  sl.registerLazySingleton(
    () => GetCategoriesUseCase(sl()),
  );
  sl.registerLazySingleton(() => GetProductsUseCase(sl()));
  sl.registerLazySingleton(() => GetProductDetailUseCase(sl()));
  sl.registerLazySingleton(() => CreateTicketOrderUseCase(sl()));

  // ============================================================================
  // DATA LAYER
  // ============================================================================

  // Repository Implementation - Lazy Singleton
  sl.registerLazySingleton<TicketRepository>(
    () => TicketRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(), // Đã được register trong core
    ),
  );

  // Remote Data Source - Lazy Singleton
  sl.registerLazySingleton<TicketRemoteDataSource>(
    () => TicketRemoteDataSourceImpl(apiService: sl()),
  );
}
