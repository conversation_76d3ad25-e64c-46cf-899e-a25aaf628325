import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/di/injection_container.dart';
import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../../../core/services/pos_navigation_service.dart';
import '../bloc/cart_bloc.dart';
import '../widgets/product_detail_dialog.dart';
import '../widgets/cart_item_widget.dart';
import '../../domain/entities/cart_item.dart';
import '../bloc/ticket_bloc.dart';
import '../bloc/ticket_event.dart';
import '../bloc/ticket_state.dart';
import '../widgets/category_filter_widget.dart';
import '../widgets/product_grid_widget.dart';
import '../../../../core/network/api_client.dart';
import '../../../../core/di/injection_container.dart' as di;

/// Trang chính của tính năng Ticket
class TicketPage extends StatelessWidget {
  const TicketPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<TicketBloc>()..add(const LoadInitialData()),
      child: const _TicketPageContent(),
    );
  }
}

/// Content của TicketPage
class _TicketPageContent extends StatefulWidget {
  const _TicketPageContent({Key? key}) : super(key: key);

  @override
  State<_TicketPageContent> createState() => _TicketPageContentState();
}

class _TicketPageContentState extends State<_TicketPageContent> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      context.read<TicketBloc>().add(const LoadMoreProducts());
    }
  }

  void _onProductClick(product) {
    print('Product clicked: ${product.name}');

    try {
      // Kiểm tra xem CartBloc có còn hoạt động không
      final cartBloc = context.read<CartBloc>();
      if (!cartBloc.isClosed) {
        // Thêm nhanh vào giỏ hàng với quantity = 1
        cartBloc.add(AddToCart(
          product: product,
          quantity: 1,
          note: null,
        ));

        // Hiển thị snackbar thông báo
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Đã thêm ${product.name} vào giỏ hàng'),
              duration: const Duration(seconds: 1),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        print('⚠️ CartBloc is closed, cannot add product to cart');
      }
    } catch (e) {
      print('❌ Error adding product to cart: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi thêm ${product.name} vào giỏ hàng'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onOrderClick() {
    // TODO: Implement order functionality
    print('Order clicked');
  }

  // Test API call với ApiClient trực tiếp
  void _testApiCall() async {
    print('🧪 Testing API call with ApiClient directly...');

    try {
      // Get ApiClient từ DI
      final apiClient = sl<ApiClient>();

      // Test data đơn giản
      final testData = {
        'products': [
          {
            'id': '68a54cd0e38b0978342f4b0b',
            'name': 'Test Ticket',
            'price': 30000.0,
            'quantity': 1,
            'thumbnail': '',
            'note': '',
          }
        ],
        'customerName': 'Test User',
        'phone': '**********',
        'totalAmount': 30000.0,
        'email': '<EMAIL>',
        'address': '123 Test Address',
        'paymentMethod': 1,
        'bankCode': 'BAOKIM',
        'ticketType': 'tour',
        'eventLocation': 'Test Location',
      };

      print('🧪 Calling API with test data...');
      final response = await apiClient.post(
        'user/api/create-ticket-order?output=json',
        data: testData,
      );

      print('🧪 API Response: ${response.data}');

      // Hiển thị kết quả
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Test API: ${response.data['error'] == false ? 'SUCCESS' : 'FAILED'}'),
          backgroundColor:
              response.data['error'] == false ? Colors.green : Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      print('🧪 Test API Error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Test API Error: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PosColors.background,
      body: BlocBuilder<TicketBloc, TicketState>(
        builder: (context, state) {
          if (state is TicketLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is TicketError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: PosColors.error,
                  ),
                  const SizedBox(height: PosSpacing.lg),
                  Text(
                    'Có lỗi xảy ra',
                    style: PosTypography.headingSmall.copyWith(
                      color: PosColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: PosSpacing.md),
                  Text(
                    state.message,
                    style: PosTypography.bodyMedium.copyWith(
                      color: PosColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: PosSpacing.lg),
                  ElevatedButton(
                    onPressed: () {
                      context.read<TicketBloc>().add(const RetryLoading());
                    },
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            );
          }

          if (state is TicketLoaded) {
            return Row(
              children: [
                // Main Content Area (Left)
                Expanded(
                  flex: 3,
                  child: Column(
                    children: [
                      // Horizontal Categories
                      Container(
                        height: 110, // Tăng thêm để phù hợp với text 2 dòng
                        padding: const EdgeInsets.symmetric(
                            horizontal: PosSpacing.md),
                        decoration: BoxDecoration(
                          color: PosColors.surface,
                          border: Border(
                            bottom: BorderSide(color: PosColors.borderLight),
                          ),
                        ),
                        child: CategoryFilterWidget(
                          categories: state.categories,
                          selectedCategoryId: state.selectedCategoryId,
                          onCategorySelected: (categoryId) {
                            context.read<TicketBloc>().add(
                                  SelectCategory(categoryId),
                                );
                          },
                        ),
                      ),

                      // Products Grid
                      Expanded(
                        child: ProductGridWidget(
                          products: state.products,
                          isLoading: state.isLoadingMore,
                          scrollController: _scrollController,
                          onProductTap: _onProductClick,
                          onAddToCart: (product) {
                            try {
                              final cartBloc = context.read<CartBloc>();
                              if (!cartBloc.isClosed) {
                                cartBloc.add(AddToCart(product: product));
                              } else {
                                print(
                                    '⚠️ CartBloc is closed, cannot add product to cart');
                              }
                            } catch (e) {
                              print('❌ Error adding product to cart: $e');
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // Cart Sidebar (Right)
                Container(
                  width: 350,
                  decoration: BoxDecoration(
                    color: PosColors.surface,
                    border: Border(
                      left: BorderSide(color: PosColors.borderLight),
                    ),
                  ),
                  child: _CartSidebar(),
                ),
              ],
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}

/// Widget hiển thị giỏ hàng bên phải
class _CartSidebar extends StatefulWidget {
  @override
  State<_CartSidebar> createState() => _CartSidebarState();
}

class _CartSidebarState extends State<_CartSidebar> {
  bool _isProcessingPayment = false;

  void _onCartItemClick(CartItem cartItem) async {
    print('Cart item clicked: ${cartItem.product.name}');

    // Hiển thị dialog chi tiết sản phẩm từ cart item
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => ProductDetailDialog(
        productId: cartItem.productId,
        initialProduct: cartItem.product,
      ),
    );

    // Xử lý kết quả trả về từ dialog
    if (result != null && result['action'] == 'add_to_cart' && mounted) {
      final productToAdd = result['product'];
      final quantity = result['quantity'] as int;
      final note = result['note'] as String?;

      // Thêm vào cart thông qua CartBloc
      try {
        final cartBloc = context.read<CartBloc>();
        if (!cartBloc.isClosed) {
          cartBloc.add(AddToCart(
            product: productToAdd,
            quantity: quantity,
            note: note,
          ));
        } else {
          print('⚠️ CartBloc is closed, cannot add product to cart');
        }
      } catch (e) {
        print('❌ Error adding product to cart: $e');
      }

      // Hiển thị snackbar thông báo
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Đã thêm ${productToAdd.name} vào giỏ hàng'),
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // Test API call với ApiClient trực tiếp
  void _testApiCall() async {
    print('🧪 Testing API call with ApiClient directly...');

    try {
      // Get ApiClient từ DI
      final apiClient = di.sl<ApiClient>();

      // Test data đơn giản
      final testData = {
        'products': [
          {
            'id': '68a54cd0e38b0978342f4b0b',
            'name': 'Test Ticket',
            'price': 30000.0,
            'quantity': 1,
            'thumbnail': '',
            'note': '',
          }
        ],
        'customerName': 'Test User',
        'phone': '**********',
        'totalAmount': 30000.0,
        'email': '<EMAIL>',
        'address': '123 Test Address',
        'paymentMethod': 1,
        'bankCode': 'BAOKIM',
        'ticketType': 'tour',
        'eventLocation': 'Test Location',
      };

      print('🧪 Calling API with test data...');
      final response = await apiClient.post(
        '/user/api/create-ticket-order?output=json',
        data: testData,
      );

      print('🧪 API Response: ${response.data}');

      // Hiển thị kết quả
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Test API: ${response.data['error'] == false ? 'SUCCESS' : 'FAILED'}'),
          backgroundColor:
              response.data['error'] == false ? Colors.green : Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      print('🧪 Test API Error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Test API Error: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _navigateToPayment() {
    // Navigate to Payment tab regardless of cart state
    PosNavigationService().navigateToPaymentTab();

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Chuyển đến tab Thanh toán'),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, cartState) {
        return Column(
          children: [
            // Cart Header
            Container(
              padding: const EdgeInsets.all(PosSpacing.md),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: PosColors.borderLight),
                ),
              ),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'ID: #646546456',
                  style: const TextStyle(
                    fontFamily: 'Varela',
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF333333),
                    height: 1.0,
                  ),
                ),
              ),
            ),

            // Cart Content
            Expanded(
              child: cartState.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.confirmation_number_outlined,
                            size: 64,
                            color: PosColors.textSecondary,
                          ),
                          const SizedBox(height: PosSpacing.md),
                          Text(
                            'Giỏ hàng trống',
                            style: PosTypography.bodyLarge.copyWith(
                              color: PosColors.textSecondary,
                            ),
                          ),
                          const SizedBox(height: PosSpacing.sm),
                          Text(
                            'Thêm vé vào giỏ hàng',
                            style: PosTypography.bodyMedium.copyWith(
                              color: PosColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(PosSpacing.sm),
                      itemCount: cartState.items.length,
                      itemBuilder: (context, index) {
                        final item = cartState.items[index];
                        return GestureDetector(
                          onTap: () => _onCartItemClick(item),
                          child: CartItemWidget(item: item),
                        );
                      },
                    ),
            ),

            // Cart Footer
            if (cartState.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(PosSpacing.md),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(color: PosColors.borderLight),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Thành tiền:',
                          style: PosTypography.bodyLarge.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${cartState.totalPrice.toStringAsFixed(0).replaceAllMapped(
                                RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                (Match m) => '${m[1]}.',
                              )} đ',
                          style: PosTypography.headingSmall.copyWith(
                            color: PosColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: PosSpacing.md),
                    Row(
                      children: [
                        // Thanh toán button
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.only(right: 20),
                            child: ElevatedButton(
                              onPressed: _isProcessingPayment
                                  ? null
                                  : _navigateToPayment,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF27C7FF),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: PosSpacing.md,
                                ),
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                disabledBackgroundColor:
                                    const Color(0xFF27C7FF).withOpacity(0.7),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'THANH TOÁN',
                                    style: PosTypography.bodyMedium.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                  if (_isProcessingPayment) ...[
                                    const SizedBox(width: 8),
                                    const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: PosSpacing.sm),
                        // Test API button
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: const Color(0xFF4CAF50),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: IconButton(
                            onPressed: _testApiCall,
                            icon: const Icon(
                              Icons.bug_report,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                        const SizedBox(width: PosSpacing.sm),
                        // Trash button
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFF9800),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: IconButton(
                            onPressed: () {
                              context.read<CartBloc>().add(const ClearCart());
                            },
                            icon: const Icon(
                              Icons.delete_outline,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }
}
