import 'package:flutter/material.dart';

import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../domain/entities/cart_item.dart';
import '../../domain/entities/ticket_product.dart';
import 'ticket_item_widget.dart';

/// Demo page để minh họa các cách sử dụng khác nhau của TicketItemWidget
/// 
/// Trang này cho thấy tính linh hoạt và khả năng tái sử dụng của widget component
class TicketItemWidgetDemo extends StatefulWidget {
  const TicketItemWidgetDemo({Key? key}) : super(key: key);

  @override
  State<TicketItemWidgetDemo> createState() => _TicketItemWidgetDemoState();
}

class _TicketItemWidgetDemoState extends State<TicketItemWidgetDemo> {
  // Sample data để demo
  late List<CartItem> _sampleItems;

  @override
  void initState() {
    super.initState();
    _initializeSampleData();
  }

  void _initializeSampleData() {
    _sampleItems = [
      CartItem(
        productId: '1',
        product: TicketProduct(
          id: '1',
          name: 'Vé xem phim Avatar 2',
          price: 120000,
          thumbnail: 'https://example.com/avatar2.jpg',
          categoryId: 'movie',
          description: 'Vé xem phim Avatar 2 - Suất chiếu 19:30',
        ),
        quantity: 2,
        note: 'Ghế đôi VIP',
      ),
      CartItem(
        productId: '2',
        product: TicketProduct(
          id: '2',
          name: 'Combo bắp nước',
          price: 85000,
          thumbnail: 'https://example.com/combo.jpg',
          categoryId: 'food',
          description: 'Combo bắp rang bơ + nước ngọt',
        ),
        quantity: 1,
        note: null,
      ),
    ];
  }

  void _handleCustomQuantityChange(String productId, int newQuantity) {
    setState(() {
      final index = _sampleItems.indexWhere((item) => item.productId == productId);
      if (index != -1) {
        if (newQuantity <= 0) {
          _sampleItems.removeAt(index);
        } else {
          _sampleItems[index] = _sampleItems[index].copyWith(quantity: newQuantity);
        }
      }
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đã cập nhật số lượng thành $newQuantity'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PosColors.background,
      appBar: AppBar(
        title: const Text('TicketItemWidget Demo'),
        backgroundColor: PosColors.surface,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(PosSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              title: '1. Sử dụng cơ bản (với CartBloc)',
              description: 'Widget sử dụng CartBloc mặc định từ context',
              child: Column(
                children: _sampleItems
                    .map((item) => TicketItemWidget(item: item))
                    .toList(),
              ),
            ),
            
            const SizedBox(height: PosSpacing.xl),
            
            _buildSection(
              title: '2. Sử dụng với custom callback',
              description: 'Widget sử dụng callback tùy chỉnh để xử lý thay đổi số lượng',
              child: Column(
                children: _sampleItems
                    .map((item) => TicketItemWidget(
                          item: item,
                          onQuantityChanged: _handleCustomQuantityChange,
                        ))
                    .toList(),
              ),
            ),
            
            const SizedBox(height: PosSpacing.xl),
            
            _buildSection(
              title: '3. Chỉ hiển thị thông tin (không có controls)',
              description: 'Widget chỉ hiển thị thông tin, không cho phép thay đổi số lượng',
              child: Column(
                children: _sampleItems
                    .map((item) => TicketItemWidget(
                          item: item,
                          showQuantityControls: false,
                        ))
                    .toList(),
              ),
            ),
            
            const SizedBox(height: PosSpacing.xl),
            
            _buildSection(
              title: '4. Ẩn ghi chú',
              description: 'Widget không hiển thị phần ghi chú',
              child: Column(
                children: _sampleItems
                    .map((item) => TicketItemWidget(
                          item: item,
                          showNote: false,
                        ))
                    .toList(),
              ),
            ),
            
            const SizedBox(height: PosSpacing.xl),
            
            _buildSection(
              title: '5. Tùy chỉnh padding và margin',
              description: 'Widget với padding và margin tùy chỉnh',
              child: Column(
                children: _sampleItems
                    .map((item) => TicketItemWidget(
                          item: item,
                          padding: const EdgeInsets.all(PosSpacing.lg),
                          margin: const EdgeInsets.symmetric(
                            vertical: PosSpacing.md,
                            horizontal: PosSpacing.sm,
                          ),
                        ))
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String description,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: PosTypography.headingSmall.copyWith(
            color: PosColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: PosSpacing.sm),
        Text(
          description,
          style: PosTypography.bodyMedium.copyWith(
            color: PosColors.textSecondary,
          ),
        ),
        const SizedBox(height: PosSpacing.md),
        Container(
          padding: const EdgeInsets.all(PosSpacing.md),
          decoration: BoxDecoration(
            color: PosColors.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: PosColors.borderLight),
          ),
          child: child,
        ),
      ],
    );
  }
}

/// Extension để thêm phương thức copyWith cho CartItem (nếu chưa có)
extension CartItemExtension on CartItem {
  CartItem copyWith({
    String? productId,
    TicketProduct? product,
    int? quantity,
    String? note,
  }) {
    return CartItem(
      productId: productId ?? this.productId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      note: note ?? this.note,
    );
  }
}
