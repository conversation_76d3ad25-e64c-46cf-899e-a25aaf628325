import 'package:flutter/material.dart';

import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../domain/entities/ticket_product.dart';
import '../../../../src/common/widgets/product_item_widget.dart';

/// Widget hiển thị grid sản phẩm ticket với responsive design
class ProductGridWidget extends StatelessWidget {
  /// Danh sách sản phẩm
  final List<TicketProduct> products;

  /// Callback khi tap vào sản phẩm
  final Function(TicketProduct product)? onProductTap;

  /// Callback khi thêm vào giỏ hàng
  final Function(TicketProduct product)? onAddToCart;

  /// <PERSON><PERSON> đang loading không
  final bool isLoading;

  /// ScrollController để handle pagination
  final ScrollController? scrollController;

  const ProductGridWidget({
    Key? key,
    required this.products,
    this.onProductTap,
    this.onAddToCart,
    this.isLoading = false,
    this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (products.isEmpty && !isLoading) {
      return const _EmptyProductsWidget();
    }

    return GridView.builder(
      controller: scrollController,
      padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16), // Giảm padding ngang để phù hợp với 4 cột
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4, // Thay đổi từ 3 thành 4 cột
        mainAxisExtent: 240, // Tăng thêm một chút để tránh overflow
        crossAxisSpacing: 16, // Giảm khoảng cách ngang để phù hợp với 4 cột
        mainAxisSpacing: 24, // Giảm khoảng cách dọc
      ),
      itemCount: products.length + (isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= products.length) {
          // Loading indicator at the end
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final product = products[index];
        return ProductItemWidget(
          title: product.name,
          imageUrl: product.thumbnail,
          price: product.formattedPrice,
          oldPrice: product.hasDiscount ? product.formattedPriceOld : null,
          discount: product.discount,
          onTap: () => onProductTap?.call(product),
        );
      },
    );
  }
}

/// Widget hiển thị khi không có sản phẩm
class _EmptyProductsWidget extends StatelessWidget {
  const _EmptyProductsWidget() : super();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.confirmation_number_outlined,
            size: 80,
            color: PosColors.textSecondary,
          ),
          const SizedBox(height: PosSpacing.lg),
          Text(
            'Không có vé nào',
            style: PosTypography.headingMedium.copyWith(
              color: PosColors.textSecondary,
            ),
          ),
          const SizedBox(height: PosSpacing.sm),
          Text(
            'Thử thay đổi bộ lọc hoặc tìm kiếm khác',
            style: PosTypography.bodyMedium.copyWith(
              color: PosColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
