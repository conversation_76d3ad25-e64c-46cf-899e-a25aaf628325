import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../domain/entities/cart_item.dart';
import '../bloc/cart_bloc.dart';

class CartItemWidget extends StatelessWidget {
  final CartItem item;
  final void Function(String productId, int newQuantity)? onQuantityChanged;
  final bool showQuantityControls;
  final bool showNote;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const CartItemWidget({
    super.key,
    required this.item,
    this.onQuantityChanged,
    this.showQuantityControls = true,
    this.showNote = true,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: PosSpacing.sm),
      padding: padding ?? const EdgeInsets.all(PosSpacing.sm),
      decoration: BoxDecoration(
        color: PosColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: PosColors.borderLight),
      ),
      child: Row(
        children: [
          _buildProductImage(),
          const SizedBox(width: PosSpacing.sm),
          Expanded(
            child: _buildProductInfo(),
          ),
          if (showQuantityControls) _buildQuantityControls(context),
        ],
      ),
    );
  }

  Widget _buildProductImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(6),
      child: Image.network(
        item.product.thumbnail,
        width: 50,
        height: 50,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 50,
            height: 50,
            color: PosColors.borderLight,
            child: Icon(
              Icons.confirmation_number,
              color: PosColors.textSecondary,
            ),
          );
        },
      ),
    );
  }

  Widget _buildProductInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          item.product.name,
          style: PosTypography.bodyMedium.copyWith(
            fontWeight: FontWeight.w500,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 2),
        Text(
          item.product.formattedPrice,
          style: PosTypography.bodySmall.copyWith(
            color: PosColors.primary,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (showNote) ...[
          const SizedBox(height: 6),
          Text(
            'Ghi chú: ${item.note ?? ''}',
            style: PosTypography.bodySmall.copyWith(
              color: PosColors.textSecondary,
              fontStyle:
                  item.note == null ? FontStyle.italic : FontStyle.normal,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildQuantityControls(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: PosColors.borderLight),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildQuantityButton(
            context: context,
            icon: Icons.remove,
            onTap: () => _handleQuantityChange(context, item.quantity - 1),
          ),
          Container(
            width: 40,
            height: 32,
            alignment: Alignment.center,
            child: Text(
              '${item.quantity}',
              style: PosTypography.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          _buildQuantityButton(
            context: context,
            icon: Icons.add,
            onTap: () => _handleQuantityChange(context, item.quantity + 1),
            isIncrease: true,
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityButton({
    required BuildContext context,
    required IconData icon,
    required VoidCallback onTap,
    bool isIncrease = false,
  }) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        border: Border(
          right: isIncrease
              ? BorderSide.none
              : BorderSide(color: PosColors.borderLight),
          left: isIncrease
              ? BorderSide(color: PosColors.borderLight)
              : BorderSide.none,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        child: Icon(
          icon,
          size: 16,
          color: PosColors.textSecondary,
        ),
      ),
    );
  }

  void _handleQuantityChange(BuildContext context, int newQuantity) {
    if (onQuantityChanged != null) {
      onQuantityChanged!(item.productId, newQuantity);
    } else {
      context.read<CartBloc>().add(
            UpdateQuantity(
              productId: item.productId,
              quantity: newQuantity,
            ),
          );
    }
  }
}

