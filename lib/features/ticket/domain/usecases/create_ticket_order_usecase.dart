import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/ticket_repository.dart';

/// UseCase để tạo đơn hàng vé
/// 
/// Xử lý logic business cho việc tạo đơn hàng vé
class CreateTicketOrderUseCase implements UseCase<Map<String, dynamic>, CreateTicketOrderParams> {
  final TicketRepository repository;

  CreateTicketOrderUseCase(this.repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(CreateTicketOrderParams params) async {
    return await repository.createTicketOrder(
      products: params.products,
      customerName: params.customerName,
      phone: params.phone,
      totalAmount: params.totalAmount,
      email: params.email,
      address: params.address,
      note: params.note,
      paymentMethod: params.paymentMethod,
      bankCode: params.bankCode,
      subtotal: params.subtotal,
      discountAmount: params.discountAmount,
      coupon: params.coupon,
      travelDate: params.travelDate,
      ticketType: params.ticketType,
      eventLocation: params.eventLocation,
    );
  }
}

/// Parameters cho CreateTicketOrderUseCase
class CreateTicketOrderParams {
  final List<Map<String, dynamic>> products;
  final String customerName;
  final String phone;
  final double totalAmount;
  final String? email;
  final String? address;
  final String? note;
  final int? paymentMethod;
  final String? bankCode;
  final double? subtotal;
  final double? discountAmount;
  final String? coupon;
  final String? travelDate;
  final String? ticketType;
  final String? eventLocation;

  const CreateTicketOrderParams({
    required this.products,
    required this.customerName,
    required this.phone,
    required this.totalAmount,
    this.email,
    this.address,
    this.note,
    this.paymentMethod,
    this.bankCode,
    this.subtotal,
    this.discountAmount,
    this.coupon,
    this.travelDate,
    this.ticketType,
    this.eventLocation,
  });

  /// Tạo từ cart items và customer info
  factory CreateTicketOrderParams.fromCartAndCustomer({
    required List<Map<String, dynamic>> cartProducts,
    required String customerName,
    required String phone,
    required double totalAmount,
    String? email,
    String? address,
    String? note,
    int? paymentMethod,
    String? bankCode,
    double? subtotal,
    double? discountAmount,
    String? coupon,
    String? travelDate,
    String? ticketType,
    String? eventLocation,
  }) {
    return CreateTicketOrderParams(
      products: cartProducts,
      customerName: customerName,
      phone: phone,
      totalAmount: totalAmount,
      email: email,
      address: address,
      note: note,
      paymentMethod: paymentMethod,
      bankCode: bankCode,
      subtotal: subtotal,
      discountAmount: discountAmount,
      coupon: coupon,
      travelDate: travelDate,
      ticketType: ticketType,
      eventLocation: eventLocation,
    );
  }
}
