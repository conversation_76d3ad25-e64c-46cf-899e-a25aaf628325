/// Enum đại diện cho trạng thái hoá đơn (tương thích với Orders API)
enum InvoiceStatus {
  /// Ch<PERSON> xác nhận (status = 0)
  pending('<PERSON><PERSON> xác nhận', 'PENDING'),

  /// <PERSON>ang xử lý (status = 1)
  processing('<PERSON><PERSON> xử lý', 'PROCESSING'),

  /// Sẵn sàng/Đang giao (status = 2)
  ready('Sẵn sàng', 'READY'),

  /// Đã thanh toán/Hoàn thành (status = 3)
  paid('Đã thanh toán', 'PAID'),

  /// Đã hủy (status = 4)
  cancelled('Đã hủy', 'CANCELLED'),

  /// Tr<PERSON> hàng (status = 5)
  refunded('Trả hàng', 'REFUNDED');

  const InvoiceStatus(this.displayName, this.value);

  /// Tên hiển thị của trạng thái
  final String displayName;
  
  /// Giá trị string của trạng thái
  final String value;

  /// Tạo InvoiceStatus từ string value
  static InvoiceStatus fromString(String value) {
    switch (value.toUpperCase()) {
      case 'PENDING':
        return InvoiceStatus.pending;
      case 'PROCESSING':
        return InvoiceStatus.processing;
      case 'READY':
        return InvoiceStatus.ready;
      case 'PAID':
        return InvoiceStatus.paid;
      case 'CANCELLED':
        return InvoiceStatus.cancelled;
      case 'REFUNDED':
        return InvoiceStatus.refunded;
      // Backward compatibility
      case 'UNPAID':
        return InvoiceStatus.pending;
      case 'OVERDUE':
        return InvoiceStatus.pending;
      default:
        return InvoiceStatus.pending;
    }
  }

  /// Kiểm tra xem trạng thái có phải là đã thanh toán không
  bool get isPaid => this == InvoiceStatus.paid;

  /// Kiểm tra xem trạng thái có phải là chưa thanh toán không
  bool get isUnpaid => this == InvoiceStatus.pending;

  /// Kiểm tra xem trạng thái có phải là quá hạn không
  bool get isOverdue => this == InvoiceStatus.pending;

  /// Kiểm tra xem trạng thái có phải là đã hủy không
  bool get isCancelled => this == InvoiceStatus.cancelled;

  /// Kiểm tra xem trạng thái có phải là đang xử lý không
  bool get isProcessing => this == InvoiceStatus.processing;

  /// Kiểm tra xem trạng thái có phải là sẵn sàng không
  bool get isReady => this == InvoiceStatus.ready;

  /// Kiểm tra xem trạng thái có phải là trả hàng không
  bool get isRefunded => this == InvoiceStatus.refunded;
}
