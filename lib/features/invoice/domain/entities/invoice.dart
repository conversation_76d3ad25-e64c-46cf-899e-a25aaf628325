import 'package:equatable/equatable.dart';
import 'invoice_item.dart';
import 'invoice_status.dart';
import 'payment_method.dart';
import 'invoice_table_info.dart';

/// Entity đại diện cho hoá đơn (tương thích với Orders API)
class Invoice extends Equatable {
  /// ID duy nhất của hoá đơn (từ _id của order)
  final String id;

  /// Order ID từ hệ thống
  final String orderId;

  /// Payment ID
  final String paymentId;

  /// Mã số đơn hàng
  final int code;

  /// Số hoá đơn (ví dụ: #2146-Ban 1)
  final String invoiceNumber;

  /// Tên khách hàng
  final String customerName;

  /// Số điện thoại khách hàng
  final String customerPhone;

  /// Email khách hàng
  final String? customerEmail;

  /// Số bàn/khu vực
  final String tableNumber;

  /// Thông tin bàn chi tiết
  final InvoiceTableInfo? tableInfo;

  /// Ngày tạo hoá đơn
  final DateTime createdDate;

  /// Ngày hoàn thành
  final DateTime? completedDate;

  /// Tổng số tiền
  final double totalAmount;

  /// Tổng tiền trước thuế/giảm giá
  final double subtotal;

  /// Số tiền giảm giá
  final double discountAmount;

  /// Phí vận chuyển (nếu có)
  final double transportFee;

  /// Trạng thái hoá đơn (từ order status)
  final InvoiceStatus status;

  /// Danh sách các mục trong hoá đơn
  final List<InvoiceItem> items;

  /// Ghi chú
  final String? notes;

  /// Phương thức thanh toán
  final PaymentMethod? paymentMethod;

  /// Mã ngân hàng (nếu thanh toán online)
  final String? bankCode;

  /// Thanh toán online hay không
  final bool isPayOnline;

  /// Loại dịch vụ (dine-in, takeaway, delivery)
  final String serviceType;

  /// Mã coupon (nếu có)
  final String? coupon;

  /// ID cửa hàng
  final String storeId;

  /// ID nhân viên tạo hoá đơn
  final String? staffId;

  /// Tên nhân viên tạo hoá đơn
  final String? staffName;

  const Invoice({
    required this.id,
    required this.orderId,
    required this.paymentId,
    required this.code,
    required this.invoiceNumber,
    required this.customerName,
    required this.customerPhone,
    this.customerEmail,
    required this.tableNumber,
    this.tableInfo,
    required this.createdDate,
    this.completedDate,
    required this.totalAmount,
    required this.subtotal,
    this.discountAmount = 0.0,
    this.transportFee = 0.0,
    required this.status,
    required this.items,
    this.notes,
    this.paymentMethod,
    this.bankCode,
    this.isPayOnline = false,
    required this.serviceType,
    this.coupon,
    required this.storeId,
    this.staffId,
    this.staffName,
  });

  /// Tính tổng số lượng món
  int get totalQuantity {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  /// Format tổng tiền theo định dạng VND
  String get formattedTotalAmount {
    return '${totalAmount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}đ';
  }

  /// Format ngày tạo theo định dạng dd-MM-yyyy HH:mm
  String get formattedCreatedDate {
    return '${createdDate.day.toString().padLeft(2, '0')}-'
        '${createdDate.month.toString().padLeft(2, '0')}-'
        '${createdDate.year} '
        '${createdDate.hour.toString().padLeft(2, '0')}:'
        '${createdDate.minute.toString().padLeft(2, '0')}';
  }

  /// Kiểm tra xem hoá đơn có quá hạn không (chưa thanh toán sau 24h)
  bool get isOverdue {
    if (status.isPaid) return false;
    final now = DateTime.now();
    final difference = now.difference(createdDate);
    return difference.inHours > 24;
  }

  /// Tạo bản sao với các thuộc tính được cập nhật
  Invoice copyWith({
    String? id,
    String? orderId,
    String? paymentId,
    int? code,
    String? invoiceNumber,
    String? customerName,
    String? customerPhone,
    String? customerEmail,
    String? tableNumber,
    InvoiceTableInfo? tableInfo,
    DateTime? createdDate,
    DateTime? completedDate,
    double? totalAmount,
    double? subtotal,
    double? discountAmount,
    double? transportFee,
    InvoiceStatus? status,
    List<InvoiceItem>? items,
    String? notes,
    PaymentMethod? paymentMethod,
    String? bankCode,
    bool? isPayOnline,
    String? serviceType,
    String? coupon,
    String? storeId,
    String? staffId,
    String? staffName,
  }) {
    return Invoice(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      paymentId: paymentId ?? this.paymentId,
      code: code ?? this.code,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      customerEmail: customerEmail ?? this.customerEmail,
      tableNumber: tableNumber ?? this.tableNumber,
      tableInfo: tableInfo ?? this.tableInfo,
      createdDate: createdDate ?? this.createdDate,
      completedDate: completedDate ?? this.completedDate,
      totalAmount: totalAmount ?? this.totalAmount,
      subtotal: subtotal ?? this.subtotal,
      discountAmount: discountAmount ?? this.discountAmount,
      transportFee: transportFee ?? this.transportFee,
      status: status ?? this.status,
      items: items ?? this.items,
      notes: notes ?? this.notes,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      bankCode: bankCode ?? this.bankCode,
      isPayOnline: isPayOnline ?? this.isPayOnline,
      serviceType: serviceType ?? this.serviceType,
      coupon: coupon ?? this.coupon,
      storeId: storeId ?? this.storeId,
      staffId: staffId ?? this.staffId,
      staffName: staffName ?? this.staffName,
    );
  }

  @override
  List<Object?> get props => [
        id,
        orderId,
        paymentId,
        code,
        invoiceNumber,
        customerName,
        customerPhone,
        customerEmail,
        tableNumber,
        tableInfo,
        createdDate,
        completedDate,
        totalAmount,
        subtotal,
        discountAmount,
        transportFee,
        status,
        items,
        notes,
        paymentMethod,
        bankCode,
        isPayOnline,
        serviceType,
        coupon,
        storeId,
        staffId,
        staffName,
      ];
}
