import 'package:equatable/equatable.dart';

/// Entity đại diện cho thông tin bàn trong hoá đơn
class InvoiceTableInfo extends Equatable {
  /// ID của bàn
  final String tableId;
  
  /// Tên bàn
  final String tableName;
  
  /// ID khu vực
  final String areaId;
  
  /// Tên khu vực
  final String areaName;
  
  /// Số ghế của bàn
  final int capacity;
  
  /// Trạng thái bàn
  final int status;

  const InvoiceTableInfo({
    required this.tableId,
    required this.tableName,
    required this.areaId,
    required this.areaName,
    required this.capacity,
    required this.status,
  });

  /// Tạo bản sao với các thuộc tính được cập nhật
  InvoiceTableInfo copyWith({
    String? tableId,
    String? tableName,
    String? areaId,
    String? areaName,
    int? capacity,
    int? status,
  }) {
    return InvoiceTableInfo(
      tableId: tableId ?? this.tableId,
      tableName: tableName ?? this.tableName,
      areaId: areaId ?? this.areaId,
      areaName: areaName ?? this.areaName,
      capacity: capacity ?? this.capacity,
      status: status ?? this.status,
    );
  }

  @override
  List<Object?> get props => [
        tableId,
        tableName,
        areaId,
        areaName,
        capacity,
        status,
      ];
}
