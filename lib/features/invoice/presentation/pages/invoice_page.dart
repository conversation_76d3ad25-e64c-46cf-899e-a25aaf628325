import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/invoice_bloc.dart';
import '../bloc/invoice_event.dart';
import '../bloc/invoice_state.dart';
import '../widgets/invoice_card_widget.dart';
import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';

/// M<PERSON>n hình Hoá đơn theo thiết kế POS
/// Hi<PERSON>n thị danh sách hoá đơn dạng card grid với filter
class InvoicePage extends StatefulWidget {
  const InvoicePage({super.key});

  @override
  State<InvoicePage> createState() => _InvoicePageState();
}

class _InvoicePageState extends State<InvoicePage> {
  String _selectedFilter = 'TẤT CẢ';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Load dữ liệu ban đầu
  void _loadInitialData() {
    context.read<InvoiceBloc>().add(const LoadInvoices());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PosColors.background,
      body: Column(
        children: [
          _buildFilterSection(),
          Expanded(
            child: BlocBuilder<InvoiceBloc, InvoiceState>(
              builder: (context, state) {
                return _buildInvoiceGrid(state);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Build filter section theo thiết kế
  Widget _buildFilterSection() {
    return Container(
      color: PosColors.surface,
      padding: const EdgeInsets.symmetric(
        horizontal: PosSpacing.lg,
        vertical: PosSpacing.md,
      ),
      child: Row(
        children: [
          // Filter "TẤT CẢ"
          _buildFilterChip(
            label: 'TẤT CẢ',
            isSelected: _selectedFilter == 'TẤT CẢ',
            onTap: () {
              setState(() {
                _selectedFilter = 'TẤT CẢ';
              });
              context.read<InvoiceBloc>().add(const ClearInvoiceFilters());
            },
          ),
          const SizedBox(width: PosSpacing.md),

          // Filter "HÔM NAY"
          _buildFilterChip(
            label: 'HÔM NAY',
            isSelected: _selectedFilter == 'HÔM NAY',
            onTap: () {
              setState(() {
                _selectedFilter = 'HÔM NAY';
              });
              final today = DateTime.now();
              final startOfDay = DateTime(today.year, today.month, today.day);
              final endOfDay =
                  DateTime(today.year, today.month, today.day, 23, 59, 59);

              context.read<InvoiceBloc>().add(
                    FilterInvoicesByDateRange(
                      startDate: startOfDay,
                      endDate: endOfDay,
                    ),
                  );
            },
          ),

          const Spacer(),

          // Service Type Filter
          Container(
            width: 200,
            height: 40,
            margin: const EdgeInsets.only(right: 16),
            child: DropdownButtonFormField<String>(
              value: null, // TODO: Add state management for selected service type
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                hintText: 'Tất cả dịch vụ',
                hintStyle: PosTypography.bodyMedium.copyWith(
                  color: PosColors.textTertiary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: PosColors.borderLight),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: PosColors.borderLight),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: PosColors.primary),
                ),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('Tất cả dịch vụ')),
                // DropdownMenuItem(value: 'coffee', child: Text('Cà phê')),
                // DropdownMenuItem(value: 'drink', child: Text('Đồ uống')),
                // DropdownMenuItem(value: 'gas', child: Text('Xăng dầu')),
                // DropdownMenuItem(value: 'showroom', child: Text('Showroom')),
                // DropdownMenuItem(value: 'parking', child: Text('Bãi đỗ xe')),
                // DropdownMenuItem(value: 'spa', child: Text('Spa')),
                // DropdownMenuItem(value: 'hotel', child: Text('Khách sạn')),
                // DropdownMenuItem(value: 'examination', child: Text('Khám bệnh')),
                // DropdownMenuItem(value: 'store', child: Text('Cửa hàng')),
              ],
              onChanged: (value) {
                // TODO: Add filter by service type
                debugPrint('Selected service type: $value');
              },
            ),
          ),

          // Search field
          SizedBox(
            width: 300,
            height: 40,
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                // Debounce search
                Future.delayed(const Duration(milliseconds: 500), () {
                  if (mounted && value.isNotEmpty) {
                    context.read<InvoiceBloc>().add(SearchInvoices(query: value));
                  } else if (mounted && value.isEmpty) {
                    context.read<InvoiceBloc>().add(const LoadInvoices());
                  }
                });
              },
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                hintText: 'Tìm kiếm theo mã đơn, tên khách...',
                hintStyle: PosTypography.bodyMedium.copyWith(
                  color: PosColors.textTertiary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: PosColors.borderLight),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: PosColors.borderLight),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: PosColors.primary),
                ),
                suffixIcon: Icon(
                  Icons.search,
                  color: PosColors.textSecondary,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build filter chip theo thiết kế POS
  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: PosSpacing.lg,
          vertical: PosSpacing.sm,
        ),
        decoration: BoxDecoration(
          color: isSelected ? PosColors.primary : PosColors.surface,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? PosColors.primary : PosColors.borderLight,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: PosTypography.bodyMedium.copyWith(
            color: isSelected ? PosColors.textOnDark : PosColors.textSecondary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            fontSize: 13,
          ),
        ),
      ),
    );
  }

  /// Build invoice grid theo thiết kế
  Widget _buildInvoiceGrid(InvoiceState state) {
    if (state is InvoiceLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: PosColors.primary,
        ),
      );
    }

    if (state is InvoiceError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: PosColors.error,
            ),
            const SizedBox(height: PosSpacing.md),
            Text(
              'Có lỗi xảy ra',
              style: PosTypography.headingSmall.copyWith(
                color: PosColors.error,
              ),
            ),
            const SizedBox(height: PosSpacing.sm),
            Text(
              state.message,
              style: PosTypography.bodyMedium.copyWith(
                color: PosColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: PosSpacing.lg),
            ElevatedButton(
              onPressed: _loadInitialData,
              style: ElevatedButton.styleFrom(
                backgroundColor: PosColors.primary,
                foregroundColor: PosColors.textOnDark,
              ),
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (state is InvoiceLoaded) {
      if (state.invoices.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.receipt_long_outlined,
                size: 64,
                color: PosColors.textTertiary,
              ),
              const SizedBox(height: PosSpacing.md),
              Text(
                'Chưa có hoá đơn nào',
                style: PosTypography.headingSmall.copyWith(
                  color: PosColors.textSecondary,
                ),
              ),
              const SizedBox(height: PosSpacing.sm),
              Text(
                'Các hoá đơn sẽ hiển thị ở đây',
                style: PosTypography.bodyMedium.copyWith(
                  color: PosColors.textTertiary,
                ),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: () async {
          context.read<InvoiceBloc>().add(const RefreshInvoices());
        },
        color: PosColors.primary,
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Responsive grid - adjust columns based on screen width
            int crossAxisCount = 4;
            if (constraints.maxWidth < 800) {
              crossAxisCount = 2;
            } else if (constraints.maxWidth < 1200) {
              crossAxisCount = 3;
            }

            return GridView.builder(
              padding: const EdgeInsets.all(16), // Consistent padding
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: 16, // Consistent spacing
                mainAxisSpacing: 16, // Consistent spacing
                childAspectRatio: 2.0, // Taller to fix overflow issue
              ),
              itemCount: state.invoices.length,
              itemBuilder: (context, index) {
                final invoice = state.invoices[index];
                return InvoiceCardWidget(
                  invoice: invoice,
                  onTap: () {
                    // TODO: Navigate to invoice detail
                    debugPrint('Tapped invoice: ${invoice.invoiceNumber}');
                  },
                  onPrint: () {
                    context.read<InvoiceBloc>().add(
                          ExportInvoiceToPdf(invoiceId: invoice.id),
                        );
                  },
                );
              },
            );
          },
        ),
      );
    }

    // Default loading state
    return const Center(
      child: CircularProgressIndicator(
        color: PosColors.primary,
      ),
    );
  }
}
