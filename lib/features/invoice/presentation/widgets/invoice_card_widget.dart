import 'package:flutter/material.dart';
import '../../domain/entities/invoice.dart';
import '../../domain/entities/invoice_status.dart';
import '../../../../core/utils/currency_formatter.dart';

/// Widget hiển thị invoice card theo thiết kế POS
/// Layout: Số hoá đơn + Status | Số tiền | Thời gian + Số món | Nút in
class InvoiceCardWidget extends StatelessWidget {
  final Invoice invoice;
  final VoidCallback? onTap;
  final VoidCallback? onPrint;

  const InvoiceCardWidget({
    super.key,
    required this.invoice,
    this.onTap,
    this.onPrint,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: const Color(0xFF4CAF50),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hàng 1: ID, Bàn, Trạng thái - với Flexible để tránh overflow
            Row(
              children: [
                Expanded(
                  child: Text(
                    invoice.invoiceNumber,
                    style: const TextStyle(
                      fontFamily: 'Varela',
                      fontSize: 14, // Giảm size để fit better
                      color: Color(0xFF333333),
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                const SizedBox(width: 8),
                _buildStatusChip(invoice.status),
              ],
            ),
            const SizedBox(height: 8),

            // Hàng 2: Giá tiền - với Flexible để responsive
            Row(
              children: [
                Expanded(
                  child: Text(
                    CurrencyFormatter.formatVND(invoice.totalAmount),
                    style: const TextStyle(
                      fontFamily: 'Varela',
                      fontSize: 24, // Giảm từ 28 xuống 24
                      color: Color(0xFF333333),
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                const SizedBox(width: 8),
                InkWell(
                  onTap: onPrint,
                  borderRadius: BorderRadius.circular(4),
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF3F4F6),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.print_outlined,
                      color: Color(0xFF6B7280),
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
            const Spacer(), // Đẩy hàng cuối xuống bottom

            // Hàng 3: Ngày giờ và số lượng - với Flexible
            Row(
              children: [
                Expanded(
                  child: Text(
                    invoice.formattedCreatedDate,
                    style: const TextStyle(
                      fontFamily: 'Varela',
                      fontSize: 12, // Giảm size
                      color: Color(0xFF6B7280),
                      fontWeight: FontWeight.w400,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${invoice.totalQuantity} món',
                  style: const TextStyle(
                    fontFamily: 'Varela',
                    fontSize: 12,
                    color: Color(0xFF6B7280),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build status chip theo thiết kế - compact và nhỏ gọn
  Widget _buildStatusChip(InvoiceStatus status) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case InvoiceStatus.pending:
        backgroundColor = const Color(0xFFFEF3C7); // Vàng nhạt
        textColor = const Color(0xFFD97706); // Vàng đậm
        text = 'Chờ xác nhận';
        break;
      case InvoiceStatus.processing:
        backgroundColor = const Color(0xFFDDD6FE); // Tím nhạt
        textColor = const Color(0xFF7C3AED); // Tím đậm
        text = 'Đang xử lý';
        break;
      case InvoiceStatus.ready:
        backgroundColor = const Color(0xFFBFDBFE); // Xanh dương nhạt
        textColor = const Color(0xFF2563EB); // Xanh dương đậm
        text = 'Sẵn sàng';
        break;
      case InvoiceStatus.paid:
        backgroundColor = const Color(0xFFDCFCE7); // Xanh nhạt
        textColor = const Color(0xFF16A34A); // Xanh đậm
        text = 'Đã thanh toán';
        break;
      case InvoiceStatus.cancelled:
        backgroundColor = const Color(0xFFFEE2E2); // Đỏ nhạt
        textColor = const Color(0xFFDC2626); // Đỏ đậm
        text = 'Đã hủy';
        break;
      case InvoiceStatus.refunded:
        backgroundColor = const Color(0xFFFEE2E2); // Đỏ nhạt
        textColor = const Color(0xFFDC2626); // Đỏ đậm
        text = 'Trả hàng';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 6, // Giảm padding để compact hơn
        vertical: 2, // Giảm padding để compact hơn
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontFamily: 'Varela',
          fontWeight: FontWeight.w500,
          fontSize: 11, // Giảm size để fit better
          color: textColor,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }
}
