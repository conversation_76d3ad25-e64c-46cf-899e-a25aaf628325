import 'package:flutter/material.dart';
import '../../domain/entities/invoice_status.dart';
import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';

/// Widget để filter hoá đơn theo trạng thái và khoảng thời gian
class InvoiceFilterWidget extends StatefulWidget {
  final Function(InvoiceStatus?) onStatusFilterChanged;
  final Function(DateTime?, DateTime?) onDateRangeChanged;
  final VoidCallback onClearFilters;

  const InvoiceFilterWidget({
    super.key,
    required this.onStatusFilterChanged,
    required this.onDateRangeChanged,
    required this.onClearFilters,
  });

  @override
  State<InvoiceFilterWidget> createState() => _InvoiceFilterWidgetState();
}

class _InvoiceFilterWidgetState extends State<InvoiceFilterWidget> {
  InvoiceStatus? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Status filter dropdown
        Expanded(
          flex: 2,
          child: _buildStatusFilter(),
        ),
        
        const SizedBox(width: PosSpacing.sm),
        
        // Date range filter
        Expanded(
          flex: 2,
          child: _buildDateRangeFilter(),
        ),
        
        const SizedBox(width: PosSpacing.sm),
        
        // Clear filters button
        _buildClearButton(),
      ],
    );
  }

  /// Build status filter dropdown
  Widget _buildStatusFilter() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        border: Border.all(color: PosColors.border),
        borderRadius: BorderRadius.circular(8),
        color: PosColors.surface,
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<InvoiceStatus?>(
          value: _selectedStatus,
          hint: Padding(
            padding: const EdgeInsets.symmetric(horizontal: PosSpacing.sm),
            child: Row(
              children: [
                Icon(
                  Icons.filter_list,
                  size: 16,
                  color: PosColors.textSecondary,
                ),
                const SizedBox(width: PosSpacing.xs),
                Text(
                  'TẤT CẢ',
                  style: PosTypography.bodySmall.copyWith(
                    color: PosColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          items: [
            // All option
            DropdownMenuItem<InvoiceStatus?>(
              value: null,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: PosSpacing.sm),
                child: Row(
                  children: [
                    Icon(
                      Icons.filter_list,
                      size: 16,
                      color: PosColors.textSecondary,
                    ),
                    const SizedBox(width: PosSpacing.xs),
                    Text(
                      'TẤT CẢ',
                      style: PosTypography.bodySmall.copyWith(
                        color: PosColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Status options
            ...InvoiceStatus.values.map((status) {
              return DropdownMenuItem<InvoiceStatus?>(
                value: status,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: PosSpacing.sm),
                  child: Row(
                    children: [
                      _buildStatusIcon(status),
                      const SizedBox(width: PosSpacing.xs),
                      Text(
                        status.displayName,
                        style: PosTypography.bodySmall.copyWith(
                          color: PosColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ],
          onChanged: (value) {
            setState(() {
              _selectedStatus = value;
            });
            widget.onStatusFilterChanged(value);
          },
          isExpanded: true,
          icon: Padding(
            padding: const EdgeInsets.only(right: PosSpacing.sm),
            child: Icon(
              Icons.keyboard_arrow_down,
              color: PosColors.textSecondary,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  /// Build date range filter
  Widget _buildDateRangeFilter() {
    return GestureDetector(
      onTap: _showDateRangePicker,
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          border: Border.all(color: PosColors.border),
          borderRadius: BorderRadius.circular(8),
          color: PosColors.surface,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: PosSpacing.sm),
          child: Row(
            children: [
              Icon(
                Icons.date_range,
                size: 16,
                color: PosColors.textSecondary,
              ),
              const SizedBox(width: PosSpacing.xs),
              Expanded(
                child: Text(
                  _getDateRangeText(),
                  style: PosTypography.bodySmall.copyWith(
                    color: _hasDateFilter() 
                        ? PosColors.textPrimary 
                        : PosColors.textSecondary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: PosColors.textSecondary,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build clear filters button
  Widget _buildClearButton() {
    final hasFilters = _selectedStatus != null || _hasDateFilter();
    
    return GestureDetector(
      onTap: hasFilters ? _clearFilters : null,
      child: Container(
        height: 40,
        width: 40,
        decoration: BoxDecoration(
          border: Border.all(
            color: hasFilters ? PosColors.primary : PosColors.border,
          ),
          borderRadius: BorderRadius.circular(8),
          color: hasFilters ? PosColors.primary.withValues(alpha: 0.1) : PosColors.surface,
        ),
        child: Icon(
          Icons.clear,
          size: 20,
          color: hasFilters ? PosColors.primary : PosColors.textSecondary,
        ),
      ),
    );
  }

  /// Build status icon
  Widget _buildStatusIcon(InvoiceStatus status) {
    IconData iconData;
    Color color;

    switch (status) {
      case InvoiceStatus.pending:
        iconData = Icons.pending;
        color = Colors.orange;
        break;
      case InvoiceStatus.processing:
        iconData = Icons.hourglass_empty;
        color = Colors.purple;
        break;
      case InvoiceStatus.ready:
        iconData = Icons.check_circle_outline;
        color = Colors.blue;
        break;
      case InvoiceStatus.paid:
        iconData = Icons.check_circle;
        color = Colors.green;
        break;
      case InvoiceStatus.cancelled:
        iconData = Icons.cancel;
        color = Colors.grey;
        break;
      case InvoiceStatus.refunded:
        iconData = Icons.warning;
        color = Colors.red;
        break;
    }

    return Icon(
      iconData,
      size: 16,
      color: color,
    );
  }

  /// Show date range picker
  Future<void> _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: PosColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      widget.onDateRangeChanged(_startDate, _endDate);
    }
  }

  /// Get date range text
  String _getDateRangeText() {
    if (_startDate != null && _endDate != null) {
      final start = '${_startDate!.day}/${_startDate!.month}';
      final end = '${_endDate!.day}/${_endDate!.month}';
      return '$start - $end';
    }
    return 'HÔM NAY';
  }

  /// Check if has date filter
  bool _hasDateFilter() {
    return _startDate != null && _endDate != null;
  }

  /// Clear all filters
  void _clearFilters() {
    setState(() {
      _selectedStatus = null;
      _startDate = null;
      _endDate = null;
    });
    widget.onClearFilters();
  }
}
