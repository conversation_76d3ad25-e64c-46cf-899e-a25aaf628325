import 'package:flutter/foundation.dart';
import '../../../../core/network/api_client.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/invoice_model.dart';
import '../../domain/entities/invoice_status.dart';

/// Abstract class cho Invoice Remote Data Source
abstract class InvoiceRemoteDataSource {
  /// L<PERSON>y danh sách hoá đơn từ API
  Future<List<InvoiceModel>> getInvoices({
    required String storeId,
    required int page,
    required int limit,
    InvoiceStatus? status,
    String? search,
    DateTime? startDate,
    DateTime? endDate,
    List<int>? statuses,
  });

  /// Lấy chi tiết hoá đơn theo ID
  Future<InvoiceModel> getInvoiceDetail(String invoiceId);

  /// Cập nhật trạng thái hoá đơn
  Future<InvoiceModel> updateInvoiceStatus(
    String invoiceId,
    InvoiceStatus status,
  );

  /// Xóa hoá đơn
  Future<void> deleteInvoice(String invoiceId);

  /// Tạo hoá đơn mới
  Future<InvoiceModel> createInvoice(InvoiceModel invoice);

  /// Xuất hoá đơn ra PDF
  Future<String> exportInvoiceToPdf(String invoiceId);

  /// Lấy thống kê hoá đơn
  Future<Map<String, dynamic>> getInvoiceStats({
    required String storeId,
    DateTime? fromDate,
    DateTime? toDate,
  });
}

/// Implementation của InvoiceRemoteDataSource
class InvoiceRemoteDataSourceImpl implements InvoiceRemoteDataSource {
  final ApiClient apiClient;

  InvoiceRemoteDataSourceImpl({
    required this.apiClient,
  });

  @override
  Future<List<InvoiceModel>> getInvoices({
    required String storeId,
    required int page,
    required int limit,
    InvoiceStatus? status,
    String? search,
    DateTime? startDate,
    DateTime? endDate,
    List<int>? statuses,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'storeId': storeId,
        'page': page,
        'limit': limit,
      };

      // Thêm filter parameters
      if (statuses != null && statuses.isNotEmpty) {
        queryParams['statuses'] = statuses.join(',');
      } else if (status != null) {
        queryParams['status'] = _mapInvoiceStatusToInt(status);
      }

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String();
      }

      // Sử dụng endpoint lấy tất cả orders cho Invoice page
      final response = await apiClient.get(
        '/user/api/orders-with-tables',
        queryParameters: {
          ...queryParams,
          'output': 'json', // Đảm bảo có output=json
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;

        // Debug logging
        debugPrint('📋 Invoice API Response: ${response.statusCode}');
        debugPrint('📋 Response data keys: ${data?.keys}');
        debugPrint('📋 Success: ${data?['success']}');
        debugPrint('📋 Data type: ${data?['data']?.runtimeType}');

        // API trả về với structure: {error: null, message: "Lấy danh sách đơn hàng với bàn ăn thành công", data: {...}}
        // Success khi error == null hoặc error == false
        final bool isSuccess = (data['error'] == false) || (data['error'] == null);

        debugPrint('📋 Is Success: $isSuccess');
        debugPrint('📋 Error value: ${data['error']}');
        debugPrint('📋 Message: ${data['message']}');

        if (isSuccess && data['data'] != null) {
          final dataObj = data['data'] as Map<String, dynamic>;
          final List<dynamic> ordersData = dataObj['orders'] as List<dynamic>? ?? [];
          debugPrint('📋 Orders count: ${ordersData.length}');
          debugPrint('📋 Total: ${dataObj['total']}');
          debugPrint('📋 Page: ${dataObj['page']}');

          // TEMPORARY: Add mock data if no orders from API (for testing)
          if (ordersData.isEmpty) {
            debugPrint('📋 No orders from API, using mock data for testing');
            return _getMockInvoices();
          }

          // Debug first order structure
          if (ordersData.isNotEmpty) {
            debugPrint('📋 First order keys: ${ordersData.first.keys}');
          }

          return ordersData
              .map((orderJson) => InvoiceModel.fromOrderJson(orderJson))
              .toList();
        } else {
          debugPrint('📋 API Error: ${data['message']}');
          throw ServerException(
            message: data['message'] ?? 'Failed to load invoices',
          );
        }
      } else {
        debugPrint('📋 HTTP Error: ${response.statusCode}');
        throw ServerException(
          message: 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to load invoices: ${e.toString()}',
      );
    }
  }

  @override
  Future<InvoiceModel> getInvoiceDetail(String invoiceId) async {
    try {
      final response = await apiClient.get('/user/api/orders/$invoiceId');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['error'] == false && data['data'] != null) {
          return InvoiceModel.fromOrderJson(data['data']);
        } else {
          throw ServerException(
            message: data['message'] ?? 'Failed to load invoice detail',
          );
        }
      } else {
        throw ServerException(
          message: 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to load invoice detail: ${e.toString()}',
      );
    }
  }

  @override
  Future<InvoiceModel> updateInvoiceStatus(
    String invoiceId,
    InvoiceStatus status,
  ) async {
    try {
      final response = await apiClient.post(
        '/user/api/update-order-table-status/$invoiceId',
        data: {
          'newStatus': _mapInvoiceStatusToInt(status),
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['error'] == false) {
          // Lấy lại thông tin invoice sau khi update
          return await getInvoiceDetail(invoiceId);
        } else {
          throw ServerException(
            message: data['message'] ?? 'Failed to update invoice status',
          );
        }
      } else {
        throw ServerException(
          message: 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to update invoice status: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> deleteInvoice(String invoiceId) async {
    try {
      final response = await apiClient.delete('/user/api/orders/$invoiceId');

      if (response.statusCode != 200) {
        throw ServerException(
          message: 'Failed to delete invoice: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to delete invoice: ${e.toString()}',
      );
    }
  }

  @override
  Future<InvoiceModel> createInvoice(InvoiceModel invoice) async {
    try {
      final response = await apiClient.post(
        '/user/api/create-order-with-table',
        data: invoice.toCreateOrderJson(),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = response.data;
        if (data['error'] == false && data['data'] != null) {
          return InvoiceModel.fromOrderJson(data['data']);
        } else {
          throw ServerException(
            message: data['message'] ?? 'Failed to create invoice',
          );
        }
      } else {
        throw ServerException(
          message: 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to create invoice: ${e.toString()}',
      );
    }
  }

  @override
  Future<String> exportInvoiceToPdf(String invoiceId) async {
    try {
      final response = await apiClient.get('/user/api/export-invoice/$invoiceId');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['error'] == false && data['data'] != null && data['data']['pdfUrl'] != null) {
          return data['data']['pdfUrl'];
        } else {
          throw ServerException(
            message: data['message'] ?? 'Failed to export invoice to PDF',
          );
        }
      } else {
        throw ServerException(
          message: 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to export invoice to PDF: ${e.toString()}',
      );
    }
  }

  @override
  Future<Map<String, dynamic>> getInvoiceStats({
    required String storeId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'storeId': storeId,
      };

      if (fromDate != null) {
        queryParams['fromDate'] = fromDate.toIso8601String();
      }

      if (toDate != null) {
        queryParams['toDate'] = toDate.toIso8601String();
      }

      final response = await apiClient.get(
        '/user/api/table-order-stats',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['error'] == false && data['data'] != null) {
          return data['data'];
        } else {
          throw ServerException(
            message: data['message'] ?? 'Failed to load invoice stats',
          );
        }
      } else {
        throw ServerException(
          message: 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to load invoice stats: ${e.toString()}',
      );
    }
  }

  /// Map InvoiceStatus to integer for API
  int _mapInvoiceStatusToInt(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.pending:
        return 0;
      case InvoiceStatus.processing:
        return 1;
      case InvoiceStatus.ready:
        return 2;
      case InvoiceStatus.paid:
        return 3;
      case InvoiceStatus.cancelled:
        return 4;
      case InvoiceStatus.refunded:
        return 5;
    }
  }

  /// TEMPORARY: Mock data for testing when API returns empty
  List<InvoiceModel> _getMockInvoices() {
    final now = DateTime.now();

    return [
      InvoiceModel.fromOrderJson({
        "_id": "67a1234567890abcdef12345",
        "orderId": "ORD-2025-001",
        "paymentId": "PAY-2025-001",
        "code": 2025001,
        "customerName": "Nguyen Van Test",
        "customerPhone": "**********",
        "customerEmail": "<EMAIL>",
        "status": 1, // Processing
        "totalAmount": 65000,
        "subtotal": 65000,
        "discountAmount": 0,
        "transportFee": 0,
        "orderTime": now.subtract(const Duration(hours: 2)).toIso8601String(),
        "completedTime": null,
        "notes": "Test order for invoice",
        "paymentMethod": "cash",
        "bankCode": null,
        "isPayOnline": false,
        "serviceType": "dine-in",
        "coupon": null,
        "storeId": "623a97cdbe781e0ba814f227",
        "staffId": "staff123",
        "staffName": "Nguyen Van Staff",
        "products": [
          {
            "_id": "prod1",
            "productId": "prod1",
            "name": "Cà phê đen",
            "quantity": 2,
            "price": 25000,
            "totalPrice": 50000,
            "unit": "ly",
            "notes": null
          },
          {
            "_id": "prod2",
            "productId": "prod2",
            "name": "Bánh mì",
            "quantity": 1,
            "price": 15000,
            "totalPrice": 15000,
            "unit": "cái",
            "notes": "Không hành"
          }
        ],
        "tableInfo": {
          "tableId": "623a97cdbe781e0ba814f229",
          "tableName": "Bàn 01",
          "areaId": "623a97cdbe781e0ba814f228",
          "areaName": "Tầng 1",
          "capacity": 4,
          "status": 1
        }
      }),
      InvoiceModel.fromOrderJson({
        "_id": "67a1234567890abcdef12346",
        "orderId": "ORD-2025-002",
        "paymentId": "PAY-2025-002",
        "code": 2025002,
        "customerName": "Tran Thi Demo",
        "customerPhone": "**********",
        "customerEmail": null,
        "status": 3, // Paid
        "totalAmount": 120000,
        "subtotal": 120000,
        "discountAmount": 10000,
        "transportFee": 0,
        "orderTime": now.subtract(const Duration(hours: 1)).toIso8601String(),
        "completedTime": now.subtract(const Duration(minutes: 30)).toIso8601String(),
        "notes": "Khách VIP",
        "paymentMethod": "card",
        "bankCode": "VCB",
        "isPayOnline": true,
        "serviceType": "dine-in",
        "coupon": "DISCOUNT10",
        "storeId": "623a97cdbe781e0ba814f227",
        "staffId": "staff456",
        "staffName": "Le Van Manager",
        "products": [
          {
            "_id": "prod3",
            "productId": "prod3",
            "name": "Cà phê sữa",
            "quantity": 2,
            "price": 30000,
            "totalPrice": 60000,
            "unit": "ly",
            "notes": "Ít đường"
          },
          {
            "_id": "prod4",
            "productId": "prod4",
            "name": "Bánh ngọt",
            "quantity": 3,
            "price": 20000,
            "totalPrice": 60000,
            "unit": "cái",
            "notes": null
          }
        ],
        "tableInfo": {
          "tableId": "623a97cdbe781e0ba814f230",
          "tableName": "Bàn 05",
          "areaId": "623a97cdbe781e0ba814f228",
          "areaName": "Tầng 1",
          "capacity": 6,
          "status": 0
        }
      }),
      InvoiceModel.fromOrderJson({
        "_id": "67a1234567890abcdef12347",
        "orderId": "ORD-2025-003",
        "paymentId": "PAY-2025-003",
        "code": 2025003,
        "customerName": "Pham Van Sample",
        "customerPhone": "**********",
        "customerEmail": "<EMAIL>",
        "status": 4, // Cancelled
        "totalAmount": 45000,
        "subtotal": 45000,
        "discountAmount": 0,
        "transportFee": 5000,
        "orderTime": now.subtract(const Duration(hours: 3)).toIso8601String(),
        "completedTime": null,
        "notes": "Khách hủy đơn",
        "paymentMethod": null,
        "bankCode": null,
        "isPayOnline": false,
        "serviceType": "takeaway",
        "coupon": null,
        "storeId": "623a97cdbe781e0ba814f227",
        "staffId": "staff123",
        "staffName": "Nguyen Van Staff",
        "products": [
          {
            "_id": "prod5",
            "productId": "prod5",
            "name": "Trà đá",
            "quantity": 3,
            "price": 15000,
            "totalPrice": 45000,
            "unit": "ly",
            "notes": null
          }
        ],
        "tableInfo": null
      }),
    ];
  }
}
