import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/invoice.dart';
import '../../domain/entities/invoice_status.dart';
import '../../domain/repositories/invoice_repository.dart';
import '../datasources/invoice_remote_data_source.dart';
import '../models/invoice_model.dart';

/// Implementation của InvoiceRepository sử dụng API
class InvoiceRepositoryImpl implements InvoiceRepository {
  final InvoiceRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  InvoiceRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, InvoiceSearchResult>> getInvoices({
    required int page,
    required int limit,
    InvoiceStatus? status,
    String? search,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        // Sử dụng storeId mặc định - trong thực tế sẽ lấy từ user session
        const storeId = '623a97cdbe781e0ba814f227';
        
        final invoiceModels = await remoteDataSource.getInvoices(
          storeId: storeId,
          page: page,
          limit: limit,
          status: status,
          search: search,
          startDate: startDate,
          endDate: endDate,
        );

        final invoices = invoiceModels.map((model) => model.toEntity()).toList();

        // Tính toán pagination - tạm thời sử dụng logic đơn giản
        final totalInvoices = invoices.length;
        final totalPages = (totalInvoices / limit).ceil();
        final hasMore = page < totalPages;

        final result = InvoiceSearchResult(
          invoices: invoices,
          currentPage: page,
          totalPages: totalPages,
          totalInvoices: totalInvoices,
          hasMore: hasMore,
        );

        return Right(result);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Invoice>> getInvoiceDetail(String invoiceId) async {
    if (await networkInfo.isConnected) {
      try {
        final invoiceModel = await remoteDataSource.getInvoiceDetail(invoiceId);
        final invoice = invoiceModel.toEntity();
        return Right(invoice);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Invoice>> updateInvoiceStatus(
    String invoiceId,
    InvoiceStatus status,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final updatedInvoiceModel = await remoteDataSource.updateInvoiceStatus(
          invoiceId,
          status,
        );
        final updatedInvoice = updatedInvoiceModel.toEntity();
        return Right(updatedInvoice);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteInvoice(String invoiceId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteInvoice(invoiceId);
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Invoice>> createInvoice(Invoice invoice) async {
    if (await networkInfo.isConnected) {
      try {
        final invoiceModel = InvoiceModel.fromEntity(invoice);
        final createdInvoiceModel = await remoteDataSource.createInvoice(invoiceModel);
        final createdInvoice = createdInvoiceModel.toEntity();
        return Right(createdInvoice);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, String>> exportInvoiceToPdf(String invoiceId) async {
    if (await networkInfo.isConnected) {
      try {
        final pdfUrl = await remoteDataSource.exportInvoiceToPdf(invoiceId);
        return Right(pdfUrl);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
