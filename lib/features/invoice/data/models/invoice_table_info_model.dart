import '../../domain/entities/invoice_table_info.dart';

/// Model cho InvoiceTableInfo với khả năng serialize/deserialize JSON
class InvoiceTableInfoModel extends InvoiceTableInfo {
  const InvoiceTableInfoModel({
    required String tableId,
    required String tableName,
    required String areaId,
    required String areaName,
    required int capacity,
    required int status,
  }) : super(
          tableId: tableId,
          tableName: tableName,
          areaId: areaId,
          areaName: areaName,
          capacity: capacity,
          status: status,
        );

  /// Tạo InvoiceTableInfoModel từ JSON
  factory InvoiceTableInfoModel.fromJson(Map<String, dynamic> json) {
    return InvoiceTableInfoModel(
      tableId: json['tableId'] as String? ?? '',
      tableName: json['tableName'] as String? ?? '',
      areaId: json['areaId'] as String? ?? '',
      areaName: json['areaName'] as String? ?? '',
      capacity: json['capacity'] as int? ?? 0,
      status: json['status'] as int? ?? 0,
    );
  }

  /// <PERSON><PERSON><PERSON><PERSON> đổi sang JSON
  Map<String, dynamic> toJson() {
    return {
      'tableId': tableId,
      'tableName': tableName,
      'areaId': areaId,
      'areaName': areaName,
      'capacity': capacity,
      'status': status,
    };
  }

  /// Chuyển đổi sang Entity
  InvoiceTableInfo toEntity() {
    return InvoiceTableInfo(
      tableId: tableId,
      tableName: tableName,
      areaId: areaId,
      areaName: areaName,
      capacity: capacity,
      status: status,
    );
  }

  /// Tạo từ Entity
  factory InvoiceTableInfoModel.fromEntity(InvoiceTableInfo entity) {
    return InvoiceTableInfoModel(
      tableId: entity.tableId,
      tableName: entity.tableName,
      areaId: entity.areaId,
      areaName: entity.areaName,
      capacity: entity.capacity,
      status: entity.status,
    );
  }

  /// Tạo bản sao với các thuộc tính được cập nhật
  @override
  InvoiceTableInfoModel copyWith({
    String? tableId,
    String? tableName,
    String? areaId,
    String? areaName,
    int? capacity,
    int? status,
  }) {
    return InvoiceTableInfoModel(
      tableId: tableId ?? this.tableId,
      tableName: tableName ?? this.tableName,
      areaId: areaId ?? this.areaId,
      areaName: areaName ?? this.areaName,
      capacity: capacity ?? this.capacity,
      status: status ?? this.status,
    );
  }
}
