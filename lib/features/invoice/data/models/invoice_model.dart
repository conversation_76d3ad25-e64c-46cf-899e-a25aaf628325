import 'package:flutter/foundation.dart';
import '../../domain/entities/invoice.dart';
import '../../domain/entities/invoice_status.dart';
import '../../domain/entities/payment_method.dart';
import '../../domain/entities/invoice_table_info.dart';
import 'invoice_item_model.dart';
import 'invoice_table_info_model.dart';

/// Model cho Invoice với khả năng serialize/deserialize JSON (tương thích với Orders API)
class InvoiceModel extends Invoice {
  const InvoiceModel({
    required String id,
    required String orderId,
    required String paymentId,
    required int code,
    required String invoiceNumber,
    required String customerName,
    required String customerPhone,
    String? customerEmail,
    required String tableNumber,
    InvoiceTableInfo? tableInfo,
    required DateTime createdDate,
    DateTime? completedDate,
    required double totalAmount,
    required double subtotal,
    double discountAmount = 0.0,
    double transportFee = 0.0,
    required InvoiceStatus status,
    required List<InvoiceItemModel> items,
    String? notes,
    PaymentMethod? paymentMethod,
    String? bankCode,
    bool isPayOnline = false,
    required String serviceType,
    String? coupon,
    required String storeId,
    String? staffId,
    String? staffName,
  }) : super(
          id: id,
          orderId: orderId,
          paymentId: paymentId,
          code: code,
          invoiceNumber: invoiceNumber,
          customerName: customerName,
          customerPhone: customerPhone,
          customerEmail: customerEmail,
          tableNumber: tableNumber,
          tableInfo: tableInfo,
          createdDate: createdDate,
          completedDate: completedDate,
          totalAmount: totalAmount,
          subtotal: subtotal,
          discountAmount: discountAmount,
          transportFee: transportFee,
          status: status,
          items: items,
          notes: notes,
          paymentMethod: paymentMethod,
          bankCode: bankCode,
          isPayOnline: isPayOnline,
          serviceType: serviceType,
          coupon: coupon,
          storeId: storeId,
          staffId: staffId,
          staffName: staffName,
        );

  /// Tạo InvoiceModel từ Order JSON (từ API orders-with-tables)
  factory InvoiceModel.fromOrderJson(Map<String, dynamic> json) {
    try {
      // Debug logging
      debugPrint('🔄 Parsing order JSON with keys: ${json.keys}');

      // Parse items từ products array
      final productsJson = json['products'] as List<dynamic>? ?? [];
      debugPrint('🔄 Products count: ${productsJson.length}');

      if (productsJson.isNotEmpty) {
        debugPrint('🔄 First product keys: ${productsJson.first.keys.toList()}');
        debugPrint('🔄 First product data: ${productsJson.first}');
      }

      final items = productsJson
          .map((productJson) => InvoiceItemModel.fromOrderProductJson(productJson as Map<String, dynamic>))
          .toList();

      // Parse table info
      InvoiceTableInfo? tableInfo;
      if (json['tableInfo'] != null) {
        tableInfo = InvoiceTableInfoModel.fromJson(json['tableInfo'] as Map<String, dynamic>);
      }

      // Get service type from API response
      String serviceType = json['serviceType'] as String? ?? 'coffee';
      debugPrint('🔄 Service type: $serviceType');

      // Map order status to invoice status
      final orderStatus = json['status'] as int? ?? 0;
      final invoiceStatus = _mapOrderStatusToInvoiceStatus(orderStatus);

      return InvoiceModel(
        id: json['_id'] as String? ?? '',
        orderId: json['orderId'] as String? ?? '',
        paymentId: json['paymentId'] as String? ?? '',
        code: json['code'] as int? ?? 0,
        invoiceNumber: '#${json['code'] ?? 0}${tableInfo != null ? '-${tableInfo.tableName}' : ''}',
        customerName: json['customerName'] as String? ?? 'Khách hàng',
        customerPhone: json['phone'] as String? ?? '',
        customerEmail: json['email'] as String?,
        tableNumber: tableInfo?.tableName ?? 'N/A',
        tableInfo: tableInfo,
        createdDate: _parseDateTime(json['orderTime']) ?? DateTime.now(),
        completedDate: json['completedTime'] != null ? _parseDateTime(json['completedTime']) : null,
        totalAmount: _parseDouble(json['totalAmount']) ?? 0.0,
        subtotal: _parseDouble(json['subtotal']) ?? _parseDouble(json['totalAmount']) ?? 0.0,
        discountAmount: _parseDouble(json['discountAmount']) ?? 0.0,
        transportFee: _parseDouble(json['transportFee']) ?? 0.0,
        status: invoiceStatus,
        items: items,
        notes: json['note'] as String?,
        paymentMethod: json['paymentMethod'] != null
            ? _mapPaymentMethodFromInt(json['paymentMethod'] as int)
            : null,
        bankCode: json['bankCode'] as String?,
        isPayOnline: _parseBoolFromInt(json['isPayOnline']),
        serviceType: serviceType,
        coupon: json['coupon'] as String?,
        storeId: json['storeId'] as String? ?? '',
        staffId: json['staffId'] as String?,
        staffName: json['staffName'] as String?,
      );
    } catch (e) {
      throw FormatException('Failed to parse InvoiceModel from Order JSON: $e');
    }
  }

  /// Chuyển đổi sang JSON để tạo order mới
  Map<String, dynamic> toCreateOrderJson() {
    return {
      'storeId': storeId,
      'areaId': tableInfo?.areaId ?? '',
      'tableId': tableInfo?.tableId ?? '',
      'customerName': customerName,
      'customerPhone': customerPhone,
      'customerEmail': customerEmail,
      'products': items.map((item) => (item as InvoiceItemModel).toOrderProductJson()).toList(),
      'totalAmount': totalAmount,
      'subtotal': subtotal,
      'discountAmount': discountAmount,
      'transportFee': transportFee,
      'notes': notes,
      'paymentMethod': paymentMethod?.value,
      'bankCode': bankCode,
      'isPayOnline': isPayOnline,
      'serviceType': serviceType,
      'coupon': coupon,
    };
  }

  /// Chuyển đổi sang JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'orderId': orderId,
      'paymentId': paymentId,
      'code': code,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'customerEmail': customerEmail,
      'tableInfo': tableInfo != null ? (tableInfo as InvoiceTableInfoModel).toJson() : null,
      'orderTime': createdDate.toIso8601String(),
      'completedTime': completedDate?.toIso8601String(),
      'totalAmount': totalAmount,
      'subtotal': subtotal,
      'discountAmount': discountAmount,
      'transportFee': transportFee,
      'status': _mapInvoiceStatusToOrderStatus(status),
      'products': items.map((item) => (item as InvoiceItemModel).toOrderProductJson()).toList(),
      'notes': notes,
      'paymentMethod': paymentMethod?.value,
      'bankCode': bankCode,
      'isPayOnline': isPayOnline,
      'serviceType': serviceType,
      'coupon': coupon,
      'storeId': storeId,
      'staffId': staffId,
      'staffName': staffName,
    };
  }

  /// Chuyển đổi sang Entity
  Invoice toEntity() {
    return Invoice(
      id: id,
      orderId: orderId,
      paymentId: paymentId,
      code: code,
      invoiceNumber: invoiceNumber,
      customerName: customerName,
      customerPhone: customerPhone,
      customerEmail: customerEmail,
      tableNumber: tableNumber,
      tableInfo: tableInfo,
      createdDate: createdDate,
      completedDate: completedDate,
      totalAmount: totalAmount,
      subtotal: subtotal,
      discountAmount: discountAmount,
      transportFee: transportFee,
      status: status,
      items: items.map((item) => (item as InvoiceItemModel).toEntity()).toList(),
      notes: notes,
      paymentMethod: paymentMethod,
      bankCode: bankCode,
      isPayOnline: isPayOnline,
      serviceType: serviceType,
      coupon: coupon,
      storeId: storeId,
      staffId: staffId,
      staffName: staffName,
    );
  }

  /// Tạo từ Entity
  factory InvoiceModel.fromEntity(Invoice entity) {
    return InvoiceModel(
      id: entity.id,
      orderId: entity.orderId,
      paymentId: entity.paymentId,
      code: entity.code,
      invoiceNumber: entity.invoiceNumber,
      customerName: entity.customerName,
      customerPhone: entity.customerPhone,
      customerEmail: entity.customerEmail,
      tableNumber: entity.tableNumber,
      tableInfo: entity.tableInfo,
      createdDate: entity.createdDate,
      completedDate: entity.completedDate,
      totalAmount: entity.totalAmount,
      subtotal: entity.subtotal,
      discountAmount: entity.discountAmount,
      transportFee: entity.transportFee,
      status: entity.status,
      items: entity.items.map((item) => InvoiceItemModel.fromEntity(item)).toList(),
      notes: entity.notes,
      paymentMethod: entity.paymentMethod,
      bankCode: entity.bankCode,
      isPayOnline: entity.isPayOnline,
      serviceType: entity.serviceType,
      coupon: entity.coupon,
      storeId: entity.storeId,
      staffId: entity.staffId,
      staffName: entity.staffName,
    );
  }

  /// Helper method để parse DateTime
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// Helper method để parse double
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// Map order status (int) to InvoiceStatus
  static InvoiceStatus _mapOrderStatusToInvoiceStatus(int orderStatus) {
    switch (orderStatus) {
      case 0:
        return InvoiceStatus.pending;
      case 1:
        return InvoiceStatus.processing;
      case 2:
        return InvoiceStatus.ready;
      case 3:
        return InvoiceStatus.paid;
      case 4:
        return InvoiceStatus.cancelled;
      case 5:
        return InvoiceStatus.refunded;
      default:
        return InvoiceStatus.pending;
    }
  }

  /// Map payment method từ int (API) sang PaymentMethod enum
  static PaymentMethod? _mapPaymentMethodFromInt(int paymentMethodInt) {
    switch (paymentMethodInt) {
      case 0:
        return PaymentMethod.cash;
      case 1:
        return PaymentMethod.card;
      case 2:
        return PaymentMethod.transfer;
      default:
        return PaymentMethod.cash;
    }
  }

  /// Parse bool từ int (API trả về 0/1 thay vì true/false)
  static bool _parseBoolFromInt(dynamic value) {
    if (value is bool) return value;
    if (value is int) return value == 1;
    if (value is String) return value == '1' || value.toLowerCase() == 'true';
    return false;
  }

  /// Map InvoiceStatus to order status (int)
  static int _mapInvoiceStatusToOrderStatus(InvoiceStatus invoiceStatus) {
    switch (invoiceStatus) {
      case InvoiceStatus.pending:
        return 0;
      case InvoiceStatus.processing:
        return 1;
      case InvoiceStatus.ready:
        return 2;
      case InvoiceStatus.paid:
        return 3;
      case InvoiceStatus.cancelled:
        return 4;
      case InvoiceStatus.refunded:
        return 5;
    }
  }
}
