import '../../domain/entities/invoice_item.dart';

/// Model cho InvoiceItem với khả năng serialize/deserialize JSON
class InvoiceItemModel extends InvoiceItem {
  const InvoiceItemModel({
    required String id,
    required String productName,
    required int quantity,
    required double unitPrice,
    required double totalPrice,
    String? notes,
    String? productId,
    String? unit,
  }) : super(
          id: id,
          productName: productName,
          quantity: quantity,
          unitPrice: unitPrice,
          totalPrice: totalPrice,
          notes: notes,
          productId: productId,
          unit: unit,
        );

  /// Tạo InvoiceItemModel từ JSON
  factory InvoiceItemModel.fromJson(Map<String, dynamic> json) {
    try {
      final quantity = json['quantity'] as int? ?? 1;
      final unitPrice = _parseDouble(json['unitPrice']);
      
      return InvoiceItemModel(
        id: json['id'] as String? ?? '',
        productName: json['productName'] as String? ?? '',
        quantity: quantity,
        unitPrice: unitPrice,
        totalPrice: _parseDouble(json['totalPrice']) ?? (quantity * unitPrice),
        notes: json['notes'] as String?,
        productId: json['productId'] as String?,
        unit: json['unit'] as String?,
      );
    } catch (e) {
      throw FormatException('Lỗi parse InvoiceItemModel: $e');
    }
  }

  /// Chuyển InvoiceItemModel thành JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productName': productName,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'notes': notes,
      'productId': productId,
      'unit': unit,
    };
  }

  /// Chuyển model thành entity
  InvoiceItem toEntity() {
    return InvoiceItem(
      id: id,
      productName: productName,
      quantity: quantity,
      unitPrice: unitPrice,
      totalPrice: totalPrice,
      notes: notes,
      productId: productId,
      unit: unit,
    );
  }

  /// Tạo model từ entity
  factory InvoiceItemModel.fromEntity(InvoiceItem entity) {
    return InvoiceItemModel(
      id: entity.id,
      productName: entity.productName,
      quantity: entity.quantity,
      unitPrice: entity.unitPrice,
      totalPrice: entity.totalPrice,
      notes: entity.notes,
      productId: entity.productId,
      unit: entity.unit,
    );
  }

  /// Tạo InvoiceItemModel từ Order Product JSON
  factory InvoiceItemModel.fromOrderProductJson(Map<String, dynamic> json) {
    try {
      final quantity = json['count'] as int? ?? json['quantity'] as int? ?? 1;
      final unitPrice = _parseDouble(json['productPrice'] ?? json['price']);

      return InvoiceItemModel(
        id: json['_id'] as String? ?? '',
        productName: json['productName'] as String? ?? json['name'] as String? ?? '',
        quantity: quantity,
        unitPrice: unitPrice,
        totalPrice: _parseDouble(json['price'] ?? json['totalPrice']) != 0.0 ? _parseDouble(json['price'] ?? json['totalPrice']) : (quantity * unitPrice),
        notes: json['noteProduct'] as String? ?? json['notes'] as String?,
        productId: json['productId'] as String? ?? json['_id'],
        unit: json['unit'] as String? ?? 'cái',
      );
    } catch (e) {
      throw FormatException('Failed to parse InvoiceItemModel from Order Product JSON: $e');
    }
  }

  /// Chuyển đổi sang Order Product JSON
  Map<String, dynamic> toOrderProductJson() {
    return {
      '_id': id,
      'productId': productId ?? id,
      'name': productName,
      'quantity': quantity,
      'price': unitPrice,
      'totalPrice': totalPrice,
      'notes': notes,
      'unit': unit ?? 'cái',
    };
  }

  /// Helper method để parse double từ dynamic
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  /// Tạo bản sao với các thuộc tính được cập nhật
  @override
  InvoiceItemModel copyWith({
    String? id,
    String? productName,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    String? notes,
    String? productId,
    String? unit,
  }) {
    return InvoiceItemModel(
      id: id ?? this.id,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      notes: notes ?? this.notes,
      productId: productId ?? this.productId,
      unit: unit ?? this.unit,
    );
  }
}
