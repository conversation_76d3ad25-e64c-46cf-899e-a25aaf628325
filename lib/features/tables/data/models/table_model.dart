import '../../domain/entities/table_entity.dart';
import '../../../../models/table_api_models.dart';

/// Model để convert giữa API response và Domain Entity
class TableModel extends TableEntity {
  const TableModel({
    required super.tableId,
    required super.name,
    required super.status,
    required super.capacity,
    required super.position,
    required super.size,
    super.shape,
    super.note,
    super.priority,
    super.tags,
    super.currentOrderId,
    super.currentCustomerName,
    super.currentCustomerPhone,
    super.reservedAt,
    super.estimatedDuration,
    required super.areaId,
    required super.areaName,
    super.areaDescription,
    super.areaDisplayOrder,
    super.lastUpdated,
    super.isActive,
  });

  /// Convert từ TableApiModel sang TableModel
  factory TableModel.fromApiModel(TableApiModel apiModel) {
    return TableModel(
      tableId: apiModel.tableId,
      name: apiModel.name,
      status: apiModel.status,
      capacity: apiModel.capacity,
      position: TablePositionModel.fromApiModel(apiModel.position),
      size: TableSizeModel.fromApiModel(apiModel.size),
      shape: apiModel.shape,
      note: apiModel.note,
      priority: apiModel.priority,
      tags: apiModel.tags,
      currentOrderId: apiModel.currentOrderId,
      currentCustomerName: apiModel.currentCustomerName,
      currentCustomerPhone: apiModel.currentCustomerPhone,
      reservedAt: apiModel.reservedAt != null 
          ? DateTime.fromMillisecondsSinceEpoch(apiModel.reservedAt!)
          : null,
      estimatedDuration: apiModel.estimatedDuration,
      areaId: apiModel.areaId,
      areaName: apiModel.areaName,
      areaDescription: apiModel.areaDescription,
      areaDisplayOrder: apiModel.areaDisplayOrder,
      lastUpdated: apiModel.lastUpdated != null
          ? DateTime.fromMillisecondsSinceEpoch(apiModel.lastUpdated!)
          : null,
      isActive: apiModel.isActive,
    );
  }

  /// Convert sang TableEntity
  TableEntity toEntity() {
    return TableEntity(
      tableId: tableId,
      name: name,
      status: status,
      capacity: capacity,
      position: position,
      size: size,
      shape: shape,
      note: note,
      priority: priority,
      tags: tags,
      currentOrderId: currentOrderId,
      currentCustomerName: currentCustomerName,
      currentCustomerPhone: currentCustomerPhone,
      reservedAt: reservedAt,
      estimatedDuration: estimatedDuration,
      areaId: areaId,
      areaName: areaName,
      areaDescription: areaDescription,
      areaDisplayOrder: areaDisplayOrder,
      lastUpdated: lastUpdated,
      isActive: isActive,
    );
  }
}

/// Model cho TablePosition
class TablePositionModel extends TablePositionEntity {
  const TablePositionModel({
    super.x,
    super.y,
  });

  factory TablePositionModel.fromApiModel(TablePosition apiModel) {
    return TablePositionModel(
      x: apiModel.x,
      y: apiModel.y,
    );
  }
}

/// Model cho TableSize
class TableSizeModel extends TableSizeEntity {
  const TableSizeModel({
    super.width,
    super.height,
  });

  factory TableSizeModel.fromApiModel(TableSize apiModel) {
    return TableSizeModel(
      width: apiModel.width,
      height: apiModel.height,
    );
  }
}

/// Model cho TableStats
class TableStatsModel extends TableStatsEntity {
  const TableStatsModel({
    super.total,
    super.available,
    super.occupied,
    super.reserved,
    super.disabled,
    super.totalCapacity,
    super.areas,
  });

  factory TableStatsModel.fromApiModel(TableStats apiModel) {
    return TableStatsModel(
      total: apiModel.total,
      available: apiModel.available,
      occupied: apiModel.occupied,
      reserved: apiModel.reserved,
      disabled: apiModel.disabled,
      totalCapacity: apiModel.totalCapacity,
      areas: apiModel.areas,
    );
  }
}

/// Model cho AllTablesResponse
class AllTablesModel extends AllTablesEntity {
  const AllTablesModel({
    required super.tables,
    required super.stats,
    required super.storeId,
    super.statusFilter,
  });

  factory AllTablesModel.fromApiResponse(AllTablesResponse apiResponse, String storeId, List<String>? statusFilter) {
    return AllTablesModel(
      tables: apiResponse.tables.map((table) => TableModel.fromApiModel(table)).toList(),
      stats: TableStatsModel.fromApiModel(apiResponse.stats),
      storeId: storeId,
      statusFilter: statusFilter,
    );
  }
}
