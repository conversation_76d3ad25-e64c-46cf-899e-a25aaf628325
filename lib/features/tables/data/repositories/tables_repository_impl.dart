import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/table_entity.dart';
import '../../domain/repositories/tables_repository.dart';
import '../datasources/tables_remote_data_source.dart';
import '../models/table_model.dart';

/// Implementation của TablesRepository
class TablesRepositoryImpl implements TablesRepository {
  final TablesRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  TablesRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, AllTablesEntity>> getAllTables({
    required String storeId,
    List<String>? statusFilter,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteResponse = await remoteDataSource.getAllTables(
          storeId: storeId,
          statusFilter: statusFilter,
        );

        final allTablesModel = AllTablesModel.fromApiResponse(
          remoteResponse,
          storeId,
          statusFilter,
        );

        return Right(allTablesModel);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } on UnauthorizedException {
        return Left(UnauthorizedFailure());
      } on NotFoundException catch (e) {
        return Left(NotFoundFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'Không có kết nối mạng'));
    }
  }

  @override
  Future<Either<Failure, TableStatsEntity>> getStoreTableStats({
    required String storeId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteResponse = await remoteDataSource.getStoreTableStats(
          storeId: storeId,
        );

        final statsModel = TableStatsModel.fromApiModel(remoteResponse);
        return Right(statsModel);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } on UnauthorizedException {
        return Left(UnauthorizedFailure());
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'Không có kết nối mạng'));
    }
  }

  @override
  Future<Either<Failure, bool>> updateTableStatus({
    required String areaId,
    required String tableId,
    required String storeId,
    required String status,
    String? currentOrderId,
    String? currentCustomerName,
    String? currentCustomerPhone,
    DateTime? reservedAt,
    int? estimatedDuration,
    String? note,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.updateTableStatus(
          areaId: areaId,
          tableId: tableId,
          storeId: storeId,
          status: status,
          currentOrderId: currentOrderId,
          currentCustomerName: currentCustomerName,
          currentCustomerPhone: currentCustomerPhone,
          reservedAt: reservedAt?.millisecondsSinceEpoch,
          estimatedDuration: estimatedDuration,
          note: note,
        );

        return Right(result);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } on UnauthorizedException {
        return Left(UnauthorizedFailure());
      } on NotFoundException catch (e) {
        return Left(NotFoundFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'Không có kết nối mạng'));
    }
  }
}
