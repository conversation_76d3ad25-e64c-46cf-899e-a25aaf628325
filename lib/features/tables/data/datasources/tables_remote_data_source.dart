import 'package:dio/dio.dart';
import '../../../../core/network/api_client.dart';
import '../../../../models/table_api_models.dart';
import '../../../../core/errors/exceptions.dart';

/// Abstract class cho Tables Remote Data Source
abstract class TablesRemoteDataSource {
  Future<AllTablesResponse> getAllTables({
    required String storeId,
    List<String>? statusFilter,
  });

  Future<TableStats> getStoreTableStats({
    required String storeId,
  });

  Future<bool> updateTableStatus({
    required String areaId,
    required String tableId,
    required String storeId,
    required String status,
    String? currentOrderId,
    String? currentCustomerName,
    String? currentCustomerPhone,
    int? reservedAt,
    int? estimatedDuration,
    String? note,
  });
}

/// Implementation của Tables Remote Data Source
class TablesRemoteDataSourceImpl implements TablesRemoteDataSource {
  final ApiClient apiClient;

  TablesRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<AllTablesResponse> getAllTables({
    required String storeId,
    List<String>? statusFilter,
  }) async {
    try {
      final response = await apiClient.getAllTables(
        storeId: storeId,
        statusFilter: statusFilter,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse.fromJson(response.data);

        if (!apiResponse.error && apiResponse.data != null) {
          return AllTablesResponse.fromJson(apiResponse.data!);
        } else {
          throw ServerException(message: apiResponse.message);
        }
      } else {
        throw ServerException(
          message: 'Failed to get tables: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw UnauthorizedException();
      } else if (e.response?.statusCode == 404) {
        throw NotFoundException(message: 'Tables not found');
      } else {
        throw ServerException(
          message: e.message ?? 'Unknown error occurred',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<TableStats> getStoreTableStats({
    required String storeId,
  }) async {
    try {
      final response = await apiClient.getStoreTableStats(storeId: storeId);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse.fromJson(response.data);

        if (!apiResponse.error && apiResponse.data != null) {
          return TableStats.fromJson(apiResponse.data!['stats']);
        } else {
          throw ServerException(message: apiResponse.message);
        }
      } else {
        throw ServerException(
          message: 'Failed to get table stats: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw UnauthorizedException();
      } else {
        throw ServerException(
          message: e.message ?? 'Unknown error occurred',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<bool> updateTableStatus({
    required String areaId,
    required String tableId,
    required String storeId,
    required String status,
    String? currentOrderId,
    String? currentCustomerName,
    String? currentCustomerPhone,
    int? reservedAt,
    int? estimatedDuration,
    String? note,
  }) async {
    try {
      final response = await apiClient.updateTableStatus(
        areaId: areaId,
        tableId: tableId,
        storeId: storeId,
        status: status,
        currentOrderId: currentOrderId,
        currentCustomerName: currentCustomerName,
        currentCustomerPhone: currentCustomerPhone,
        reservedAt: reservedAt,
        estimatedDuration: estimatedDuration,
        note: note,
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse.fromJson(response.data);

        return !apiResponse.error;
      } else {
        throw ServerException(
          message: 'Failed to update table status: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw UnauthorizedException();
      } else if (e.response?.statusCode == 404) {
        throw NotFoundException(message: 'Table not found');
      } else {
        throw ServerException(
          message: e.message ?? 'Unknown error occurred',
        );
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
