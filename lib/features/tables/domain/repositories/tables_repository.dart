import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/table_entity.dart';

/// Repository interface cho Tables feature
abstract class TablesRepository {
  /// L<PERSON>y danh sách tất cả bàn ăn từ tất cả khu vực
  Future<Either<Failure, AllTablesEntity>> getAllTables({
    required String storeId,
    List<String>? statusFilter,
  });

  /// L<PERSON>y thống kê tổng quan bàn ăn của cửa hàng
  Future<Either<Failure, TableStatsEntity>> getStoreTableStats({
    required String storeId,
  });

  /// Cập nhật trạng thái bàn
  Future<Either<Failure, bool>> updateTableStatus({
    required String areaId,
    required String tableId,
    required String storeId,
    required String status,
    String? currentOrderId,
    String? currentCustomerName,
    String? currentCustomerPhone,
    DateTime? reservedAt,
    int? estimatedDuration,
    String? note,
  });
}
