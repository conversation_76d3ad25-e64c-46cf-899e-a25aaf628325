import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/table_entity.dart';
import '../repositories/tables_repository.dart';

/// Use case để lấy thống kê bàn ăn của cửa hàng
class GetStoreTableStatsUseCase implements UseCase<TableStatsEntity, GetStoreTableStatsParams> {
  final TablesRepository repository;

  GetStoreTableStatsUseCase(this.repository);

  @override
  Future<Either<Failure, TableStatsEntity>> call(GetStoreTableStatsParams params) async {
    return await repository.getStoreTableStats(storeId: params.storeId);
  }
}

/// Parameters cho GetStoreTableStatsUseCase
class GetStoreTableStatsParams extends Equatable {
  final String storeId;

  const GetStoreTableStatsParams({required this.storeId});

  @override
  List<Object?> get props => [storeId];
}
