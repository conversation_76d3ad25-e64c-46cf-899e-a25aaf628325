import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/table_entity.dart';
import '../repositories/tables_repository.dart';

/// Use case để lấy danh sách tất cả bàn ăn
class GetAllTablesUseCase implements UseCase<AllTablesEntity, GetAllTablesParams> {
  final TablesRepository repository;

  GetAllTablesUseCase(this.repository);

  @override
  Future<Either<Failure, AllTablesEntity>> call(GetAllTablesParams params) async {
    return await repository.getAllTables(
      storeId: params.storeId,
      statusFilter: params.statusFilter,
    );
  }
}

/// Parameters cho GetAllTablesUseCase
class GetAllTablesParams extends Equatable {
  final String storeId;
  final List<String>? statusFilter;

  const GetAllTablesParams({
    required this.storeId,
    this.statusFilter,
  });

  @override
  List<Object?> get props => [storeId, statusFilter];
}
