import 'package:equatable/equatable.dart';

/// Entity cho Table trong domain layer
class TableEntity extends Equatable {
  final String tableId;
  final String name;
  final String status;
  final int capacity;
  final TablePositionEntity position;
  final TableSizeEntity size;
  final String shape;
  final String? note;
  final int priority;
  final List<String> tags;
  
  // Thông tin đặt bàn hiện tại
  final String? currentOrderId;
  final String? currentCustomerName;
  final String? currentCustomerPhone;
  final DateTime? reservedAt;
  final int? estimatedDuration;
  
  // Thông tin khu vực
  final String areaId;
  final String areaName;
  final String? areaDescription;
  final int areaDisplayOrder;
  
  // Metadata
  final DateTime? lastUpdated;
  final bool isActive;

  const TableEntity({
    required this.tableId,
    required this.name,
    required this.status,
    required this.capacity,
    required this.position,
    required this.size,
    this.shape = 'circle',
    this.note,
    this.priority = 0,
    this.tags = const [],
    this.currentOrderId,
    this.currentCustomerName,
    this.currentCustomerPhone,
    this.reservedAt,
    this.estimatedDuration,
    required this.areaId,
    required this.areaName,
    this.areaDescription,
    this.areaDisplayOrder = 0,
    this.lastUpdated,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [
        tableId,
        name,
        status,
        capacity,
        position,
        size,
        shape,
        note,
        priority,
        tags,
        currentOrderId,
        currentCustomerName,
        currentCustomerPhone,
        reservedAt,
        estimatedDuration,
        areaId,
        areaName,
        areaDescription,
        areaDisplayOrder,
        lastUpdated,
        isActive,
      ];
}

/// Entity cho vị trí bàn
class TablePositionEntity extends Equatable {
  final double x;
  final double y;

  const TablePositionEntity({
    this.x = 0,
    this.y = 0,
  });

  @override
  List<Object?> get props => [x, y];
}

/// Entity cho kích thước bàn
class TableSizeEntity extends Equatable {
  final double width;
  final double height;

  const TableSizeEntity({
    this.width = 60,
    this.height = 60,
  });

  @override
  List<Object?> get props => [width, height];
}

/// Entity cho thống kê bàn ăn
class TableStatsEntity extends Equatable {
  final int total;
  final int available;
  final int occupied;
  final int reserved;
  final int disabled;
  final int totalCapacity;
  final int areas;

  const TableStatsEntity({
    this.total = 0,
    this.available = 0,
    this.occupied = 0,
    this.reserved = 0,
    this.disabled = 0,
    this.totalCapacity = 0,
    this.areas = 0,
  });

  @override
  List<Object?> get props => [
        total,
        available,
        occupied,
        reserved,
        disabled,
        totalCapacity,
        areas,
      ];
}

/// Entity cho response của API all-tables
class AllTablesEntity extends Equatable {
  final List<TableEntity> tables;
  final TableStatsEntity stats;
  final String storeId;
  final List<String>? statusFilter;

  const AllTablesEntity({
    required this.tables,
    required this.stats,
    required this.storeId,
    this.statusFilter,
  });

  @override
  List<Object?> get props => [tables, stats, storeId, statusFilter];
}
