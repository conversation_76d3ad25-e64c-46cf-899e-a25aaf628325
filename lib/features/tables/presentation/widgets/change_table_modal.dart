import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../models/pos_models.dart';
import '../../../../services/mock_data_service.dart';
import 'table_widget.dart';

class ChangeTableModal extends StatefulWidget {
  const ChangeTableModal({super.key});

  @override
  State<ChangeTableModal> createState() => _ChangeTableModalState();
}

class _ChangeTableModalState extends State<ChangeTableModal> {
  late List<RestaurantTable> _availableTables;

  @override
  void initState() {
    super.initState();
    // Filter for available tables from the mock data
    _availableTables = MockDataService.mockTables
        .where((table) => table.status == TableStatus.available)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      titlePadding: const EdgeInsets.only(top: 24, bottom: 16),
      title: Stack(
        alignment: Alignment.center,
        children: [
          const Text(
            'CHỌN BÀN',
            style: TextStyle(
              fontFamily: 'Varela',
              fontWeight: FontWeight.w400,
              fontSize: 28,
              color: Color(0xFF333333),
            ),
          ),
          Positioned(
            right: 16,
            top: -12, // Adjust to align with the title
            child: IconButton(
              icon: Icon(Icons.close, color: Colors.grey[400], size: 32),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
        ],
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      content: SizedBox(
        width: 800, // Fixed width for better control
        height: 500, // Fixed height
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            crossAxisSpacing: 30,
            mainAxisSpacing: 30,
            childAspectRatio: 180 / 180, // Aspect ratio for 180x180 size
          ),
          itemCount: _availableTables.length,
          itemBuilder: (context, index) {
            final table = _availableTables[index];
            return SizedBox(
              width: 180,
              height: 180,
              child: TableWidget(
                table: table,
                onTap: () {
                  // TODO: Implement table change logic
                  Navigator.of(context).pop(table);
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
