import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../models/pos_models.dart';

class TableWidget extends StatelessWidget {
  final RestaurantTable table;
  final VoidCallback? onTap;

  const TableWidget({super.key, required this.table, this.onTap});

  String _getSvgPath(TableStatus status) {
    switch (status) {
      case TableStatus.available:
        return 'assets/images/svg/table-green.svg';
      case TableStatus.occupied:
        return 'assets/images/svg/table-orange.svg';
      case TableStatus.reserved:
        return 'assets/images/svg/table-gray.svg';
      default:
        return 'assets/images/svg/table-gray.svg';
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Stack(
        alignment: Alignment.center,
        children: [
          SvgPicture.asset(
            _getSvgPath(table.status),
            width: 180,
            height: 180,
          ),
          Text(
            table.name.replaceAll('Bàn ', 'B'),
            style: const TextStyle(
              fontFamily: 'Varela',
              fontWeight: FontWeight.w400,
              fontSize: 25,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }
}
