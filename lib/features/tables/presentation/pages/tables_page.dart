import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/pos_colors.dart';
import '../../../../core/theme/pos_typography.dart';
import '../../../../core/theme/pos_spacing.dart';
import '../../../../core/di/injection_container.dart' as di;
import '../../../../models/pos_models.dart';
import '../../../../services/mock_data_service.dart';
import '../../domain/entities/table_entity.dart';
import '../bloc/tables_bloc.dart';

/// Tables Page - Feature-based structure with BLoC
class TablesPage extends StatelessWidget {
  const TablesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<TablesBloc>()
        ..add(const LoadAllTablesEvent(storeId: '623a97cdbe781e0ba814f227')),
      child: const _TablesPageContent(),
    );
  }
}

class _TablesPageContent extends StatefulWidget {
  const _TablesPageContent();

  @override
  State<_TablesPageContent> createState() => _TablesPageContentState();
}

class _TablesPageContentState extends State<_TablesPageContent> {
  final MockDataService _mockDataService = MockDataService();
  List<Order> _orders = [];
  String _selectedArea = 'TẤT CẢ';

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  void _loadOrders() {
    // Load orders from mock service for now
    _orders = _mockDataService.getAllOrders();
  }

  Order? _getOrderForTable(String tableId) {
    try {
      return _orders.firstWhere((order) => order.tableId == tableId);
    } catch (e) {
      return null;
    }
  }

  String _getTableStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return 'Trống';
      case 'occupied':
        return 'Có khách';
      case 'reserved':
        return 'Đã đặt';
      case 'cleaning':
      case 'disabled':
        return 'Dọn dẹp';
      default:
        return 'Không xác định';
    }
  }

  Color _getTableColor(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return Colors.green;
      case 'occupied':
        return Colors.red;
      case 'reserved':
        return Colors.orange;
      case 'cleaning':
      case 'disabled':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _mapDisplayStatusToApi(String displayStatus) {
    switch (displayStatus) {
      case 'TRỐNG':
        return 'available';
      case 'CÓ KHÁCH':
        return 'occupied';
      case 'ĐÃ ĐẶT':
        return 'reserved';
      default:
        return 'available';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PosColors.background,
      body: BlocBuilder<TablesBloc, TablesState>(
        builder: (context, state) {
          if (state is TablesLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is TablesError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Lỗi: ${state.message}',
                    style: PosTypography.bodyLarge,
                  ),
                  const SizedBox(height: PosSpacing.md),
                  ElevatedButton(
                    onPressed: () {
                      context.read<TablesBloc>().add(
                            const RefreshTablesEvent(
                                storeId: '623a97cdbe781e0ba814f227'),
                          );
                    },
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            );
          } else if (state is TablesLoaded) {
            return Column(
              children: [
                _buildFilterSection(context, state.allTables),
                Expanded(
                  child: _buildTablesGrid(context, state.allTables),
                ),
              ],
            );
          }
          return const Center(child: Text('Không có dữ liệu'));
        },
      ),
    );
  }

  Widget _buildFilterSection(BuildContext context, AllTablesEntity allTables) {
    return Container(
      color: PosColors.surface,
      padding: const EdgeInsets.symmetric(
        horizontal: PosSpacing.lg,
        vertical: PosSpacing.md,
      ),
      child: Row(
        children: [
          _buildFilterDropdown(
            'TẤT CẢ',
            ['TẤT CẢ', 'TRỐNG', 'CÓ KHÁCH', 'ĐÃ ĐẶT'],
            (value) {
              List<String>? statusFilter;
              if (value != 'TẤT CẢ') {
                statusFilter = [_mapDisplayStatusToApi(value!)];
              }
              context.read<TablesBloc>().add(
                    FilterTablesEvent(
                      storeId: '623a97cdbe781e0ba814f227',
                      statusFilter: statusFilter,
                    ),
                  );
            },
          ),
          const SizedBox(width: PosSpacing.lg),
          _buildFilterDropdown(
            _selectedArea,
            ['TẤT CẢ', 'TẦNG 1', 'TẦNG 2', 'SÂN THƯỢNG'],
            (value) {
              setState(() {
                _selectedArea = value ?? 'TẤT CẢ';
              });
            },
          ),
          const Spacer(),
          Text(
            'Tổng: ${allTables.tables.length} bàn',
            style: PosTypography.bodyMedium.copyWith(
              color: PosColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTablesGrid(BuildContext context, AllTablesEntity allTables) {
    return GridView.builder(
      padding: const EdgeInsets.all(PosSpacing.lg),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        childAspectRatio: 1,
        crossAxisSpacing: PosSpacing.xs,
        mainAxisSpacing: PosSpacing.xs,
      ),
      itemCount: allTables.tables.length,
      itemBuilder: (context, index) {
        final table = allTables.tables[index];
        return _buildTableCard(context, table);
      },
    );
  }

  Widget _buildTableCard(BuildContext context, TableEntity table) {
    return GestureDetector(
      onTap: () {
        if (table.status == 'available') {
          // Navigate to menu for available table
          // posMainScreenKey.currentState?.switchToMenuTab(table.name);
        } else {
          final order = _getOrderForTable(table.tableId);
          _showTableDetails(table, order);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: _getTableColor(table.status),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: PosColors.border,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              table.name,
              style: PosTypography.bodyLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _getTableStatusText(table.status),
              style: PosTypography.bodySmall.copyWith(
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
            Text(
              '${table.capacity} chỗ',
              style: PosTypography.bodySmall.copyWith(
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterDropdown(String currentValue, List<String> items,
      ValueChanged<String?> onChanged) {
    return DropdownButtonHideUnderline(
      child: DropdownButton<String>(
        value: currentValue,
        icon: const Icon(Icons.arrow_drop_down, color: PosColors.textSecondary),
        style: PosTypography.bodyLarge.copyWith(color: PosColors.textSecondary),
        onChanged: onChanged,
        items: items.map<DropdownMenuItem<String>>((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(value),
          );
        }).toList(),
      ),
    );
  }

  void _showTableDetails(TableEntity table, Order? order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Chi tiết ${table.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Trạng thái: ${_getTableStatusText(table.status)}'),
            Text('Số chỗ ngồi: ${table.capacity}'),
            Text('Khu vực: ${table.areaName}'),
            if (table.currentCustomerName != null) ...[
              const SizedBox(height: PosSpacing.md),
              Text('Khách hàng: ${table.currentCustomerName}'),
              if (table.currentCustomerPhone != null)
                Text('SĐT: ${table.currentCustomerPhone}'),
            ],
            if (order != null) ...[
              const SizedBox(height: PosSpacing.md),
              Text('Order #${order.id}'),
              Text('Số món: ${order.items.length}'),
              Text('Tổng tiền: ${order.total.toStringAsFixed(0)}đ'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }
}
