part of 'tables_bloc.dart';

/// Base class cho tất cả Tables events
abstract class TablesEvent extends Equatable {
  const TablesEvent();

  @override
  List<Object?> get props => [];
}

/// Event để load tất cả bàn ăn
class LoadAllTablesEvent extends TablesEvent {
  final String storeId;
  final List<String>? statusFilter;

  const LoadAllTablesEvent({
    required this.storeId,
    this.statusFilter,
  });

  @override
  List<Object?> get props => [storeId, statusFilter];
}

/// Event để refresh danh sách bàn ăn
class RefreshTablesEvent extends TablesEvent {
  final String storeId;
  final List<String>? statusFilter;

  const RefreshTablesEvent({
    required this.storeId,
    this.statusFilter,
  });

  @override
  List<Object?> get props => [storeId, statusFilter];
}

/// Event để filter bàn ăn theo trạng thái
class FilterTablesEvent extends TablesEvent {
  final String storeId;
  final List<String>? statusFilter;

  const FilterTablesEvent({
    required this.storeId,
    this.statusFilter,
  });

  @override
  List<Object?> get props => [storeId, statusFilter];
}

/// Event để load thống kê bàn ăn
class LoadTableStatsEvent extends TablesEvent {
  final String storeId;

  const LoadTableStatsEvent({required this.storeId});

  @override
  List<Object?> get props => [storeId];
}

/// Event để cập nhật trạng thái bàn
class UpdateTableStatusEvent extends TablesEvent {
  final String areaId;
  final String tableId;
  final String storeId;
  final String status;
  final String? currentOrderId;
  final String? currentCustomerName;
  final String? currentCustomerPhone;
  final DateTime? reservedAt;
  final int? estimatedDuration;
  final String? note;

  const UpdateTableStatusEvent({
    required this.areaId,
    required this.tableId,
    required this.storeId,
    required this.status,
    this.currentOrderId,
    this.currentCustomerName,
    this.currentCustomerPhone,
    this.reservedAt,
    this.estimatedDuration,
    this.note,
  });

  @override
  List<Object?> get props => [
        areaId,
        tableId,
        storeId,
        status,
        currentOrderId,
        currentCustomerName,
        currentCustomerPhone,
        reservedAt,
        estimatedDuration,
        note,
      ];
}

/// Event để reset error state
class ResetTablesErrorEvent extends TablesEvent {
  const ResetTablesErrorEvent();
}
