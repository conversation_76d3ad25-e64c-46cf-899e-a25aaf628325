import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/table_entity.dart';
import '../../domain/usecases/get_all_tables_usecase.dart';
import '../../domain/usecases/get_store_table_stats_usecase.dart';
import '../../../../core/errors/failures.dart';

part 'tables_event.dart';
part 'tables_state.dart';

/// BLoC cho Tables feature
class TablesBloc extends Bloc<TablesEvent, TablesState> {
  final GetAllTablesUseCase getAllTablesUseCase;
  final GetStoreTableStatsUseCase getStoreTableStatsUseCase;

  TablesBloc({
    required this.getAllTablesUseCase,
    required this.getStoreTableStatsUseCase,
  }) : super(const TablesInitial()) {
    on<LoadAllTablesEvent>(_onLoadAllTables);
    on<RefreshTablesEvent>(_onRefreshTables);
    on<FilterTablesEvent>(_onFilterTables);
    on<LoadTableStatsEvent>(_onLoadTableStats);
    on<ResetTablesErrorEvent>(_onResetError);
  }

  /// Handler cho LoadAllTablesEvent
  Future<void> _onLoadAllTables(
    LoadAllTablesEvent event,
    Emitter<TablesState> emit,
  ) async {
    emit(const TablesLoading());

    final result = await getAllTablesUseCase(
      GetAllTablesParams(
        storeId: event.storeId,
        statusFilter: event.statusFilter,
      ),
    );

    result.fold(
      (failure) => emit(TablesError(message: _mapFailureToMessage(failure))),
      (allTables) => emit(TablesLoaded(allTables: allTables)),
    );
  }

  /// Handler cho RefreshTablesEvent
  Future<void> _onRefreshTables(
    RefreshTablesEvent event,
    Emitter<TablesState> emit,
  ) async {
    // Giữ data hiện tại và hiển thị refresh indicator
    final currentState = state;
    if (currentState is TablesLoaded) {
      emit(currentState.copyWith(isRefreshing: true));
    } else {
      emit(const TablesLoading());
    }

    final result = await getAllTablesUseCase(
      GetAllTablesParams(
        storeId: event.storeId,
        statusFilter: event.statusFilter,
      ),
    );

    result.fold(
      (failure) {
        if (currentState is TablesLoaded) {
          emit(TablesError(
            message: _mapFailureToMessage(failure),
            previousData: currentState.allTables,
          ));
        } else {
          emit(TablesError(message: _mapFailureToMessage(failure)));
        }
      },
      (allTables) => emit(TablesLoaded(allTables: allTables)),
    );
  }

  /// Handler cho FilterTablesEvent
  Future<void> _onFilterTables(
    FilterTablesEvent event,
    Emitter<TablesState> emit,
  ) async {
    // Giữ data hiện tại trong khi load filter mới
    final currentState = state;
    AllTablesEntity? currentData;
    
    if (currentState is TablesLoaded) {
      currentData = currentState.allTables;
      emit(currentState.copyWith(isRefreshing: true));
    } else {
      emit(const TablesLoading());
    }

    final result = await getAllTablesUseCase(
      GetAllTablesParams(
        storeId: event.storeId,
        statusFilter: event.statusFilter,
      ),
    );

    result.fold(
      (failure) => emit(TablesError(
        message: _mapFailureToMessage(failure),
        previousData: currentData,
      )),
      (allTables) => emit(TablesLoaded(allTables: allTables)),
    );
  }

  /// Handler cho LoadTableStatsEvent
  Future<void> _onLoadTableStats(
    LoadTableStatsEvent event,
    Emitter<TablesState> emit,
  ) async {
    final currentState = state;
    AllTablesEntity? currentTablesData;
    
    if (currentState is TablesLoaded) {
      currentTablesData = currentState.allTables;
    }

    emit(TableStatsLoading(currentTablesData: currentTablesData));

    final result = await getStoreTableStatsUseCase(
      GetStoreTableStatsParams(storeId: event.storeId),
    );

    result.fold(
      (failure) => emit(TableStatsError(
        message: _mapFailureToMessage(failure),
        currentTablesData: currentTablesData,
      )),
      (stats) => emit(TableStatsLoaded(
        stats: stats,
        currentTablesData: currentTablesData,
      )),
    );
  }

  /// Handler cho ResetTablesErrorEvent
  void _onResetError(
    ResetTablesErrorEvent event,
    Emitter<TablesState> emit,
  ) {
    final currentState = state;
    if (currentState is TablesError && currentState.previousData != null) {
      emit(TablesLoaded(allTables: currentState.previousData!));
    } else {
      emit(const TablesInitial());
    }
  }

  /// Map Failure sang error message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return failure.message;
    } else if (failure is NetworkFailure) {
      return 'Không có kết nối mạng';
    } else if (failure is UnauthorizedFailure) {
      return 'Phiên đăng nhập đã hết hạn';
    } else if (failure is NotFoundFailure) {
      return failure.message;
    } else {
      return 'Đã xảy ra lỗi không xác định';
    }
  }
}
