part of 'tables_bloc.dart';

/// Base class cho tất cả Tables states
abstract class TablesState extends Equatable {
  const TablesState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class TablesInitial extends TablesState {
  const TablesInitial();
}

/// Loading state
class TablesLoading extends TablesState {
  const TablesLoading();
}

/// State khi đã load thành công danh sách bàn ăn
class TablesLoaded extends TablesState {
  final AllTablesEntity allTables;
  final bool isRefreshing;

  const TablesLoaded({
    required this.allTables,
    this.isRefreshing = false,
  });

  @override
  List<Object?> get props => [allTables, isRefreshing];

  TablesLoaded copyWith({
    AllTablesEntity? allTables,
    bool? isRefreshing,
  }) {
    return TablesLoaded(
      allTables: allTables ?? this.allTables,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }
}

/// State khi có lỗi
class TablesError extends TablesState {
  final String message;
  final AllTablesEntity? previousData;

  const TablesError({
    required this.message,
    this.previousData,
  });

  @override
  List<Object?> get props => [message, previousData];
}

/// State khi đang cập nhật trạng thái bàn
class TableStatusUpdating extends TablesState {
  final AllTablesEntity currentData;
  final String tableId;

  const TableStatusUpdating({
    required this.currentData,
    required this.tableId,
  });

  @override
  List<Object?> get props => [currentData, tableId];
}

/// State khi cập nhật trạng thái bàn thành công
class TableStatusUpdated extends TablesState {
  final AllTablesEntity updatedData;
  final String message;

  const TableStatusUpdated({
    required this.updatedData,
    required this.message,
  });

  @override
  List<Object?> get props => [updatedData, message];
}

/// State khi cập nhật trạng thái bàn thất bại
class TableStatusUpdateError extends TablesState {
  final String message;
  final AllTablesEntity currentData;

  const TableStatusUpdateError({
    required this.message,
    required this.currentData,
  });

  @override
  List<Object?> get props => [message, currentData];
}

/// State khi đang load thống kê
class TableStatsLoading extends TablesState {
  final AllTablesEntity? currentTablesData;

  const TableStatsLoading({this.currentTablesData});

  @override
  List<Object?> get props => [currentTablesData];
}

/// State khi đã load thống kê thành công
class TableStatsLoaded extends TablesState {
  final TableStatsEntity stats;
  final AllTablesEntity? currentTablesData;

  const TableStatsLoaded({
    required this.stats,
    this.currentTablesData,
  });

  @override
  List<Object?> get props => [stats, currentTablesData];
}

/// State khi load thống kê thất bại
class TableStatsError extends TablesState {
  final String message;
  final AllTablesEntity? currentTablesData;

  const TableStatsError({
    required this.message,
    this.currentTablesData,
  });

  @override
  List<Object?> get props => [message, currentTablesData];
}
