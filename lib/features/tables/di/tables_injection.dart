import '../../../core/di/injection_container.dart' as di;
import '../data/datasources/tables_remote_data_source.dart';
import '../data/repositories/tables_repository_impl.dart';
import '../domain/repositories/tables_repository.dart';
import '../domain/usecases/get_all_tables_usecase.dart';
import '../domain/usecases/get_store_table_stats_usecase.dart';
import '../presentation/bloc/tables_bloc.dart';

/// Initialize Tables feature dependencies
void initTablesInjection() {
  // BLoC
  di.sl.registerFactory(
    () => TablesBloc(
      getAllTablesUseCase: di.sl(),
      getStoreTableStatsUseCase: di.sl(),
    ),
  );

  // Use Cases
  di.sl.registerLazySingleton(() => GetAllTablesUseCase(di.sl()));
  di.sl.registerLazySingleton(() => GetStoreTableStatsUseCase(di.sl()));

  // Repository
  di.sl.registerLazySingleton<TablesRepository>(
    () => TablesRepositoryImpl(
      remoteDataSource: di.sl(),
      networkInfo: di.sl(),
    ),
  );

  // Data Sources
  di.sl.registerLazySingleton<TablesRemoteDataSource>(
    () => TablesRemoteDataSourceImpl(apiClient: di.sl()),
  );
}
