import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../constants/app_constants.dart';
import '../../config/environment.dart';
import '../../config/proxy_config.dart';
import '../../services/logger_service.dart';
import '../../utils/error_handler.dart';

class ApiClient {
  late final Dio _dio;
  final FlutterSecureStorage _storage;
  LoggerService? _logger;
  ErrorHandler? _errorHandler;

  ApiClient(this._storage) {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiUrl, // Use environment-based URL
      connectTimeout: Duration(milliseconds: AppConstants.apiConnectTimeout),
      receiveTimeout: Duration(milliseconds: AppConstants.apiReceiveTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    _initializeInterceptors();

    // Configure proxy and SSL for development
    if (AppConfig.isDevelopment) {
      _configureProxy();
    }

    // Configure SSL certificate bypass for all environments (temporary fix for ChoHaiSan API)
    _configureSSL();
  }

  // Set logger and error handler
  void setLoggers(LoggerService logger, ErrorHandler errorHandler) {
    _logger = logger;
    _errorHandler = errorHandler;
    _initializeInterceptors(); // Reinitialize with logger
    _logger?.debug('ApiClient đã thiết lập logger và error handler');
  }

  // Initialize interceptors
  void _initializeInterceptors() {
    _dio.interceptors.clear();

    // Add custom interceptor with enhanced features
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Smart token injection - skip for auth endpoints that don't require token
          if (!options.path.contains('/auth/login') &&
              !options.path.contains('/auth/register') &&
              !options.path.contains('/auth/forgot-password') &&
              !options.path.contains('/login.html')) {
            final token = await _storage.read(key: AppConstants.authTokenKey);
            debugPrint('🔍 Token from storage: $token');
            debugPrint('🔍 Request path: ${options.path}');

            if (token != null) {
              // Sử dụng access-token header cho API LBVD và table management
             options.headers['access-token'] = token;
            } else {
              debugPrint('❌ No token found in storage');
            }
          }

          // Build full URL for logging
          final fullUrl = '${options.baseUrl}${options.path}';
          final queryString = options.queryParameters?.isNotEmpty == true
              ? '?${options.queryParameters.entries.map((e) => '${e.key}=${e.value}').join('&')}'
              : '';
          final completeUrl = '$fullUrl$queryString';

          // Log API request
          _logger?.debug(
            'API REQUEST: ${options.method} ${options.path}',
            data: {
              'fullUrl': completeUrl,
              'baseUrl': options.baseUrl,
              'path': options.path,
              'headers': options.headers,
              'query': options.queryParameters,
              'body': options.data,
            },
          );

          debugPrint('🌐 REQUEST[${options.method}] => FULL URL: $completeUrl');
          debugPrint('📍 REQUEST[${options.method}] => PATH: ${options.path}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          // Build full URL for response logging
          final responseFullUrl =
              '${response.requestOptions.baseUrl}${response.requestOptions.path}';
          final responseQueryString = response
                      .requestOptions.queryParameters?.isNotEmpty ==
                  true
              ? '?${response.requestOptions.queryParameters.entries.map((e) => '${e.key}=${e.value}').join('&')}'
              : '';
          final responseCompleteUrl = '$responseFullUrl$responseQueryString';

          // Log API response
          _logger?.debug(
            'API RESPONSE: ${response.statusCode} ${response.requestOptions.path}',
            data: {
              'fullUrl': responseCompleteUrl,
              'status': response.statusCode,
              'headers': response.headers.map,
            },
          );

          handler.next(response);
        },
        onError: (error, handler) async {
          // Build full URL for error logging
          final errorFullUrl =
              '${error.requestOptions.baseUrl}${error.requestOptions.path}';
          final errorQueryString = error
                      .requestOptions.queryParameters?.isNotEmpty ==
                  true
              ? '?${error.requestOptions.queryParameters.entries.map((e) => '${e.key}=${e.value}').join('&')}'
              : '';
          final errorCompleteUrl = '$errorFullUrl$errorQueryString';

          // Log API error
          _logger?.error(
            'API ERROR: ${error.response?.statusCode} ${error.requestOptions.path}',
            data: {
              'fullUrl': errorCompleteUrl,
              'status': error.response?.statusCode,
              'message': error.message,
              'type': error.type.toString(),
            },
          );

          // Use ErrorHandler to handle API errors
          _errorHandler?.handleApiError(error.requestOptions.path, error,
              stackTrace: error.stackTrace);

          debugPrint(
              '❌ ERROR[${error.response?.statusCode}] => FULL URL: $errorCompleteUrl');
          debugPrint(
              '📍 ERROR[${error.response?.statusCode}] => PATH: ${error.requestOptions.path}');

          // Handle 401 errors
          if (error.response?.statusCode == 401) {
            debugPrint('🚨 401 Error detected - deleting token');
            debugPrint('🚨 Request path: ${error.requestOptions.path}');
            await _storage.delete(key: AppConstants.authTokenKey);
            debugPrint('🚨 Token deleted due to 401 error');
          }

          handler.next(error);
        },
      ),
    );

    // Add development logging
    if (AppConfig.isDevelopment) {
      _dio.interceptors.add(LogInterceptor(
        request: true,
        requestBody: true,
        responseBody: false, // Reduce log spam
        responseHeader: false,
        requestHeader: true,
        error: true,
      ));
    }
  }

  // Configure proxy for Proxyman
  void _configureProxy() {
    if (AppConfig.isDevelopment && ProxyConfig.enabled) {
      (_dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
          (client) {
        client.findProxy = (uri) => 'PROXY ${ProxyConfig.proxyUrl}';
        client.badCertificateCallback = (cert, host, port) => true;
        return client;
      };
      debugPrint('✓ Proxyman đã được kích hoạt: ${ProxyConfig.proxyUrl}');
    }
  }

  // Configure SSL certificate bypass for ChoHaiSan API
  void _configureSSL() {
    (_dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
        (client) {
      // Bypass SSL certificate verification for ChoHaiSan API
      client.badCertificateCallback = (cert, host, port) {
        // Only bypass for ChoHaiSan API domain
        if (host.contains('langnuoibienvandon.com')) {
          debugPrint('🔓 SSL bypass for ChoHaiSan API: $host');
          return true;
        }
        return false; // Use normal SSL verification for other domains
      };
      return client;
    };
    debugPrint('✓ SSL configuration đã được thiết lập cho ChoHaiSan API');
  }

  Dio get dio => _dio;

  // GET request
  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.get(path,
        queryParameters: queryParameters, options: options);
  }

  // POST request
  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.post(path,
        data: data, queryParameters: queryParameters, options: options);
  }

  // PUT request
  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.put(path,
        data: data, queryParameters: queryParameters, options: options);
  }

  // DELETE request
  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.delete(path,
        data: data, queryParameters: queryParameters, options: options);
  }

  // ===================== HELPER METHODS =====================

  /// Save authentication token
  Future<void> saveToken(String token) async {
    debugPrint('🔍 ApiClient.saveToken() called with token: ${token.substring(0, 50)}...');
    debugPrint('🔍 Using key: ${AppConstants.authTokenKey}');

    try {
      await _storage.write(key: AppConstants.authTokenKey, value: token);
      debugPrint('✅ Token written to storage successfully');

      // Verify immediately
      final readBack = await _storage.read(key: AppConstants.authTokenKey);
      debugPrint('🔍 Read back token: ${readBack?.substring(0, 50)}...');
      debugPrint('🔍 Save successful: ${readBack == token}');
    } catch (e) {
      debugPrint('❌ Error saving token: $e');
      rethrow;
    }
  }

  /// Get authentication token
  Future<String?> getToken() async {
    return await _storage.read(key: AppConstants.authTokenKey);
  }

  /// Delete authentication token
  Future<void> deleteToken() async {
    debugPrint('🗑️ ApiClient.deleteToken() called');
    debugPrint('🗑️ Stack trace: ${StackTrace.current}');
    await _storage.delete(key: AppConstants.authTokenKey);
    debugPrint('🗑️ Token deleted from storage');
  }

  /// Update proxy configuration
  void updateProxyConfig() {
    _configureProxy();
  }

  // ===================== TABLE MANAGEMENT API =====================

  /// Lấy danh sách tất cả bàn ăn từ tất cả khu vực
  Future<Response> getAllTables({
    required String storeId,
    List<String>? statusFilter,
  }) async {
    final queryParams = <String, dynamic>{
      'storeId': storeId,
    };

    if (statusFilter != null && statusFilter.isNotEmpty) {
      queryParams['status'] = statusFilter.join(',');
    }

    return await get(
      '/user/api/all-tables',
      queryParameters: queryParams,
    );
  }

  /// Lấy thống kê tổng quan bàn ăn của cửa hàng
  Future<Response> getStoreTableStats({
    required String storeId,
  }) async {
    return await get(
      '/user/api/store-table-stats',
      queryParameters: {'storeId': storeId},
    );
  }

  /// Lấy danh sách khu vực bàn ăn
  Future<Response> getTableAreas({
    required String storeId,
  }) async {
    return await get(
      '/user/api/table-areas',
      queryParameters: {'storeId': storeId},
    );
  }

  /// Cập nhật trạng thái bàn
  Future<Response> updateTableStatus({
    required String areaId,
    required String tableId,
    required String storeId,
    required String status,
    String? currentOrderId,
    String? currentCustomerName,
    String? currentCustomerPhone,
    int? reservedAt,
    int? estimatedDuration,
    String? note,
  }) async {
    final data = <String, dynamic>{
      'storeId': storeId,
      'status': status,
    };

    if (currentOrderId != null) data['currentOrderId'] = currentOrderId;
    if (currentCustomerName != null) data['currentCustomerName'] = currentCustomerName;
    if (currentCustomerPhone != null) data['currentCustomerPhone'] = currentCustomerPhone;
    if (reservedAt != null) data['reservedAt'] = reservedAt;
    if (estimatedDuration != null) data['estimatedDuration'] = estimatedDuration;
    if (note != null) data['note'] = note;

    return await post(
      '/user/api/update-table-status/$areaId/$tableId',
      data: data,
    );
  }
}
