import 'package:flutter/material.dart';
import '../theme/pos_colors.dart';
import '../theme/pos_typography.dart';
import '../theme/pos_spacing.dart';

/// Custom POS Tab Bar with specific layout according to <PERSON><PERSON>lin design
/// Layout: <PERSON><PERSON><PERSON> - <PERSON><PERSON> - <PERSON>ịch hẹn - Ho<PERSON> đơn - Bar/Bếp - Vé [Spacer] Thoát
/// Note: Payment tab is hidden and placed at the end of TabBarView (index 7)
class CustomPosTabBar extends StatelessWidget implements PreferredSizeWidget {
  final TabController tabController;
  final List<Tab> tabs;
  final Function(int) onTap;

  const CustomPosTabBar({
    super.key,
    required this.tabController,
    required this.tabs,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 75,
      color: PosColors.primary,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: PosSpacing.md),
        child: Row(
          children: [
            // Bàn
            _buildTabItem(0, tabs[0]),
            const SizedBox(width: 40),

            // Menu
            _buildTabItem(1, tabs[1]),
            const SizedBox(width: 40),

            // Lịch hẹn
            _buildTabItem(2, tabs[2]),
            const SizedBox(width: 40),

            // Hoá đơn
            _buildTabItem(3, tabs[3]),
            const SizedBox(width: 40),

            // Bar/Bếp
            _buildTabItem(4, tabs[4]),
            const SizedBox(width: 40),

            // Vé
            _buildTabItem(5, tabs[5]),

            // Spacer để đẩy Thoát về bên phải
            const Spacer(),

            // Thoát
            _buildTabItem(6, tabs[6]),
          ],
        ),
      ),
    );
  }

  Widget _buildTabItem(int index, Tab tab) {
    // Simple 1:1 mapping since Payment tab is now at the end (index 7)
    // No complex index adjustment needed
    final isSelected = tabController.index == index;
    final isExitTab = index == 6; // Thoát tab

    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 12,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            IconTheme(
              data: IconThemeData(
                size: 24,
                color: isSelected && !isExitTab
                    ? PosColors.textOnDark
                    : PosColors.textOnDark.withValues(alpha: 0.7),
              ),
              child: tab.icon ?? const Icon(Icons.help),
            ),
            const SizedBox(height: 4),
            // Text
            Text(
              tab.text ?? '',
              style: PosTypography.bodySmall.copyWith(
                color: isSelected && !isExitTab
                    ? PosColors.textOnDark
                    : PosColors.textOnDark.withValues(alpha: 0.7),
                fontWeight: isSelected && !isExitTab
                    ? FontWeight.w600
                    : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(75);
}
