import 'package:get_it/get_it.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/environment.dart';
import '../network/network_info.dart';
import '../network/api_client.dart';
import '../../services/logger_service.dart';
import '../../services/logout_service.dart';
import '../../services/api_service.dart';
import '../../utils/error_handler.dart';

// Auth feature
import '../../features/auth/data/datasources/auth_remote_data_source.dart';
import '../../features/auth/data/datasources/auth_local_data_source.dart';
import '../../features/auth/data/repositories/auth_repository_impl.dart';
import '../../features/auth/domain/repositories/auth_repository.dart';
import '../../features/auth/domain/usecases/login_usecase.dart';
import '../../features/auth/domain/usecases/logout_usecase.dart';
import '../../features/auth/domain/usecases/register_usecase.dart';
import '../../features/auth/domain/usecases/get_current_user_usecase.dart';
import '../../features/auth/domain/usecases/forgot_password_usecase.dart';
import '../../features/auth/domain/usecases/change_password_usecase.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../features/auth/presentation/bloc/change_password/change_password_bloc.dart';

// Home feature
import '../../features/home/<USER>/datasources/home_remote_data_source.dart';
import '../../features/home/<USER>/datasources/home_local_data_source.dart';
import '../../features/home/<USER>/repositories/home_repository_impl.dart';
import '../../features/home/<USER>/repositories/home_repository.dart';
import '../../features/home/<USER>/usecases/get_banners_usecase.dart';
import '../../features/home/<USER>/usecases/get_featured_properties_usecase.dart';
import '../../features/home/<USER>/usecases/get_recent_properties_usecase.dart';
import '../../features/home/<USER>/usecases/get_vip_properties_usecase.dart';
import '../../features/home/<USER>/usecases/get_featured_users_usecase.dart';
import '../../features/home/<USER>/usecases/get_property_owners_usecase.dart';
import '../../features/home/<USER>/usecases/get_service_providers_usecase.dart';
import '../../features/home/<USER>/bloc/home_bloc.dart';

// Wallet feature (New)
import '../../features/wallet/di/wallet_injection.dart';
// Search Feature
import '../../features/search/data/datasources/search_remote_data_source.dart';
import '../../features/search/data/repositories/search_repository_impl.dart';
import '../../features/search/domain/repositories/search_repository.dart';
import '../../features/search/domain/usecases/get_categories_usecase.dart';
import '../../features/search/domain/usecases/get_service_categories_usecase.dart';
import '../../features/search/domain/usecases/search_properties_usecase.dart';
import '../../features/search/domain/usecases/search_services_usecase.dart';
import '../../features/search/presentation/bloc/search_bloc.dart';

// Map feature
import '../../features/map/data/datasources/map_remote_data_source.dart';
import '../../features/map/data/datasources/map_local_data_source.dart';
import '../../features/map/data/repositories/map_repository_impl.dart';
import '../../features/map/domain/repositories/map_repository.dart';
import '../../features/map/domain/usecases/get_property_markers_usecase.dart';
import '../../features/map/domain/usecases/get_user_location_usecase.dart';
import '../../features/map/domain/usecases/search_location_usecase.dart';
import '../../features/map/domain/usecases/get_property_details_usecase.dart';
import '../../features/map/presentation/bloc/map_bloc.dart';

// Forum feature
import '../../features/forum/data/datasources/forum_remote_data_source.dart';
import '../../features/forum/data/repositories/forum_repository_impl.dart';
import '../../features/forum/domain/repositories/forum_repository.dart';
import '../../features/forum/domain/usecases/get_forum_posts_usecase.dart';
import '../../features/forum/domain/usecases/get_forum_post_usecase.dart';
import '../../features/forum/domain/usecases/create_forum_post_usecase.dart';
import '../../features/forum/domain/usecases/like_forum_post_usecase.dart';
import '../../features/forum/domain/usecases/upload_media_usecase.dart';
import '../../features/forum/domain/usecases/add_forum_comment_usecase.dart';
import '../../features/forum/domain/usecases/get_forum_comments_usecase.dart';
import '../../features/forum/presentation/bloc/home_forum_bloc.dart';
import '../../features/forum/presentation/bloc/forum_posts/forum_posts_bloc.dart';
import '../../features/forum/presentation/bloc/forum_create/forum_create_bloc.dart';
import '../../features/forum/presentation/bloc/forum_post_detail/forum_post_detail_bloc.dart';

// Campaign feature
import '../../features/campaign/data/datasources/campaign_remote_data_source.dart';
import '../../features/campaign/data/repositories/campaign_repository_impl.dart';
import '../../features/campaign/domain/repositories/campaign_repository.dart';
import '../../features/campaign/domain/usecases/get_home_campaigns_usecase.dart';
import '../../features/campaign/domain/usecases/get_campaign_list_usecase.dart';
import '../../features/campaign/domain/usecases/load_more_campaigns_usecase.dart';
import '../../features/campaign/domain/usecases/refresh_campaign_list_usecase.dart';
import '../../features/campaign/domain/usecases/toggle_campaign_like_usecase.dart';
import '../../features/campaign/domain/usecases/join_campaign_usecase.dart';
import '../../features/campaign/domain/usecases/leave_campaign_usecase.dart';
import '../../features/campaign/domain/usecases/get_campaign_detail_usecase.dart';
import '../../features/campaign/domain/usecases/get_campaign_comments_usecase.dart';
import '../../features/campaign/domain/usecases/add_campaign_comment_usecase.dart';
import '../../features/campaign/presentation/bloc/home_campaigns_bloc.dart';
import '../../features/campaign/presentation/bloc/campaign_list_bloc.dart';
import '../../features/campaign/presentation/bloc/campaign_detail_bloc.dart';

// Service feature
import '../../features/service/data/datasources/service_remote_data_source.dart';
import '../../features/service/data/repositories/service_repository_impl.dart';
import '../../features/service/data/repositories/service_listing_repository_impl.dart';
import '../../features/service/data/repositories/service_detail_repository_impl.dart';
import '../../features/service/domain/repositories/service_repository.dart';
import '../../features/service/domain/repositories/service_listing_repository.dart';
import '../../features/service/domain/repositories/service_detail_repository.dart';
import '../../features/service/domain/usecases/get_home_services_usecase.dart';
import '../../features/service/domain/usecases/book_service_usecase.dart';
import '../../features/service/domain/usecases/get_service_list_usecase.dart';
import '../../features/service/domain/usecases/load_more_services_usecase.dart';
import '../../features/service/domain/usecases/like_service_usecase.dart';
import '../../features/service/domain/usecases/get_service_detail.dart';
import '../../features/service/domain/usecases/get_service_comments.dart';
import '../../features/service/domain/usecases/add_service_comment.dart';
import '../../features/service/domain/usecases/reply_service_comment.dart';
import '../../features/service/presentation/bloc/home_services_bloc.dart';
import '../../features/service/presentation/bloc/service_listing_bloc.dart';
import '../../features/service/presentation/bloc/service_detail/service_detail_bloc.dart';

// Property feature
import '../../features/property/data/datasources/property_remote_data_source.dart';
import '../../features/property/data/repositories/property_repository_impl.dart';
import '../../features/property/domain/repositories/property_repository.dart';
import '../../features/property/domain/usecases/get_home_properties_usecase.dart';
import '../../features/property/domain/usecases/toggle_favorite_property_usecase.dart';
import '../../features/property/presentation/bloc/home_properties_bloc.dart';
import '../../features/property/presentation/bloc/property_list_bloc.dart';
import '../../features/property/domain/usecases/get_property_types_usecase.dart';
import '../../features/property/domain/usecases/get_property_list_usecase.dart';
import '../../features/property/domain/usecases/search_properties_usecase.dart'
    as property_search;
import '../../features/property/domain/usecases/load_more_properties_usecase.dart';
import '../../features/property/domain/usecases/refresh_property_list_usecase.dart';
import '../../features/property/domain/usecases/get_favorite_properties_usecase.dart';

// Property Detail feature
import '../../features/property/data/datasources/property_detail_remote_data_source.dart';
import '../../features/property/data/datasources/property_detail_remote_data_source_impl.dart';
import '../../features/property/data/repositories/property_detail_repository_impl.dart';
import '../../features/property/data/repositories/property_comment_repository_impl.dart';
import '../../features/property/domain/repositories/property_detail_repository.dart';
import '../../features/property/domain/repositories/property_comment_repository.dart';
import '../../features/property/domain/usecases/get_property_detail.dart';
import '../../features/property/domain/usecases/toggle_property_like.dart';
import '../../features/property/domain/usecases/get_related_properties.dart';
import '../../features/property/domain/usecases/get_property_comments.dart';
import '../../features/property/domain/usecases/add_property_comment.dart';
import '../../features/property/domain/usecases/reply_to_comment.dart';
import '../../features/property/presentation/bloc/property_detail_bloc.dart';
import '../../features/property/presentation/bloc/property_comment_bloc.dart';
import 'package:http/http.dart' as http;

// User Detail feature
import '../../features/user_detail/data/datasources/user_detail_remote_data_source.dart';
import '../../features/user_detail/data/datasources/user_detail_cache_data_source.dart';
import '../../features/user_detail/data/repositories/user_detail_repository_impl.dart';
import '../../features/user_detail/domain/repositories/user_detail_repository.dart';
import '../../features/user_detail/domain/usecases/get_user_detail_usecase.dart';
import '../../features/user_detail/presentation/bloc/user_detail_bloc.dart';

// House Detail feature
import '../../features/house_detail/data/datasources/house_detail_remote_data_source.dart';
import '../../features/house_detail/data/datasources/house_detail_cache_data_source.dart';
import '../../features/house_detail/data/repositories/house_detail_repository_impl.dart';
import '../../features/house_detail/domain/repositories/house_detail_repository.dart';
import '../../features/house_detail/domain/usecases/get_house_detail_usecase.dart';
import '../../features/house_detail/presentation/bloc/house_detail_bloc.dart';

// User Type Listing feature
import '../../features/user_type_listing/data/datasources/user_type_remote_data_source.dart';
import '../../features/user_type_listing/data/datasources/user_type_remote_data_source_impl.dart';
import '../../features/user_type_listing/data/repositories/user_type_repository_impl.dart';
import '../../features/user_type_listing/domain/repositories/user_type_repository.dart';
import '../../features/user_type_listing/domain/usecases/get_users_by_type.dart';
import '../../features/user_type_listing/domain/usecases/search_users_by_type.dart';
import '../../features/user_type_listing/presentation/bloc/user_type_listing_bloc.dart';

// House Management Feature
import '../../features/house_management/di/house_management_injection_container.dart';

// Add import for AddHouse feature
import '../../features/add_house/di/add_house_injection.dart';

// Create Post Feature
import '../../features/create_post/presentation/bloc/create_post_bloc.dart';
import '../../features/create_post/domain/usecases/create_post_usecase.dart';
import '../../features/create_post/domain/usecases/get_property_types_usecase.dart';
import '../../features/create_post/domain/usecases/upload_images_usecase.dart';
import '../../features/create_post/domain/usecases/upload_video_usecase.dart';
import '../../features/create_post/domain/repositories/create_post_repository.dart';
import '../../features/create_post/data/repositories/create_post_repository_impl.dart';
import '../../features/create_post/data/datasources/create_post_remote_data_source.dart';
import '../../features/create_post/data/datasources/create_post_local_data_source.dart';

// Revenue Management Feature
import '../../features/revenue_management/presentation/bloc/revenue_summary/revenue_summary_bloc.dart';
import '../../features/revenue_management/presentation/bloc/revenue_detail/revenue_detail_bloc.dart';
import '../../features/revenue_management/presentation/bloc/next_month_plan/next_month_plan_bloc.dart';
import '../../features/revenue_management/presentation/bloc/add_revenue/add_revenue_bloc.dart';
import '../../features/revenue_management/domain/usecases/get_revenue_summary_usecase.dart';
import '../../features/revenue_management/domain/usecases/get_revenue_detail_usecase.dart';
import '../../features/revenue_management/domain/usecases/get_next_month_plan_usecase.dart';
import '../../features/revenue_management/domain/usecases/add_revenue_transaction_usecase.dart';
import '../../features/revenue_management/domain/usecases/delete_revenue_transaction_usecase.dart';
import '../../features/revenue_management/domain/usecases/export_revenue_excel_usecase.dart';
import '../../features/revenue_management/domain/repositories/revenue_repository.dart';
import '../../features/revenue_management/data/repositories/revenue_repository_impl.dart';
import '../../features/revenue_management/data/datasources/revenue_remote_data_source.dart';
import '../../features/revenue_management/data/datasources/revenue_cache_data_source.dart';

// Customer Management feature
import '../../features/customer_management/di/customer_management_injection.dart';

// Cho Hai San feature
import '../../features/cho_hai_san/data/datasources/cho_hai_san_remote_datasource.dart';
import '../../features/cho_hai_san/data/repositories/cho_hai_san_repository_impl.dart';
import '../../features/cho_hai_san/domain/repositories/cho_hai_san_repository.dart';
import '../../features/cho_hai_san/domain/usecases/get_categories_usecase.dart'
    as cho_hai_san_categories;
import '../../features/cho_hai_san/domain/usecases/get_products_usecase.dart';
import '../../features/cho_hai_san/domain/usecases/get_product_detail_usecase.dart';
import '../../features/cho_hai_san/presentation/bloc/cho_hai_san_bloc.dart';

// Do Uong feature
import '../../features/do_uong/di/do_uong_injection.dart';

// Ticket feature
import '../../features/ticket/di/ticket_injection.dart';

// Invoice feature
import '../../features/invoice/data/datasources/invoice_remote_data_source.dart';
import '../../features/invoice/data/repositories/invoice_repository_impl.dart';
import '../../features/invoice/domain/repositories/invoice_repository.dart';
import '../../features/invoice/domain/usecases/get_invoices_usecase.dart';
import '../../features/invoice/domain/usecases/get_invoice_detail_usecase.dart';
import '../../features/invoice/domain/usecases/update_invoice_status_usecase.dart';
import '../../features/invoice/presentation/bloc/invoice_bloc.dart';

// Bar/Kitchen feature
import '../../features/bar_kitchen/di/bar_kitchen_injection.dart';

// Menu feature
import '../../features/menu/di/menu_di.dart';

// Tables feature
import '../../features/tables/di/tables_injection.dart';

// Orders feature
import '../../features/orders/di/orders_injection.dart';

// Reservations feature
import '../../features/reservations/injection_container.dart'
    as reservations_di;

final sl = GetIt.instance;

Future<void> init() async {
  // Reset GetIt instance to avoid duplicate registration errors
  if (sl.isRegistered<AuthBloc>()) {
    await sl.reset();
  }

  // Features
  _initAuth();
  _initHome();
  _initSearchFeature();
  _initMap();
  _initForum();
  _initCampaign();
  _initService();
  _initProperty();
  _initPropertyDetail();
  _initUserDetail();
  _initUserTypeListing();
  await initAddHouseInjection(); // Register AddHouse dependencies

  // Core
  _initCore();

  // External
  await _initExternal();

  // Features - Wallet (New)
  await initWalletInjection();

  // House Management Feature
  initHouseManagementDependencies();

  // House Detail Feature
  _initHouseDetail();

  // Create Post Feature
  _initCreatePost();

  // Revenue Management Feature
  _initRevenueManagement();

  // Customer Management Feature
  _initCustomerManagement();

  // Cho Hai San Feature
  _initChoHaiSan();

  // Do Uong Feature
  _initDoUong();

  // Ticket Feature
  _initTicket();

  // Invoice Feature
  _initInvoice();

  // Bar/Kitchen Feature
  _initBarKitchen();

  // Menu Feature
  initMenuFeature();

  // Tables Feature
  initTablesInjection();

  // Orders Feature
  initOrdersInjection();

  // Reservations Feature
  await reservations_di.initReservationsDependencies();
}

void _initAuth() {
  // BLoC
  sl.registerFactory(() => AuthBloc(
        loginUseCase: sl(),
        logoutUseCase: sl(),
        getCurrentUserUseCase: sl(),
        registerUseCase: sl(),
        forgotPasswordUseCase: sl(),
      ));

  // Change Password BLoC
  sl.registerFactory(() => ChangePasswordBloc(
        changePasswordUseCase: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => GetCurrentUserUseCase(sl()));
  sl.registerLazySingleton(() => RegisterUseCase(sl()));
  sl.registerLazySingleton(() => ForgotPasswordUseCase(sl()));
  sl.registerLazySingleton(() => ChangePasswordUseCase(sl()));

  // Repository
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(apiClient: sl<ApiClient>()),
  );

  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(storage: sl()),
  );
}

void _initHome() {
  // BLoC
  sl.registerFactory(() => HomeBloc(
        getBannersUseCase: sl(),
        getFeaturedPropertiesUseCase: sl(),
        getRecentPropertiesUseCase: sl(),
        getVipPropertiesUseCase: sl(),
        getFeaturedUsersUseCase: sl(),
        getPropertyOwnersUseCase: sl(),
        getServiceProvidersUseCase: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => GetBannersUseCase(sl()));
  sl.registerLazySingleton(() => GetFeaturedPropertiesUseCase(sl()));
  sl.registerLazySingleton(() => GetRecentPropertiesUseCase(sl()));
  sl.registerLazySingleton(() => GetVipPropertiesUseCase(sl()));
  sl.registerLazySingleton(() => GetFeaturedUsersUsecase(repository: sl()));
  sl.registerLazySingleton(() => GetPropertyOwnersUseCase(repository: sl()));
  sl.registerLazySingleton(() => GetServiceProvidersUseCase(repository: sl()));

  // Repository
  sl.registerLazySingleton<HomeRepository>(
    () => HomeRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<HomeRemoteDataSource>(
    () => HomeRemoteDataSourceImpl(apiClient: sl<ApiClient>()),
  );
  sl.registerLazySingleton<HomeLocalDataSource>(
    () => HomeLocalDataSourceImpl(storage: sl()),
  );
}

void _initSearchFeature() {
  // BLoC
  sl.registerFactory(
    () => SearchBloc(
      searchPropertiesUseCase: sl(),
      searchServicesUseCase: sl(),
      getCategoriesUseCase: sl(),
      getServiceCategoriesUseCase: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => SearchPropertiesUseCase(sl()));
  sl.registerLazySingleton(() => SearchServicesUseCase(sl()));
  sl.registerLazySingleton(() => GetCategoriesUseCase(sl()));
  sl.registerLazySingleton(() => GetServiceCategoriesUseCase(sl()));

  // Repository
  sl.registerLazySingleton<SearchRepository>(
    () => SearchRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<SearchRemoteDataSource>(
    () => SearchRemoteDataSourceImpl(apiClient: sl<ApiClient>()),
  );
}

void _initMap() {
  // BLoC
  sl.registerFactory(() => MapBloc(
        getPropertyMarkersUseCase: sl(),
        getUserLocationUseCase: sl(),
        searchLocationUseCase: sl(),
        getPropertyDetailsUseCase: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => GetPropertyMarkersUseCase(sl()));
  sl.registerLazySingleton(() => GetUserLocationUseCase(sl()));
  sl.registerLazySingleton(() => SearchLocationUseCase(sl()));
  sl.registerLazySingleton(() => GetPropertyDetailsUseCase(sl()));

  // Repository
  sl.registerLazySingleton<MapRepository>(
    () => MapRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<MapRemoteDataSource>(
    () => MapRemoteDataSourceImpl(apiClient: sl<ApiClient>()),
  );

  sl.registerLazySingleton<MapLocalDataSource>(
    () => MapLocalDataSourceImpl(storage: sl()),
  );
}

void _initForum() {
  // BLoC
  sl.registerFactory(() => HomeForumBloc(
        getForumPostsUsecase: sl(),
        toggleLikeForumPostUsecase: sl(),
      ));

  sl.registerFactory(() => ForumPostsBloc(
        getForumPostsUsecase: sl(),
        toggleLikeForumPostUsecase: sl(),
      ));

  sl.registerFactory(() => ForumCreateBloc(
        createForumPostUsecase: sl(),
        uploadMediaUsecase: sl(),
      ));

  sl.registerFactory(() => ForumPostDetailBloc(
        getForumPostDetailUsecase: sl(),
        getForumCommentsUsecase: sl(),
        addForumCommentUsecase: sl(),
        toggleLikeForumPostUsecase: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => GetForumPostsUsecase(repository: sl()));
  sl.registerLazySingleton(() => GetForumPostUsecase(repository: sl()));
  sl.registerLazySingleton(() => CreateForumPostUsecase(repository: sl()));
  sl.registerLazySingleton(() => ToggleLikeForumPostUsecase(repository: sl()));
  sl.registerLazySingleton(() => UploadMediaUsecase(repository: sl()));
  sl.registerLazySingleton(() => AddForumCommentUsecase(repository: sl()));
  sl.registerLazySingleton(() => GetForumCommentsUsecase(repository: sl()));

  // Repository
  sl.registerLazySingleton<ForumRepository>(
    () => ForumRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<ForumRemoteDataSource>(
    () => ForumRemoteDataSourceImpl(apiClient: sl<ApiClient>()),
  );
}

void _initCampaign() {
  // BLoC
  sl.registerFactory(() => HomeCampaignsBloc(
        getHomeCampaignsUsecase: sl(),
        joinCampaignUsecase: sl(),
        leaveCampaignUsecase: sl(),
      ));

  sl.registerFactory(() => CampaignListBloc(
        getCampaignListUseCase: sl(),
        loadMoreCampaignsUseCase: sl(),
        refreshCampaignListUseCase: sl(),
        toggleCampaignLikeUseCase: sl(),
      ));

  sl.registerFactory(() => CampaignDetailBloc(
        getCampaignDetailUseCase: sl(),
        getCampaignCommentsUseCase: sl(),
        addCampaignCommentUseCase: sl(),
        toggleCampaignLikeUseCase: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => GetHomeCampaignsUsecase(repository: sl()));
  sl.registerLazySingleton(() => GetCampaignListUseCase(sl()));
  sl.registerLazySingleton(() => LoadMoreCampaignsUseCase(sl()));
  sl.registerLazySingleton(() => RefreshCampaignListUseCase(sl()));
  sl.registerLazySingleton(() => ToggleCampaignLikeUseCase(sl()));
  sl.registerLazySingleton(() => JoinCampaignUsecase(repository: sl()));
  sl.registerLazySingleton(() => LeaveCampaignUsecase(repository: sl()));
  sl.registerLazySingleton(() => GetCampaignDetailUseCase(sl()));
  sl.registerLazySingleton(() => GetCampaignCommentsUseCase(sl()));
  sl.registerLazySingleton(() => AddCampaignCommentUseCase(sl()));

  // Repository
  sl.registerLazySingleton<CampaignRepository>(
    () => CampaignRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<CampaignRemoteDataSource>(
    () => CampaignRemoteDataSourceImpl(apiClient: sl<ApiClient>()),
  );
}

void _initService() {
  // BLoC
  sl.registerFactory(() => HomeServicesBloc(
        getHomeServicesUsecase: sl(),
        bookServiceUsecase: sl(),
      ));

  sl.registerFactory(() => ServiceListingBloc(
        getServiceListUseCase: sl(),
        loadMoreServicesUseCase: sl(),
        likeServiceUseCase: sl(),
      ));

  sl.registerFactory(() => ServiceDetailBloc(
        getServiceDetail: sl(),
        getServiceComments: sl(),
        addServiceComment: sl(),
        replyServiceComment: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => GetHomeServicesUsecase(repository: sl()));
  sl.registerLazySingleton(() => BookServiceUsecase(repository: sl()));
  sl.registerLazySingleton(() => GetServiceListUseCase(sl()));
  sl.registerLazySingleton(() => LoadMoreServicesUseCase(sl()));
  sl.registerLazySingleton(() => LikeServiceUseCase(sl()));

  // Service Detail Use cases
  sl.registerLazySingleton(() => GetServiceDetail(repository: sl()));
  sl.registerLazySingleton(() => GetServiceComments(repository: sl()));
  sl.registerLazySingleton(() => AddServiceComment(repository: sl()));
  sl.registerLazySingleton(() => ReplyServiceComment(repository: sl()));

  // Repository
  sl.registerLazySingleton<ServiceRepository>(
    () => ServiceRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  sl.registerLazySingleton<ServiceListingRepository>(
    () => ServiceListingRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  sl.registerLazySingleton<ServiceDetailRepository>(
    () => ServiceDetailRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<ServiceRemoteDataSource>(
    () => ServiceRemoteDataSourceImpl(apiClient: sl<ApiClient>()),
  );
}

void _initProperty() {
  // BLoC
  sl.registerFactory(() => HomePropertiesBloc(
        getHomePropertiesUsecase: sl(),
        toggleFavoritePropertyUsecase: sl(),
      ));

  sl.registerFactory(() => PropertyListBloc(
        sl(), // GetPropertyListUseCase
        sl(), // SearchPropertiesUseCase
        sl(), // LoadMorePropertiesUseCase
        sl(), // RefreshPropertyListUseCase
        sl(), // GetFavoritePropertiesUseCase
      ));

  // Use cases
  sl.registerLazySingleton(() => GetHomePropertiesUsecase(repository: sl()));
  sl.registerLazySingleton(
      () => ToggleFavoritePropertyUsecase(repository: sl()));
  sl.registerLazySingleton(() => GetPropertyTypesUsecase(repository: sl()));

  // Property List Use Cases
  sl.registerLazySingleton(() => GetPropertyListUseCase(sl()));
  sl.registerLazySingleton(() => property_search.SearchPropertiesUseCase(sl()));
  sl.registerLazySingleton(() => LoadMorePropertiesUseCase(sl()));
  sl.registerLazySingleton(() => RefreshPropertyListUseCase(sl()));
  sl.registerLazySingleton(() => GetFavoritePropertiesUseCase(sl()));

  // Repository
  sl.registerLazySingleton<PropertyRepository>(
    () => PropertyRepositoryImpl(sl()),
  );

  // Data sources
  sl.registerLazySingleton<PropertyRemoteDataSource>(
    () => PropertyRemoteDataSourceImpl(sl<ApiClient>()),
  );
}

void _initUserDetail() {
  // BLoC
  sl.registerFactory(() => UserDetailBloc(
        getUserDetailUsecase: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => GetUserDetailUsecase(repository: sl()));

  // Repository
  sl.registerLazySingleton<UserDetailRepository>(
    () => UserDetailRepositoryImpl(
      sl(),
      sl(),
      sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<UserDetailRemoteDataSource>(
    () => UserDetailRemoteDataSourceImpl(sl<ApiClient>()),
  );

  sl.registerLazySingleton<UserDetailCacheDataSource>(
    () => UserDetailCacheDataSourceImpl(sl()),
  );
}

void _initUserTypeListing() {
  // BLoC
  sl.registerFactory(() => UserTypeListingBloc(
        getUsersByType: sl(),
        searchUsersByType: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => GetUsersByType(sl()));
  sl.registerLazySingleton(() => SearchUsersByType(sl()));

  // Repository
  sl.registerLazySingleton<UserTypeRepository>(
    () => UserTypeRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<UserTypeRemoteDataSource>(
    () => UserTypeRemoteDataSourceImpl(apiClient: sl()),
  );
}

void _initCore() {
  // Network
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(sl()));
  sl.registerLazySingleton(() => InternetConnectionChecker());

  // Dio
  sl.registerLazySingleton(() {
    final dio = Dio(
      BaseOptions(
        baseUrl: AppConfig.apiUrl,
        connectTimeout: const Duration(seconds: 60),
        receiveTimeout: const Duration(seconds: 60),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      ),
    );
    // Log API calls
    if (AppConfig.isDevelopment) {
      dio.interceptors.add(LogInterceptor(
        request: true,
        requestHeader: true,
        requestBody: true,
        responseHeader: true,
        responseBody: true,
        error: true,
      ));
    }
    return dio;
  });

  // Logger Service (if not already registered)
  if (!sl.isRegistered<LoggerService>()) {
    sl.registerLazySingleton<LoggerService>(() => LoggerService());
  }

  // Logout Service (if not already registered)
  if (!sl.isRegistered<LogoutService>()) {
    sl.registerLazySingleton<LogoutService>(() => LogoutService());
  }

  // Error Handler (if not already registered)
  if (!sl.isRegistered<ErrorHandler>()) {
    sl.registerLazySingleton<ErrorHandler>(
        () => ErrorHandler(sl<LoggerService>()));
  }

  // ApiService
  if (!sl.isRegistered<ApiService>()) {
    sl.registerLazySingleton<ApiService>(() {
      final apiService = ApiService();
      try {
        final logger = sl<LoggerService>();
        final errorHandler = sl<ErrorHandler>();
        apiService.setLoggers(logger, errorHandler);
      } catch (e) {
        debugPrint('⚠️ Logger/ErrorHandler not available for ApiService: $e');
      }
      return apiService;
    });
  }
}

Future<void> _initExternal() async {
  // SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);

  // Connectivity
  sl.registerLazySingleton(() => Connectivity());

  // Secure Storage
  const storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  sl.registerLazySingleton(() => storage);

  // API Client
  sl.registerLazySingleton(() {
    final apiClient = ApiClient(sl());
    // Setup logger and error handler if available
    try {
      final logger = sl<LoggerService>();
      final errorHandler = sl<ErrorHandler>();
      apiClient.setLoggers(logger, errorHandler);
    } catch (e) {
      // Logger/ErrorHandler not available, continue without them
      debugPrint('⚠️ Logger/ErrorHandler not available for ApiClient: $e');
    }
    return apiClient;
  });
}

void _initHouseDetail() {
  // BLoC
  sl.registerFactory(() => HouseDetailBloc(
        getHouseDetailUseCase: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => GetHouseDetailUseCase(repository: sl()));

  // Repository
  sl.registerLazySingleton<HouseDetailRepository>(
    () => HouseDetailRepositoryImpl(
      remoteDataSource: sl(),
      cacheDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<HouseDetailRemoteDataSource>(
    () => HouseDetailRemoteDataSourceImpl(apiClient: sl<ApiClient>()),
  );

  sl.registerLazySingleton<HouseDetailCacheDataSource>(
    () => HouseDetailCacheDataSourceImpl(sharedPreferences: sl()),
  );
}

void _initPropertyDetail() {
  // BLoCs
  sl.registerFactory(() => PropertyDetailBloc(
        getPropertyDetail: sl(),
        togglePropertyLike: sl(),
        getRelatedProperties: sl(),
      ));

  sl.registerFactory(() => PropertyCommentBloc(
        getPropertyComments: sl(),
        addPropertyComment: sl(),
        replyToComment: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => GetPropertyDetail(sl()));
  sl.registerLazySingleton(() => TogglePropertyLike(sl()));
  sl.registerLazySingleton(() => GetRelatedProperties(sl()));
  sl.registerLazySingleton(() => GetPropertyComments(sl()));
  sl.registerLazySingleton(() => AddPropertyComment(sl()));
  sl.registerLazySingleton(() => ReplyToComment(sl()));

  // Repositories
  sl.registerLazySingleton<PropertyDetailRepository>(
    () => PropertyDetailRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  sl.registerLazySingleton<PropertyCommentRepository>(
    () => PropertyCommentRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<PropertyDetailRemoteDataSource>(
    () => PropertyDetailRemoteDataSourceImpl(client: sl()),
  );

  // HTTP Client (if not already registered)
  if (!sl.isRegistered<http.Client>()) {
    sl.registerLazySingleton<http.Client>(() => http.Client());
  }
}

void _initCreatePost() {
  // BLoC
  sl.registerFactory(() => CreatePostBloc(
        createPostUseCase: sl(),
        getPropertyTypesUseCase: sl(),
        uploadImagesUseCase: sl(),
        uploadVideoUseCase: sl(),
      ));

  // Use cases
  sl.registerLazySingleton(() => CreatePostUseCase(repository: sl()));
  sl.registerLazySingleton(() => GetPropertyTypesUseCase(repository: sl()));
  sl.registerLazySingleton(() => UploadImagesUseCase(repository: sl()));
  sl.registerLazySingleton(() => UploadVideoUseCase(repository: sl()));

  // Repository
  sl.registerLazySingleton<CreatePostRepository>(
    () => CreatePostRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<CreatePostRemoteDataSource>(
    () => CreatePostRemoteDataSourceImpl(apiClient: sl<ApiClient>()),
  );

  sl.registerLazySingleton<CreatePostLocalDataSource>(
    () => CreatePostLocalDataSourceImpl(storage: sl()),
  );
}

void _initRevenueManagement() {
  // BLoCs
  sl.registerFactory(() => RevenueSummaryBloc(
        getRevenueSummaryUseCase: sl(),
      ));

  sl.registerFactory(() => RevenueDetailBloc(
        getRevenueDetailUseCase: sl(),
        deleteRevenueTransactionUseCase: sl(),
      ));

  sl.registerFactory(() => NextMonthPlanBloc(
        getNextMonthPlanUseCase: sl(),
      ));

  sl.registerFactory(() => AddRevenueBloc(
        addRevenueTransactionUseCase: sl(),
      ));

  // Use Cases
  sl.registerLazySingleton(() => GetRevenueSummaryUseCase(sl()));
  sl.registerLazySingleton(() => GetRevenueDetailUseCase(sl()));
  sl.registerLazySingleton(() => GetNextMonthPlanUseCase(sl()));
  sl.registerLazySingleton(() => AddRevenueTransactionUseCase(sl()));
  sl.registerLazySingleton(() => DeleteRevenueTransactionUseCase(sl()));
  sl.registerLazySingleton(() => ExportRevenueExcelUseCase(sl()));

  // Repository
  sl.registerLazySingleton<RevenueRepository>(
    () => RevenueRepositoryImpl(
      remoteDataSource: sl(),
      cacheDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data Sources
  sl.registerLazySingleton<RevenueRemoteDataSource>(
    () => RevenueRemoteDataSourceImpl(apiClient: sl()),
  );

  sl.registerLazySingleton<RevenueCacheDataSource>(
    () => RevenueCacheDataSourceImpl(sharedPreferences: sl()),
  );
}

void _initCustomerManagement() {
  // Initialize Customer Management dependencies
  initCustomerManagementDependencies();
}

void _initChoHaiSan() {
  // BLoC
  sl.registerFactory(
    () => ChoHaiSanBloc(
      getCategoriesUseCase: sl(),
      getProductsUseCase: sl(),
    ),
  );

  // Use Cases
  sl.registerLazySingleton(
    () => cho_hai_san_categories.GetCategoriesUseCase(sl()),
  );

  // ApiService for Cho Hai San, Ticket, etc.
  if (!sl.isRegistered<ApiService>()) {
    sl.registerLazySingleton(() {
      final apiService = ApiService();
      try {
        final logger = sl<LoggerService>();
        final errorHandler = sl<ErrorHandler>();
        apiService.setLoggers(logger, errorHandler);
      } catch (e) {
        debugPrint('⚠️ Logger/ErrorHandler not available for ApiService: $e');
      }
      return apiService;
    });
  }

  sl.registerLazySingleton(() => GetProductsUseCase(sl()));
  sl.registerLazySingleton(() => GetProductDetailUseCase(sl()));

  // Repository
  sl.registerLazySingleton<ChoHaiSanRepository>(
    () => ChoHaiSanRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data Sources
  sl.registerLazySingleton<ChoHaiSanRemoteDataSource>(
    () => ChoHaiSanRemoteDataSourceImpl(apiService: sl()),
  );
}

void _initDoUong() {
  // Initialize Do Uong dependencies
  initDoUongInjection();
}

void _initTicket() {
  // Initialize Ticket dependencies
  initTicketInjection();
}

void _initInvoice() {
  // BLoC
  sl.registerFactory(
    () => InvoiceBloc(
      getInvoicesUseCase: sl(),
      getInvoiceDetailUseCase: sl(),
      updateInvoiceStatusUseCase: sl(),
    ),
  );

  // Use Cases
  sl.registerLazySingleton(() => GetInvoicesUseCase(sl()));
  sl.registerLazySingleton(() => GetInvoiceDetailUseCase(sl()));
  sl.registerLazySingleton(() => UpdateInvoiceStatusUseCase(sl()));

  // Repository
  sl.registerLazySingleton<InvoiceRepository>(
    () => InvoiceRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data Sources
  sl.registerLazySingleton<InvoiceRemoteDataSource>(
    () => InvoiceRemoteDataSourceImpl(
      apiClient: sl(),
    ),
  );
}

void _initBarKitchen() {
  // Initialize Bar/Kitchen dependencies
  initBarKitchenInjection();
}
