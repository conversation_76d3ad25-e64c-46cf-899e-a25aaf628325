class ServerException implements Exception {
  final String message;
  const ServerException({required this.message});

  @override
  String toString() => message;
}

class NetworkException implements Exception {
  final String message;
  const NetworkException({required this.message});

  @override
  String toString() => message;
}

class CacheException implements Exception {
  final String message;
  const CacheException({required this.message});

  @override
  String toString() => message;
}

class AuthException implements Exception {
  final String message;
  const AuthException({required this.message});

  @override
  String toString() => message;
}

class ValidationException implements Exception {
  final String message;
  const ValidationException({required this.message});

  @override
  String toString() => message;
}

class LocationException implements Exception {
  final String message;
  const LocationException({required this.message});

  @override
  String toString() => message;
}

class UnauthorizedException implements Exception {
  final String message;
  const UnauthorizedException({this.message = 'Unauthorized access'});

  @override
  String toString() => message;
}

class NotFoundException implements Exception {
  final String message;
  const NotFoundException({required this.message});

  @override
  String toString() => message;
}
