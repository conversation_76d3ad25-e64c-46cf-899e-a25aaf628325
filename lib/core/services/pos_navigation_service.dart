import 'package:flutter/material.dart';

/// Service để quản lý navigation giữa các tabs trong POS system
class PosNavigationService {
  static final PosNavigationService _instance =
      PosNavigationService._internal();
  factory PosNavigationService() => _instance;
  PosNavigationService._internal();

  /// Callback để navigate đến Payment tab
  VoidCallback? _navigateToPaymentTab;

  /// Đăng ký callback để navigate đến Payment tab
  void registerNavigateToPaymentTab(VoidCallback callback) {
    _navigateToPaymentTab = callback;
  }

  /// Navigate đến Payment tab
  void navigateToPaymentTab() {
    _navigateToPaymentTab?.call();
  }

  /// Clear callbacks khi dispose
  void dispose() {
    _navigateToPaymentTab = null;
  }
}
