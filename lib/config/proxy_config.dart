import 'package:flutter/foundation.dart';

class ProxyConfig {
  // Kích hoạt/vô hiệu hóa Proxyman
  static bool get enabled => _enabled;
  static bool _enabled = false; // Mặc định tắt

  // URL của Proxyman (IP:Port)
  // Lưu ý: <PERSON><PERSON><PERSON> sử dụng IP của máy chạy Proxyman, không phải localhost
  static String get proxyUrl => _proxyUrl;
  // static String _proxyUrl = '***************:9090';
  static String _proxyUrl = '***************:9090';

  // Kích hoạt Proxyman
  static void enable() {
    _enabled = true;
    debugPrint('✓ Proxyman đã được kích hoạt: $_proxyUrl');
  }

  // Vô hiệu hóa Proxyman
  static void disable() {
    _enabled = false;
    debugPrint('✗ Proxyman đã bị vô hiệu hóa');
  }

  // Cập nhật URL của Proxyman
  static void setProxyUrl(String url) {
    _proxyUrl = url;
    debugPrint('✓ Đã cập nhật URL Proxyman: $_proxyUrl');
  }
}
