import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import '../../../core/theme/pos_colors.dart';
import '../../../core/theme/pos_typography.dart';

class ProductItemWidget extends StatelessWidget {
  final String title;
  final String imageUrl;
  final String price;
  final String? oldPrice;
  final String? discount;
  final VoidCallback? onTap;

  final VoidCallback? onAddToCart;
  const ProductItemWidget({
    super.key,
    required this.title,
    required this.imageUrl,
    required this.price,
    this.oldPrice,
    this.discount,
    this.onTap,
    this.onAddToCart,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _ProductImage(
            imageUrl: imageUrl,
            discount: discount,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontFamily: 'Varela',
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: Color(0xFF333333),
              height: 1.0,
              letterSpacing: 0,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 16),
          _PriceSection(
            price: price,
            oldPrice: oldPrice,
          ),
        ],
      ),
    );
  }
}

class _ProductImage extends StatelessWidget {
  final String imageUrl;
  final String? discount;

  const _ProductImage({
    super.key,
    required this.imageUrl,
    this.discount,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: 167, // 250 * 2/3 ≈ 167
          decoration: BoxDecoration(
            border: Border.all(
              color: PosColors.borderLight,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: PosColors.borderLight,
                child: Center(
                  child: Icon(
                    Icons.confirmation_number,
                    color: PosColors.textSecondary,
                    size: 32,
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: PosColors.borderLight,
                child: Center(
                  child: Icon(
                    Icons.confirmation_number,
                    color: PosColors.textSecondary,
                    size: 32,
                  ),
                ),
              ),
            ),
          ),
        ),
        if (discount != null && discount!.isNotEmpty)
          Positioned(
            top: 6,
            right: 6,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 4,
                vertical: 1,
              ),
              decoration: BoxDecoration(
                color: PosColors.error,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '-$discount%',
                style: PosTypography.bodySmall.copyWith(
                  color: PosColors.textOnDark,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
      ],
    );
  }
}

class _PriceSection extends StatelessWidget {
  final String price;
  final VoidCallback? onAddToCart;
  final String? oldPrice;

  const _PriceSection({
    required this.price,
    this.oldPrice,
    this.onAddToCart,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          price,
          style: const TextStyle(
            fontFamily: 'Varela',
            fontSize: 20,
            fontWeight: FontWeight.w400,
            color: Color(0xFFB71C1C),
            height: 1.0,
            letterSpacing: 0,
          ),
        ),
        if (oldPrice != null) ...[
          const SizedBox(width: 8),
          Text(
            oldPrice!,
            style: const TextStyle(
              fontFamily: 'Varela',
              fontSize: 20,
              fontWeight: FontWeight.w400,
              color: Colors.grey,
              height: 1.0,
              letterSpacing: 0,
              decoration: TextDecoration.lineThrough,
            ),
          ),
        ],
        const Spacer(),
        if (onAddToCart != null)
          SizedBox(
            height: 32,
            child: ElevatedButton(
              onPressed: onAddToCart,
              style: ElevatedButton.styleFrom(
                backgroundColor: PosColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
                elevation: 0,
              ),
              child: const Text(
                'Thêm',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
