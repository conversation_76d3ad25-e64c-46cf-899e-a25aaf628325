import 'package:freezed_annotation/freezed_annotation.dart';
import 'pos_models.dart';

part 'table_api_models.freezed.dart';
part 'table_api_models.g.dart';

/// API Response wrapper cho tất cả API calls
@freezed
class ApiResponse with _$ApiResponse {
  const factory ApiResponse({
    required bool error,
    required String message,
    Map<String, dynamic>? data,
  }) = _ApiResponse;

  factory ApiResponse.fromJson(Map<String, dynamic> json) =>
      _$ApiResponseFromJson(json);
}

/// Model cho Table từ API backend
@freezed
class TableApiModel with _$TableApiModel {
  const factory TableApiModel({
    required String tableId,
    required String name,
    required String status, // 'available', 'occupied', 'reserved', 'disabled'
    required int capacity,
    required TablePosition position,
    required TableSize size,
    @Default('circle') String shape,
    String? note,
    @Default(0) int priority,
    @Default([]) List<String> tags,
    
    // Thông tin đặt bàn hiện tại
    String? currentOrderId,
    String? currentCustomerName,
    String? currentCustomerPhone,
    int? reservedAt,
    int? estimatedDuration,
    
    // Thông tin khu vực
    required String areaId,
    required String areaName,
    String? areaDescription,
    @Default(0) int areaDisplayOrder,
    
    // Metadata
    int? lastUpdated,
    @Default(true) bool isActive,
  }) = _TableApiModel;

  factory TableApiModel.fromJson(Map<String, dynamic> json) =>
      _$TableApiModelFromJson(json);
}

/// Model cho vị trí bàn
@freezed
class TablePosition with _$TablePosition {
  const factory TablePosition({
    @Default(0) double x,
    @Default(0) double y,
  }) = _TablePosition;

  factory TablePosition.fromJson(Map<String, dynamic> json) =>
      _$TablePositionFromJson(json);
}

/// Model cho kích thước bàn
@freezed
class TableSize with _$TableSize {
  const factory TableSize({
    @Default(60) double width,
    @Default(60) double height,
  }) = _TableSize;

  factory TableSize.fromJson(Map<String, dynamic> json) =>
      _$TableSizeFromJson(json);
}

/// Model cho thống kê bàn ăn
@freezed
class TableStats with _$TableStats {
  const factory TableStats({
    @Default(0) int total,
    @Default(0) int available,
    @Default(0) int occupied,
    @Default(0) int reserved,
    @Default(0) int disabled,
    @Default(0) int totalCapacity,
    @Default(0) int areas,
  }) = _TableStats;

  factory TableStats.fromJson(Map<String, dynamic> json) =>
      _$TableStatsFromJson(json);
}

/// Model cho response của API all-tables
@freezed
class AllTablesResponse with _$AllTablesResponse {
  const factory AllTablesResponse({
    required List<TableApiModel> tables,
    required TableStats stats,
    AllTablesFilters? filters,
  }) = _AllTablesResponse;

  factory AllTablesResponse.fromJson(Map<String, dynamic> json) =>
      _$AllTablesResponseFromJson(json);
}

/// Model cho filters của API all-tables
@freezed
class AllTablesFilters with _$AllTablesFilters {
  const factory AllTablesFilters({
    required String storeId,
    List<String>? statusFilter,
  }) = _AllTablesFilters;

  factory AllTablesFilters.fromJson(Map<String, dynamic> json) =>
      _$AllTablesFiltersFromJson(json);
}

/// Extension để convert từ TableApiModel sang RestaurantTable (model hiện tại)
extension TableApiModelExtension on TableApiModel {
  RestaurantTable toRestaurantTable() {
    return RestaurantTable(
      id: tableId,
      name: name,
      capacity: capacity,
      status: _mapApiStatusToTableStatus(status),
      currentOrderId: currentOrderId,
      occupiedAt: reservedAt != null ? DateTime.fromMillisecondsSinceEpoch(reservedAt!) : null,
      x: position.x,
      y: position.y,
    );
  }

  TableStatus _mapApiStatusToTableStatus(String apiStatus) {
    switch (apiStatus.toLowerCase()) {
      case 'available':
        return TableStatus.available;
      case 'occupied':
        return TableStatus.occupied;
      case 'reserved':
        return TableStatus.reserved;
      case 'disabled':
      case 'cleaning':
        return TableStatus.cleaning;
      default:
        return TableStatus.available;
    }
  }
}
