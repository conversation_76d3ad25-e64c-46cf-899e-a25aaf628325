// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'table_api_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ApiResponse _$ApiResponseFromJson(Map<String, dynamic> json) {
  return _ApiResponse.fromJson(json);
}

/// @nodoc
mixin _$ApiResponse {
  bool get error => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  Map<String, dynamic>? get data => throw _privateConstructorUsedError;

  /// Serializes this ApiResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ApiResponseCopyWith<ApiResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApiResponseCopyWith<$Res> {
  factory $ApiResponseCopyWith(
          ApiResponse value, $Res Function(ApiResponse) then) =
      _$ApiResponseCopyWithImpl<$Res, ApiResponse>;
  @useResult
  $Res call({bool error, String message, Map<String, dynamic>? data});
}

/// @nodoc
class _$ApiResponseCopyWithImpl<$Res, $Val extends ApiResponse>
    implements $ApiResponseCopyWith<$Res> {
  _$ApiResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
    Object? message = null,
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ApiResponseImplCopyWith<$Res>
    implements $ApiResponseCopyWith<$Res> {
  factory _$$ApiResponseImplCopyWith(
          _$ApiResponseImpl value, $Res Function(_$ApiResponseImpl) then) =
      __$$ApiResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool error, String message, Map<String, dynamic>? data});
}

/// @nodoc
class __$$ApiResponseImplCopyWithImpl<$Res>
    extends _$ApiResponseCopyWithImpl<$Res, _$ApiResponseImpl>
    implements _$$ApiResponseImplCopyWith<$Res> {
  __$$ApiResponseImplCopyWithImpl(
      _$ApiResponseImpl _value, $Res Function(_$ApiResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
    Object? message = null,
    Object? data = freezed,
  }) {
    return _then(_$ApiResponseImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ApiResponseImpl implements _ApiResponse {
  const _$ApiResponseImpl(
      {required this.error,
      required this.message,
      final Map<String, dynamic>? data})
      : _data = data;

  factory _$ApiResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ApiResponseImplFromJson(json);

  @override
  final bool error;
  @override
  final String message;
  final Map<String, dynamic>? _data;
  @override
  Map<String, dynamic>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'ApiResponse(error: $error, message: $message, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApiResponseImpl &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, error, message, const DeepCollectionEquality().hash(_data));

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApiResponseImplCopyWith<_$ApiResponseImpl> get copyWith =>
      __$$ApiResponseImplCopyWithImpl<_$ApiResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ApiResponseImplToJson(
      this,
    );
  }
}

abstract class _ApiResponse implements ApiResponse {
  const factory _ApiResponse(
      {required final bool error,
      required final String message,
      final Map<String, dynamic>? data}) = _$ApiResponseImpl;

  factory _ApiResponse.fromJson(Map<String, dynamic> json) =
      _$ApiResponseImpl.fromJson;

  @override
  bool get error;
  @override
  String get message;
  @override
  Map<String, dynamic>? get data;

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApiResponseImplCopyWith<_$ApiResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TableApiModel _$TableApiModelFromJson(Map<String, dynamic> json) {
  return _TableApiModel.fromJson(json);
}

/// @nodoc
mixin _$TableApiModel {
  String get tableId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get status =>
      throw _privateConstructorUsedError; // 'available', 'occupied', 'reserved', 'disabled'
  int get capacity => throw _privateConstructorUsedError;
  TablePosition get position => throw _privateConstructorUsedError;
  TableSize get size => throw _privateConstructorUsedError;
  String get shape => throw _privateConstructorUsedError;
  String? get note => throw _privateConstructorUsedError;
  int get priority => throw _privateConstructorUsedError;
  List<String> get tags =>
      throw _privateConstructorUsedError; // Thông tin đặt bàn hiện tại
  String? get currentOrderId => throw _privateConstructorUsedError;
  String? get currentCustomerName => throw _privateConstructorUsedError;
  String? get currentCustomerPhone => throw _privateConstructorUsedError;
  int? get reservedAt => throw _privateConstructorUsedError;
  int? get estimatedDuration =>
      throw _privateConstructorUsedError; // Thông tin khu vực
  String get areaId => throw _privateConstructorUsedError;
  String get areaName => throw _privateConstructorUsedError;
  String? get areaDescription => throw _privateConstructorUsedError;
  int get areaDisplayOrder => throw _privateConstructorUsedError; // Metadata
  int? get lastUpdated => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;

  /// Serializes this TableApiModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TableApiModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TableApiModelCopyWith<TableApiModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TableApiModelCopyWith<$Res> {
  factory $TableApiModelCopyWith(
          TableApiModel value, $Res Function(TableApiModel) then) =
      _$TableApiModelCopyWithImpl<$Res, TableApiModel>;
  @useResult
  $Res call(
      {String tableId,
      String name,
      String status,
      int capacity,
      TablePosition position,
      TableSize size,
      String shape,
      String? note,
      int priority,
      List<String> tags,
      String? currentOrderId,
      String? currentCustomerName,
      String? currentCustomerPhone,
      int? reservedAt,
      int? estimatedDuration,
      String areaId,
      String areaName,
      String? areaDescription,
      int areaDisplayOrder,
      int? lastUpdated,
      bool isActive});

  $TablePositionCopyWith<$Res> get position;
  $TableSizeCopyWith<$Res> get size;
}

/// @nodoc
class _$TableApiModelCopyWithImpl<$Res, $Val extends TableApiModel>
    implements $TableApiModelCopyWith<$Res> {
  _$TableApiModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TableApiModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tableId = null,
    Object? name = null,
    Object? status = null,
    Object? capacity = null,
    Object? position = null,
    Object? size = null,
    Object? shape = null,
    Object? note = freezed,
    Object? priority = null,
    Object? tags = null,
    Object? currentOrderId = freezed,
    Object? currentCustomerName = freezed,
    Object? currentCustomerPhone = freezed,
    Object? reservedAt = freezed,
    Object? estimatedDuration = freezed,
    Object? areaId = null,
    Object? areaName = null,
    Object? areaDescription = freezed,
    Object? areaDisplayOrder = null,
    Object? lastUpdated = freezed,
    Object? isActive = null,
  }) {
    return _then(_value.copyWith(
      tableId: null == tableId
          ? _value.tableId
          : tableId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      capacity: null == capacity
          ? _value.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as int,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as TablePosition,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as TableSize,
      shape: null == shape
          ? _value.shape
          : shape // ignore: cast_nullable_to_non_nullable
              as String,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      currentOrderId: freezed == currentOrderId
          ? _value.currentOrderId
          : currentOrderId // ignore: cast_nullable_to_non_nullable
              as String?,
      currentCustomerName: freezed == currentCustomerName
          ? _value.currentCustomerName
          : currentCustomerName // ignore: cast_nullable_to_non_nullable
              as String?,
      currentCustomerPhone: freezed == currentCustomerPhone
          ? _value.currentCustomerPhone
          : currentCustomerPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      reservedAt: freezed == reservedAt
          ? _value.reservedAt
          : reservedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      estimatedDuration: freezed == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      areaId: null == areaId
          ? _value.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      areaName: null == areaName
          ? _value.areaName
          : areaName // ignore: cast_nullable_to_non_nullable
              as String,
      areaDescription: freezed == areaDescription
          ? _value.areaDescription
          : areaDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      areaDisplayOrder: null == areaDisplayOrder
          ? _value.areaDisplayOrder
          : areaDisplayOrder // ignore: cast_nullable_to_non_nullable
              as int,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as int?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of TableApiModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TablePositionCopyWith<$Res> get position {
    return $TablePositionCopyWith<$Res>(_value.position, (value) {
      return _then(_value.copyWith(position: value) as $Val);
    });
  }

  /// Create a copy of TableApiModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TableSizeCopyWith<$Res> get size {
    return $TableSizeCopyWith<$Res>(_value.size, (value) {
      return _then(_value.copyWith(size: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TableApiModelImplCopyWith<$Res>
    implements $TableApiModelCopyWith<$Res> {
  factory _$$TableApiModelImplCopyWith(
          _$TableApiModelImpl value, $Res Function(_$TableApiModelImpl) then) =
      __$$TableApiModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String tableId,
      String name,
      String status,
      int capacity,
      TablePosition position,
      TableSize size,
      String shape,
      String? note,
      int priority,
      List<String> tags,
      String? currentOrderId,
      String? currentCustomerName,
      String? currentCustomerPhone,
      int? reservedAt,
      int? estimatedDuration,
      String areaId,
      String areaName,
      String? areaDescription,
      int areaDisplayOrder,
      int? lastUpdated,
      bool isActive});

  @override
  $TablePositionCopyWith<$Res> get position;
  @override
  $TableSizeCopyWith<$Res> get size;
}

/// @nodoc
class __$$TableApiModelImplCopyWithImpl<$Res>
    extends _$TableApiModelCopyWithImpl<$Res, _$TableApiModelImpl>
    implements _$$TableApiModelImplCopyWith<$Res> {
  __$$TableApiModelImplCopyWithImpl(
      _$TableApiModelImpl _value, $Res Function(_$TableApiModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TableApiModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tableId = null,
    Object? name = null,
    Object? status = null,
    Object? capacity = null,
    Object? position = null,
    Object? size = null,
    Object? shape = null,
    Object? note = freezed,
    Object? priority = null,
    Object? tags = null,
    Object? currentOrderId = freezed,
    Object? currentCustomerName = freezed,
    Object? currentCustomerPhone = freezed,
    Object? reservedAt = freezed,
    Object? estimatedDuration = freezed,
    Object? areaId = null,
    Object? areaName = null,
    Object? areaDescription = freezed,
    Object? areaDisplayOrder = null,
    Object? lastUpdated = freezed,
    Object? isActive = null,
  }) {
    return _then(_$TableApiModelImpl(
      tableId: null == tableId
          ? _value.tableId
          : tableId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      capacity: null == capacity
          ? _value.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as int,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as TablePosition,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as TableSize,
      shape: null == shape
          ? _value.shape
          : shape // ignore: cast_nullable_to_non_nullable
              as String,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      currentOrderId: freezed == currentOrderId
          ? _value.currentOrderId
          : currentOrderId // ignore: cast_nullable_to_non_nullable
              as String?,
      currentCustomerName: freezed == currentCustomerName
          ? _value.currentCustomerName
          : currentCustomerName // ignore: cast_nullable_to_non_nullable
              as String?,
      currentCustomerPhone: freezed == currentCustomerPhone
          ? _value.currentCustomerPhone
          : currentCustomerPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      reservedAt: freezed == reservedAt
          ? _value.reservedAt
          : reservedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      estimatedDuration: freezed == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      areaId: null == areaId
          ? _value.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      areaName: null == areaName
          ? _value.areaName
          : areaName // ignore: cast_nullable_to_non_nullable
              as String,
      areaDescription: freezed == areaDescription
          ? _value.areaDescription
          : areaDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      areaDisplayOrder: null == areaDisplayOrder
          ? _value.areaDisplayOrder
          : areaDisplayOrder // ignore: cast_nullable_to_non_nullable
              as int,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as int?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TableApiModelImpl implements _TableApiModel {
  const _$TableApiModelImpl(
      {required this.tableId,
      required this.name,
      required this.status,
      required this.capacity,
      required this.position,
      required this.size,
      this.shape = 'circle',
      this.note,
      this.priority = 0,
      final List<String> tags = const [],
      this.currentOrderId,
      this.currentCustomerName,
      this.currentCustomerPhone,
      this.reservedAt,
      this.estimatedDuration,
      required this.areaId,
      required this.areaName,
      this.areaDescription,
      this.areaDisplayOrder = 0,
      this.lastUpdated,
      this.isActive = true})
      : _tags = tags;

  factory _$TableApiModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TableApiModelImplFromJson(json);

  @override
  final String tableId;
  @override
  final String name;
  @override
  final String status;
// 'available', 'occupied', 'reserved', 'disabled'
  @override
  final int capacity;
  @override
  final TablePosition position;
  @override
  final TableSize size;
  @override
  @JsonKey()
  final String shape;
  @override
  final String? note;
  @override
  @JsonKey()
  final int priority;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

// Thông tin đặt bàn hiện tại
  @override
  final String? currentOrderId;
  @override
  final String? currentCustomerName;
  @override
  final String? currentCustomerPhone;
  @override
  final int? reservedAt;
  @override
  final int? estimatedDuration;
// Thông tin khu vực
  @override
  final String areaId;
  @override
  final String areaName;
  @override
  final String? areaDescription;
  @override
  @JsonKey()
  final int areaDisplayOrder;
// Metadata
  @override
  final int? lastUpdated;
  @override
  @JsonKey()
  final bool isActive;

  @override
  String toString() {
    return 'TableApiModel(tableId: $tableId, name: $name, status: $status, capacity: $capacity, position: $position, size: $size, shape: $shape, note: $note, priority: $priority, tags: $tags, currentOrderId: $currentOrderId, currentCustomerName: $currentCustomerName, currentCustomerPhone: $currentCustomerPhone, reservedAt: $reservedAt, estimatedDuration: $estimatedDuration, areaId: $areaId, areaName: $areaName, areaDescription: $areaDescription, areaDisplayOrder: $areaDisplayOrder, lastUpdated: $lastUpdated, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TableApiModelImpl &&
            (identical(other.tableId, tableId) || other.tableId == tableId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.capacity, capacity) ||
                other.capacity == capacity) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.shape, shape) || other.shape == shape) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.currentOrderId, currentOrderId) ||
                other.currentOrderId == currentOrderId) &&
            (identical(other.currentCustomerName, currentCustomerName) ||
                other.currentCustomerName == currentCustomerName) &&
            (identical(other.currentCustomerPhone, currentCustomerPhone) ||
                other.currentCustomerPhone == currentCustomerPhone) &&
            (identical(other.reservedAt, reservedAt) ||
                other.reservedAt == reservedAt) &&
            (identical(other.estimatedDuration, estimatedDuration) ||
                other.estimatedDuration == estimatedDuration) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.areaName, areaName) ||
                other.areaName == areaName) &&
            (identical(other.areaDescription, areaDescription) ||
                other.areaDescription == areaDescription) &&
            (identical(other.areaDisplayOrder, areaDisplayOrder) ||
                other.areaDisplayOrder == areaDisplayOrder) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        tableId,
        name,
        status,
        capacity,
        position,
        size,
        shape,
        note,
        priority,
        const DeepCollectionEquality().hash(_tags),
        currentOrderId,
        currentCustomerName,
        currentCustomerPhone,
        reservedAt,
        estimatedDuration,
        areaId,
        areaName,
        areaDescription,
        areaDisplayOrder,
        lastUpdated,
        isActive
      ]);

  /// Create a copy of TableApiModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TableApiModelImplCopyWith<_$TableApiModelImpl> get copyWith =>
      __$$TableApiModelImplCopyWithImpl<_$TableApiModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TableApiModelImplToJson(
      this,
    );
  }
}

abstract class _TableApiModel implements TableApiModel {
  const factory _TableApiModel(
      {required final String tableId,
      required final String name,
      required final String status,
      required final int capacity,
      required final TablePosition position,
      required final TableSize size,
      final String shape,
      final String? note,
      final int priority,
      final List<String> tags,
      final String? currentOrderId,
      final String? currentCustomerName,
      final String? currentCustomerPhone,
      final int? reservedAt,
      final int? estimatedDuration,
      required final String areaId,
      required final String areaName,
      final String? areaDescription,
      final int areaDisplayOrder,
      final int? lastUpdated,
      final bool isActive}) = _$TableApiModelImpl;

  factory _TableApiModel.fromJson(Map<String, dynamic> json) =
      _$TableApiModelImpl.fromJson;

  @override
  String get tableId;
  @override
  String get name;
  @override
  String get status; // 'available', 'occupied', 'reserved', 'disabled'
  @override
  int get capacity;
  @override
  TablePosition get position;
  @override
  TableSize get size;
  @override
  String get shape;
  @override
  String? get note;
  @override
  int get priority;
  @override
  List<String> get tags; // Thông tin đặt bàn hiện tại
  @override
  String? get currentOrderId;
  @override
  String? get currentCustomerName;
  @override
  String? get currentCustomerPhone;
  @override
  int? get reservedAt;
  @override
  int? get estimatedDuration; // Thông tin khu vực
  @override
  String get areaId;
  @override
  String get areaName;
  @override
  String? get areaDescription;
  @override
  int get areaDisplayOrder; // Metadata
  @override
  int? get lastUpdated;
  @override
  bool get isActive;

  /// Create a copy of TableApiModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TableApiModelImplCopyWith<_$TableApiModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TablePosition _$TablePositionFromJson(Map<String, dynamic> json) {
  return _TablePosition.fromJson(json);
}

/// @nodoc
mixin _$TablePosition {
  double get x => throw _privateConstructorUsedError;
  double get y => throw _privateConstructorUsedError;

  /// Serializes this TablePosition to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TablePosition
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TablePositionCopyWith<TablePosition> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TablePositionCopyWith<$Res> {
  factory $TablePositionCopyWith(
          TablePosition value, $Res Function(TablePosition) then) =
      _$TablePositionCopyWithImpl<$Res, TablePosition>;
  @useResult
  $Res call({double x, double y});
}

/// @nodoc
class _$TablePositionCopyWithImpl<$Res, $Val extends TablePosition>
    implements $TablePositionCopyWith<$Res> {
  _$TablePositionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TablePosition
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? x = null,
    Object? y = null,
  }) {
    return _then(_value.copyWith(
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TablePositionImplCopyWith<$Res>
    implements $TablePositionCopyWith<$Res> {
  factory _$$TablePositionImplCopyWith(
          _$TablePositionImpl value, $Res Function(_$TablePositionImpl) then) =
      __$$TablePositionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double x, double y});
}

/// @nodoc
class __$$TablePositionImplCopyWithImpl<$Res>
    extends _$TablePositionCopyWithImpl<$Res, _$TablePositionImpl>
    implements _$$TablePositionImplCopyWith<$Res> {
  __$$TablePositionImplCopyWithImpl(
      _$TablePositionImpl _value, $Res Function(_$TablePositionImpl) _then)
      : super(_value, _then);

  /// Create a copy of TablePosition
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? x = null,
    Object? y = null,
  }) {
    return _then(_$TablePositionImpl(
      x: null == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double,
      y: null == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TablePositionImpl implements _TablePosition {
  const _$TablePositionImpl({this.x = 0, this.y = 0});

  factory _$TablePositionImpl.fromJson(Map<String, dynamic> json) =>
      _$$TablePositionImplFromJson(json);

  @override
  @JsonKey()
  final double x;
  @override
  @JsonKey()
  final double y;

  @override
  String toString() {
    return 'TablePosition(x: $x, y: $y)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TablePositionImpl &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, x, y);

  /// Create a copy of TablePosition
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TablePositionImplCopyWith<_$TablePositionImpl> get copyWith =>
      __$$TablePositionImplCopyWithImpl<_$TablePositionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TablePositionImplToJson(
      this,
    );
  }
}

abstract class _TablePosition implements TablePosition {
  const factory _TablePosition({final double x, final double y}) =
      _$TablePositionImpl;

  factory _TablePosition.fromJson(Map<String, dynamic> json) =
      _$TablePositionImpl.fromJson;

  @override
  double get x;
  @override
  double get y;

  /// Create a copy of TablePosition
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TablePositionImplCopyWith<_$TablePositionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TableSize _$TableSizeFromJson(Map<String, dynamic> json) {
  return _TableSize.fromJson(json);
}

/// @nodoc
mixin _$TableSize {
  double get width => throw _privateConstructorUsedError;
  double get height => throw _privateConstructorUsedError;

  /// Serializes this TableSize to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TableSize
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TableSizeCopyWith<TableSize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TableSizeCopyWith<$Res> {
  factory $TableSizeCopyWith(TableSize value, $Res Function(TableSize) then) =
      _$TableSizeCopyWithImpl<$Res, TableSize>;
  @useResult
  $Res call({double width, double height});
}

/// @nodoc
class _$TableSizeCopyWithImpl<$Res, $Val extends TableSize>
    implements $TableSizeCopyWith<$Res> {
  _$TableSizeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TableSize
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? width = null,
    Object? height = null,
  }) {
    return _then(_value.copyWith(
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TableSizeImplCopyWith<$Res>
    implements $TableSizeCopyWith<$Res> {
  factory _$$TableSizeImplCopyWith(
          _$TableSizeImpl value, $Res Function(_$TableSizeImpl) then) =
      __$$TableSizeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double width, double height});
}

/// @nodoc
class __$$TableSizeImplCopyWithImpl<$Res>
    extends _$TableSizeCopyWithImpl<$Res, _$TableSizeImpl>
    implements _$$TableSizeImplCopyWith<$Res> {
  __$$TableSizeImplCopyWithImpl(
      _$TableSizeImpl _value, $Res Function(_$TableSizeImpl) _then)
      : super(_value, _then);

  /// Create a copy of TableSize
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? width = null,
    Object? height = null,
  }) {
    return _then(_$TableSizeImpl(
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TableSizeImpl implements _TableSize {
  const _$TableSizeImpl({this.width = 60, this.height = 60});

  factory _$TableSizeImpl.fromJson(Map<String, dynamic> json) =>
      _$$TableSizeImplFromJson(json);

  @override
  @JsonKey()
  final double width;
  @override
  @JsonKey()
  final double height;

  @override
  String toString() {
    return 'TableSize(width: $width, height: $height)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TableSizeImpl &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, width, height);

  /// Create a copy of TableSize
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TableSizeImplCopyWith<_$TableSizeImpl> get copyWith =>
      __$$TableSizeImplCopyWithImpl<_$TableSizeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TableSizeImplToJson(
      this,
    );
  }
}

abstract class _TableSize implements TableSize {
  const factory _TableSize({final double width, final double height}) =
      _$TableSizeImpl;

  factory _TableSize.fromJson(Map<String, dynamic> json) =
      _$TableSizeImpl.fromJson;

  @override
  double get width;
  @override
  double get height;

  /// Create a copy of TableSize
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TableSizeImplCopyWith<_$TableSizeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TableStats _$TableStatsFromJson(Map<String, dynamic> json) {
  return _TableStats.fromJson(json);
}

/// @nodoc
mixin _$TableStats {
  int get total => throw _privateConstructorUsedError;
  int get available => throw _privateConstructorUsedError;
  int get occupied => throw _privateConstructorUsedError;
  int get reserved => throw _privateConstructorUsedError;
  int get disabled => throw _privateConstructorUsedError;
  int get totalCapacity => throw _privateConstructorUsedError;
  int get areas => throw _privateConstructorUsedError;

  /// Serializes this TableStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TableStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TableStatsCopyWith<TableStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TableStatsCopyWith<$Res> {
  factory $TableStatsCopyWith(
          TableStats value, $Res Function(TableStats) then) =
      _$TableStatsCopyWithImpl<$Res, TableStats>;
  @useResult
  $Res call(
      {int total,
      int available,
      int occupied,
      int reserved,
      int disabled,
      int totalCapacity,
      int areas});
}

/// @nodoc
class _$TableStatsCopyWithImpl<$Res, $Val extends TableStats>
    implements $TableStatsCopyWith<$Res> {
  _$TableStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TableStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? available = null,
    Object? occupied = null,
    Object? reserved = null,
    Object? disabled = null,
    Object? totalCapacity = null,
    Object? areas = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      available: null == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as int,
      occupied: null == occupied
          ? _value.occupied
          : occupied // ignore: cast_nullable_to_non_nullable
              as int,
      reserved: null == reserved
          ? _value.reserved
          : reserved // ignore: cast_nullable_to_non_nullable
              as int,
      disabled: null == disabled
          ? _value.disabled
          : disabled // ignore: cast_nullable_to_non_nullable
              as int,
      totalCapacity: null == totalCapacity
          ? _value.totalCapacity
          : totalCapacity // ignore: cast_nullable_to_non_nullable
              as int,
      areas: null == areas
          ? _value.areas
          : areas // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TableStatsImplCopyWith<$Res>
    implements $TableStatsCopyWith<$Res> {
  factory _$$TableStatsImplCopyWith(
          _$TableStatsImpl value, $Res Function(_$TableStatsImpl) then) =
      __$$TableStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int total,
      int available,
      int occupied,
      int reserved,
      int disabled,
      int totalCapacity,
      int areas});
}

/// @nodoc
class __$$TableStatsImplCopyWithImpl<$Res>
    extends _$TableStatsCopyWithImpl<$Res, _$TableStatsImpl>
    implements _$$TableStatsImplCopyWith<$Res> {
  __$$TableStatsImplCopyWithImpl(
      _$TableStatsImpl _value, $Res Function(_$TableStatsImpl) _then)
      : super(_value, _then);

  /// Create a copy of TableStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? available = null,
    Object? occupied = null,
    Object? reserved = null,
    Object? disabled = null,
    Object? totalCapacity = null,
    Object? areas = null,
  }) {
    return _then(_$TableStatsImpl(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      available: null == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as int,
      occupied: null == occupied
          ? _value.occupied
          : occupied // ignore: cast_nullable_to_non_nullable
              as int,
      reserved: null == reserved
          ? _value.reserved
          : reserved // ignore: cast_nullable_to_non_nullable
              as int,
      disabled: null == disabled
          ? _value.disabled
          : disabled // ignore: cast_nullable_to_non_nullable
              as int,
      totalCapacity: null == totalCapacity
          ? _value.totalCapacity
          : totalCapacity // ignore: cast_nullable_to_non_nullable
              as int,
      areas: null == areas
          ? _value.areas
          : areas // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TableStatsImpl implements _TableStats {
  const _$TableStatsImpl(
      {this.total = 0,
      this.available = 0,
      this.occupied = 0,
      this.reserved = 0,
      this.disabled = 0,
      this.totalCapacity = 0,
      this.areas = 0});

  factory _$TableStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$TableStatsImplFromJson(json);

  @override
  @JsonKey()
  final int total;
  @override
  @JsonKey()
  final int available;
  @override
  @JsonKey()
  final int occupied;
  @override
  @JsonKey()
  final int reserved;
  @override
  @JsonKey()
  final int disabled;
  @override
  @JsonKey()
  final int totalCapacity;
  @override
  @JsonKey()
  final int areas;

  @override
  String toString() {
    return 'TableStats(total: $total, available: $available, occupied: $occupied, reserved: $reserved, disabled: $disabled, totalCapacity: $totalCapacity, areas: $areas)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TableStatsImpl &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.available, available) ||
                other.available == available) &&
            (identical(other.occupied, occupied) ||
                other.occupied == occupied) &&
            (identical(other.reserved, reserved) ||
                other.reserved == reserved) &&
            (identical(other.disabled, disabled) ||
                other.disabled == disabled) &&
            (identical(other.totalCapacity, totalCapacity) ||
                other.totalCapacity == totalCapacity) &&
            (identical(other.areas, areas) || other.areas == areas));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, total, available, occupied,
      reserved, disabled, totalCapacity, areas);

  /// Create a copy of TableStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TableStatsImplCopyWith<_$TableStatsImpl> get copyWith =>
      __$$TableStatsImplCopyWithImpl<_$TableStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TableStatsImplToJson(
      this,
    );
  }
}

abstract class _TableStats implements TableStats {
  const factory _TableStats(
      {final int total,
      final int available,
      final int occupied,
      final int reserved,
      final int disabled,
      final int totalCapacity,
      final int areas}) = _$TableStatsImpl;

  factory _TableStats.fromJson(Map<String, dynamic> json) =
      _$TableStatsImpl.fromJson;

  @override
  int get total;
  @override
  int get available;
  @override
  int get occupied;
  @override
  int get reserved;
  @override
  int get disabled;
  @override
  int get totalCapacity;
  @override
  int get areas;

  /// Create a copy of TableStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TableStatsImplCopyWith<_$TableStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AllTablesResponse _$AllTablesResponseFromJson(Map<String, dynamic> json) {
  return _AllTablesResponse.fromJson(json);
}

/// @nodoc
mixin _$AllTablesResponse {
  List<TableApiModel> get tables => throw _privateConstructorUsedError;
  TableStats get stats => throw _privateConstructorUsedError;
  AllTablesFilters? get filters => throw _privateConstructorUsedError;

  /// Serializes this AllTablesResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AllTablesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AllTablesResponseCopyWith<AllTablesResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AllTablesResponseCopyWith<$Res> {
  factory $AllTablesResponseCopyWith(
          AllTablesResponse value, $Res Function(AllTablesResponse) then) =
      _$AllTablesResponseCopyWithImpl<$Res, AllTablesResponse>;
  @useResult
  $Res call(
      {List<TableApiModel> tables,
      TableStats stats,
      AllTablesFilters? filters});

  $TableStatsCopyWith<$Res> get stats;
  $AllTablesFiltersCopyWith<$Res>? get filters;
}

/// @nodoc
class _$AllTablesResponseCopyWithImpl<$Res, $Val extends AllTablesResponse>
    implements $AllTablesResponseCopyWith<$Res> {
  _$AllTablesResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AllTablesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tables = null,
    Object? stats = null,
    Object? filters = freezed,
  }) {
    return _then(_value.copyWith(
      tables: null == tables
          ? _value.tables
          : tables // ignore: cast_nullable_to_non_nullable
              as List<TableApiModel>,
      stats: null == stats
          ? _value.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as TableStats,
      filters: freezed == filters
          ? _value.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as AllTablesFilters?,
    ) as $Val);
  }

  /// Create a copy of AllTablesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TableStatsCopyWith<$Res> get stats {
    return $TableStatsCopyWith<$Res>(_value.stats, (value) {
      return _then(_value.copyWith(stats: value) as $Val);
    });
  }

  /// Create a copy of AllTablesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AllTablesFiltersCopyWith<$Res>? get filters {
    if (_value.filters == null) {
      return null;
    }

    return $AllTablesFiltersCopyWith<$Res>(_value.filters!, (value) {
      return _then(_value.copyWith(filters: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AllTablesResponseImplCopyWith<$Res>
    implements $AllTablesResponseCopyWith<$Res> {
  factory _$$AllTablesResponseImplCopyWith(_$AllTablesResponseImpl value,
          $Res Function(_$AllTablesResponseImpl) then) =
      __$$AllTablesResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<TableApiModel> tables,
      TableStats stats,
      AllTablesFilters? filters});

  @override
  $TableStatsCopyWith<$Res> get stats;
  @override
  $AllTablesFiltersCopyWith<$Res>? get filters;
}

/// @nodoc
class __$$AllTablesResponseImplCopyWithImpl<$Res>
    extends _$AllTablesResponseCopyWithImpl<$Res, _$AllTablesResponseImpl>
    implements _$$AllTablesResponseImplCopyWith<$Res> {
  __$$AllTablesResponseImplCopyWithImpl(_$AllTablesResponseImpl _value,
      $Res Function(_$AllTablesResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of AllTablesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tables = null,
    Object? stats = null,
    Object? filters = freezed,
  }) {
    return _then(_$AllTablesResponseImpl(
      tables: null == tables
          ? _value._tables
          : tables // ignore: cast_nullable_to_non_nullable
              as List<TableApiModel>,
      stats: null == stats
          ? _value.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as TableStats,
      filters: freezed == filters
          ? _value.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as AllTablesFilters?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AllTablesResponseImpl implements _AllTablesResponse {
  const _$AllTablesResponseImpl(
      {required final List<TableApiModel> tables,
      required this.stats,
      this.filters})
      : _tables = tables;

  factory _$AllTablesResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AllTablesResponseImplFromJson(json);

  final List<TableApiModel> _tables;
  @override
  List<TableApiModel> get tables {
    if (_tables is EqualUnmodifiableListView) return _tables;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tables);
  }

  @override
  final TableStats stats;
  @override
  final AllTablesFilters? filters;

  @override
  String toString() {
    return 'AllTablesResponse(tables: $tables, stats: $stats, filters: $filters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllTablesResponseImpl &&
            const DeepCollectionEquality().equals(other._tables, _tables) &&
            (identical(other.stats, stats) || other.stats == stats) &&
            (identical(other.filters, filters) || other.filters == filters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_tables), stats, filters);

  /// Create a copy of AllTablesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AllTablesResponseImplCopyWith<_$AllTablesResponseImpl> get copyWith =>
      __$$AllTablesResponseImplCopyWithImpl<_$AllTablesResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AllTablesResponseImplToJson(
      this,
    );
  }
}

abstract class _AllTablesResponse implements AllTablesResponse {
  const factory _AllTablesResponse(
      {required final List<TableApiModel> tables,
      required final TableStats stats,
      final AllTablesFilters? filters}) = _$AllTablesResponseImpl;

  factory _AllTablesResponse.fromJson(Map<String, dynamic> json) =
      _$AllTablesResponseImpl.fromJson;

  @override
  List<TableApiModel> get tables;
  @override
  TableStats get stats;
  @override
  AllTablesFilters? get filters;

  /// Create a copy of AllTablesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AllTablesResponseImplCopyWith<_$AllTablesResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AllTablesFilters _$AllTablesFiltersFromJson(Map<String, dynamic> json) {
  return _AllTablesFilters.fromJson(json);
}

/// @nodoc
mixin _$AllTablesFilters {
  String get storeId => throw _privateConstructorUsedError;
  List<String>? get statusFilter => throw _privateConstructorUsedError;

  /// Serializes this AllTablesFilters to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AllTablesFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AllTablesFiltersCopyWith<AllTablesFilters> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AllTablesFiltersCopyWith<$Res> {
  factory $AllTablesFiltersCopyWith(
          AllTablesFilters value, $Res Function(AllTablesFilters) then) =
      _$AllTablesFiltersCopyWithImpl<$Res, AllTablesFilters>;
  @useResult
  $Res call({String storeId, List<String>? statusFilter});
}

/// @nodoc
class _$AllTablesFiltersCopyWithImpl<$Res, $Val extends AllTablesFilters>
    implements $AllTablesFiltersCopyWith<$Res> {
  _$AllTablesFiltersCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AllTablesFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storeId = null,
    Object? statusFilter = freezed,
  }) {
    return _then(_value.copyWith(
      storeId: null == storeId
          ? _value.storeId
          : storeId // ignore: cast_nullable_to_non_nullable
              as String,
      statusFilter: freezed == statusFilter
          ? _value.statusFilter
          : statusFilter // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AllTablesFiltersImplCopyWith<$Res>
    implements $AllTablesFiltersCopyWith<$Res> {
  factory _$$AllTablesFiltersImplCopyWith(_$AllTablesFiltersImpl value,
          $Res Function(_$AllTablesFiltersImpl) then) =
      __$$AllTablesFiltersImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String storeId, List<String>? statusFilter});
}

/// @nodoc
class __$$AllTablesFiltersImplCopyWithImpl<$Res>
    extends _$AllTablesFiltersCopyWithImpl<$Res, _$AllTablesFiltersImpl>
    implements _$$AllTablesFiltersImplCopyWith<$Res> {
  __$$AllTablesFiltersImplCopyWithImpl(_$AllTablesFiltersImpl _value,
      $Res Function(_$AllTablesFiltersImpl) _then)
      : super(_value, _then);

  /// Create a copy of AllTablesFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storeId = null,
    Object? statusFilter = freezed,
  }) {
    return _then(_$AllTablesFiltersImpl(
      storeId: null == storeId
          ? _value.storeId
          : storeId // ignore: cast_nullable_to_non_nullable
              as String,
      statusFilter: freezed == statusFilter
          ? _value._statusFilter
          : statusFilter // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AllTablesFiltersImpl implements _AllTablesFilters {
  const _$AllTablesFiltersImpl(
      {required this.storeId, final List<String>? statusFilter})
      : _statusFilter = statusFilter;

  factory _$AllTablesFiltersImpl.fromJson(Map<String, dynamic> json) =>
      _$$AllTablesFiltersImplFromJson(json);

  @override
  final String storeId;
  final List<String>? _statusFilter;
  @override
  List<String>? get statusFilter {
    final value = _statusFilter;
    if (value == null) return null;
    if (_statusFilter is EqualUnmodifiableListView) return _statusFilter;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AllTablesFilters(storeId: $storeId, statusFilter: $statusFilter)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllTablesFiltersImpl &&
            (identical(other.storeId, storeId) || other.storeId == storeId) &&
            const DeepCollectionEquality()
                .equals(other._statusFilter, _statusFilter));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, storeId, const DeepCollectionEquality().hash(_statusFilter));

  /// Create a copy of AllTablesFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AllTablesFiltersImplCopyWith<_$AllTablesFiltersImpl> get copyWith =>
      __$$AllTablesFiltersImplCopyWithImpl<_$AllTablesFiltersImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AllTablesFiltersImplToJson(
      this,
    );
  }
}

abstract class _AllTablesFilters implements AllTablesFilters {
  const factory _AllTablesFilters(
      {required final String storeId,
      final List<String>? statusFilter}) = _$AllTablesFiltersImpl;

  factory _AllTablesFilters.fromJson(Map<String, dynamic> json) =
      _$AllTablesFiltersImpl.fromJson;

  @override
  String get storeId;
  @override
  List<String>? get statusFilter;

  /// Create a copy of AllTablesFilters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AllTablesFiltersImplCopyWith<_$AllTablesFiltersImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
