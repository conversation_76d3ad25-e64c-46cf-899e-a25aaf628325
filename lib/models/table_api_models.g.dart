// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'table_api_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ApiResponseImpl _$$ApiResponseImplFromJson(Map<String, dynamic> json) =>
    _$ApiResponseImpl(
      error: json['error'] as bool,
      message: json['message'] as String,
      data: json['data'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$ApiResponseImplToJson(_$ApiResponseImpl instance) =>
    <String, dynamic>{
      'error': instance.error,
      'message': instance.message,
      'data': instance.data,
    };

_$TableApiModelImpl _$$TableApiModelImplFromJson(Map<String, dynamic> json) =>
    _$TableApiModelImpl(
      tableId: json['tableId'] as String,
      name: json['name'] as String,
      status: json['status'] as String,
      capacity: (json['capacity'] as num).toInt(),
      position:
          TablePosition.fromJson(json['position'] as Map<String, dynamic>),
      size: TableSize.fromJson(json['size'] as Map<String, dynamic>),
      shape: json['shape'] as String? ?? 'circle',
      note: json['note'] as String?,
      priority: (json['priority'] as num?)?.toInt() ?? 0,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      currentOrderId: json['currentOrderId'] as String?,
      currentCustomerName: json['currentCustomerName'] as String?,
      currentCustomerPhone: json['currentCustomerPhone'] as String?,
      reservedAt: (json['reservedAt'] as num?)?.toInt(),
      estimatedDuration: (json['estimatedDuration'] as num?)?.toInt(),
      areaId: json['areaId'] as String,
      areaName: json['areaName'] as String,
      areaDescription: json['areaDescription'] as String?,
      areaDisplayOrder: (json['areaDisplayOrder'] as num?)?.toInt() ?? 0,
      lastUpdated: (json['lastUpdated'] as num?)?.toInt(),
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$$TableApiModelImplToJson(_$TableApiModelImpl instance) =>
    <String, dynamic>{
      'tableId': instance.tableId,
      'name': instance.name,
      'status': instance.status,
      'capacity': instance.capacity,
      'position': instance.position,
      'size': instance.size,
      'shape': instance.shape,
      'note': instance.note,
      'priority': instance.priority,
      'tags': instance.tags,
      'currentOrderId': instance.currentOrderId,
      'currentCustomerName': instance.currentCustomerName,
      'currentCustomerPhone': instance.currentCustomerPhone,
      'reservedAt': instance.reservedAt,
      'estimatedDuration': instance.estimatedDuration,
      'areaId': instance.areaId,
      'areaName': instance.areaName,
      'areaDescription': instance.areaDescription,
      'areaDisplayOrder': instance.areaDisplayOrder,
      'lastUpdated': instance.lastUpdated,
      'isActive': instance.isActive,
    };

_$TablePositionImpl _$$TablePositionImplFromJson(Map<String, dynamic> json) =>
    _$TablePositionImpl(
      x: (json['x'] as num?)?.toDouble() ?? 0,
      y: (json['y'] as num?)?.toDouble() ?? 0,
    );

Map<String, dynamic> _$$TablePositionImplToJson(_$TablePositionImpl instance) =>
    <String, dynamic>{
      'x': instance.x,
      'y': instance.y,
    };

_$TableSizeImpl _$$TableSizeImplFromJson(Map<String, dynamic> json) =>
    _$TableSizeImpl(
      width: (json['width'] as num?)?.toDouble() ?? 60,
      height: (json['height'] as num?)?.toDouble() ?? 60,
    );

Map<String, dynamic> _$$TableSizeImplToJson(_$TableSizeImpl instance) =>
    <String, dynamic>{
      'width': instance.width,
      'height': instance.height,
    };

_$TableStatsImpl _$$TableStatsImplFromJson(Map<String, dynamic> json) =>
    _$TableStatsImpl(
      total: (json['total'] as num?)?.toInt() ?? 0,
      available: (json['available'] as num?)?.toInt() ?? 0,
      occupied: (json['occupied'] as num?)?.toInt() ?? 0,
      reserved: (json['reserved'] as num?)?.toInt() ?? 0,
      disabled: (json['disabled'] as num?)?.toInt() ?? 0,
      totalCapacity: (json['totalCapacity'] as num?)?.toInt() ?? 0,
      areas: (json['areas'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$TableStatsImplToJson(_$TableStatsImpl instance) =>
    <String, dynamic>{
      'total': instance.total,
      'available': instance.available,
      'occupied': instance.occupied,
      'reserved': instance.reserved,
      'disabled': instance.disabled,
      'totalCapacity': instance.totalCapacity,
      'areas': instance.areas,
    };

_$AllTablesResponseImpl _$$AllTablesResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$AllTablesResponseImpl(
      tables: (json['tables'] as List<dynamic>)
          .map((e) => TableApiModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      stats: TableStats.fromJson(json['stats'] as Map<String, dynamic>),
      filters: json['filters'] == null
          ? null
          : AllTablesFilters.fromJson(json['filters'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AllTablesResponseImplToJson(
        _$AllTablesResponseImpl instance) =>
    <String, dynamic>{
      'tables': instance.tables,
      'stats': instance.stats,
      'filters': instance.filters,
    };

_$AllTablesFiltersImpl _$$AllTablesFiltersImplFromJson(
        Map<String, dynamic> json) =>
    _$AllTablesFiltersImpl(
      storeId: json['storeId'] as String,
      statusFilter: (json['statusFilter'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$AllTablesFiltersImplToJson(
        _$AllTablesFiltersImpl instance) =>
    <String, dynamic>{
      'storeId': instance.storeId,
      'statusFilter': instance.statusFilter,
    };
