import 'package:flutter/material.dart';
import '../core/theme/pos_colors.dart';
import '../core/theme/pos_typography.dart';
import '../core/theme/pos_spacing.dart';

/// Enum định ngh<PERSON>a c<PERSON>c loại order
enum OrderType {
  dineIn('Tại chỗ', Icons.store, Color(0xFF27C7FF)),
  takeaway('<PERSON>g về', Icons.shopping_bag, Color(0xFF4CAF50)),
  delivery('Giao hàng', Icons.delivery_dining, Color(0xFFFF9800));

  const OrderType(this.label, this.icon, this.color);

  final String label;
  final IconData icon;
  final Color color;
}

/// Modal chọn loại order
class OrderTypeSelectionModal extends StatefulWidget {
  final OrderType? selectedOrderType;
  final Function(OrderType) onOrderTypeSelected;

  const OrderTypeSelectionModal({
    Key? key,
    this.selectedOrderType,
    required this.onOrderTypeSelected,
  }) : super(key: key);

  @override
  State<OrderTypeSelectionModal> createState() =>
      _OrderTypeSelectionModalState();
}

class _OrderTypeSelectionModalState extends State<OrderTypeSelectionModal> {
  OrderType? _selectedType;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedOrderType ?? OrderType.dineIn;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(PosSpacing.lg),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.restaurant_menu,
                  color: PosColors.primary,
                  size: 24,
                ),
                const SizedBox(width: PosSpacing.sm),
                Text(
                  'Chọn loại order',
                  style: PosTypography.headingMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: PosColors.primary,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  color: PosColors.textSecondary,
                ),
              ],
            ),

            const SizedBox(height: PosSpacing.lg),

            // Order type options
            ...OrderType.values
                .map((orderType) => _buildOrderTypeOption(orderType)),

            const SizedBox(height: PosSpacing.lg),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding:
                          const EdgeInsets.symmetric(vertical: PosSpacing.md),
                      side: BorderSide(color: PosColors.borderLight),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Hủy',
                      style: PosTypography.bodyMedium.copyWith(
                        color: PosColors.textSecondary,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: PosSpacing.md),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _selectedType != null
                        ? () {
                            debugPrint(
                                "[Modal] Confirm button pressed. Selected type: $_selectedType");
                            // Lỗi nằm ở đây: Cả 2 dòng dưới đây đều thực hiện pop.
                            // Dòng 1: widget.onOrderTypeSelected gọi hàm ở do_uong_page, hàm này sẽ pop dialog.
                            // Dòng 2: Navigator.of(context).pop cũng pop dialog.
                            // -> Sửa lỗi bằng cách chỉ gọi onOrderTypeSelected, vì nó đã chứa logic pop.
                            widget.onOrderTypeSelected(_selectedType!);
                          }
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: PosColors.primary,
                      padding:
                          const EdgeInsets.symmetric(vertical: PosSpacing.md),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Xác nhận',
                      style: PosTypography.bodyMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderTypeOption(OrderType orderType) {
    final isSelected = _selectedType == orderType;

    return Container(
      margin: const EdgeInsets.only(bottom: PosSpacing.sm),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedType = orderType;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(PosSpacing.md),
          decoration: BoxDecoration(
            color: isSelected
                ? orderType.color.withValues(alpha: 0.1)
                : PosColors.surface,
            border: Border.all(
              color: isSelected ? orderType.color : PosColors.borderLight,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              // Icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isSelected ? orderType.color : PosColors.borderLight,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  orderType.icon,
                  color: isSelected ? Colors.white : PosColors.textSecondary,
                  size: 24,
                ),
              ),

              const SizedBox(width: PosSpacing.md),

              // Label and description
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      orderType.label,
                      style: PosTypography.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected
                            ? orderType.color
                            : PosColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _getOrderTypeDescription(orderType),
                      style: PosTypography.bodySmall.copyWith(
                        color: isSelected
                            ? orderType.color
                            : PosColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              // Selection indicator
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: orderType.color,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  String _getOrderTypeDescription(OrderType orderType) {
    switch (orderType) {
      case OrderType.dineIn:
        return 'Khách hàng dùng bữa tại nhà hàng';
      case OrderType.takeaway:
        return 'Khách hàng mang về nhà';
      case OrderType.delivery:
        return 'Giao hàng tận nơi';
    }
  }
}
