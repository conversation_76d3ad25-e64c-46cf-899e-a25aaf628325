import 'package:flutter/material.dart';
import '../models/pos_models.dart';
import '../core/theme/pos_spacing.dart';
import '../core/theme/pos_colors.dart';
import '../core/theme/pos_typography.dart';
import 'product_card.dart';

/// Product Grid Layout Component dựa trên thiết k<PERSON>
/// Hi<PERSON>n thị danh sách sản phẩm dạng lưới 3 cột
class ProductGrid extends StatelessWidget {
  final List<Product> products;
  final Function(Product)? onProductTap;
  final Function(Product)? onProductEdit;
  final Function(Product)? onProductDelete;
  final bool showActions;
  final bool isLoading;
  final String? selectedProductId;
  final ScrollController? scrollController;

  const ProductGrid({
    Key? key,
    required this.products,
    this.onProductTap,
    this.onProductEdit,
    this.onProductDelete,
    this.showActions = false,
    this.isLoading = false,
    this.selectedProductId,
    this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingGrid();
    }

    if (products.isEmpty) {
      return _buildEmptyState();
    }

    return _buildProductGrid();
  }

  Widget _buildProductGrid() {
    return Padding(
      padding: const EdgeInsets.all(PosSpacing.gridPadding),
      child: GridView.builder(
        controller: scrollController,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4, // Thay đổi từ 3 thành 4 cột
          crossAxisSpacing: PosSpacing.gridSpacing,
          mainAxisSpacing: PosSpacing.gridSpacing,
          childAspectRatio:
              1.27, // Tăng tỷ lệ để phù hợp với card nhỏ hơn (0.85 * 3/2)
        ),
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return ProductCard(
            product: product,
            onTap: onProductTap != null ? () => onProductTap!(product) : null,
            onEdit:
                onProductEdit != null ? () => onProductEdit!(product) : null,
            onDelete: onProductDelete != null
                ? () => onProductDelete!(product)
                : null,
            showActions: showActions,
            isSelected: selectedProductId == product.id,
          );
        },
      ),
    );
  }

  Widget _buildLoadingGrid() {
    return Padding(
      padding: const EdgeInsets.all(PosSpacing.gridPadding),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4, // Thay đổi từ 3 thành 4 cột
          crossAxisSpacing: PosSpacing.gridSpacing,
          mainAxisSpacing: PosSpacing.gridSpacing,
          childAspectRatio: 1.27, // Tăng tỷ lệ để phù hợp với card nhỏ hơn
        ),
        itemCount: 9, // Hiển thị 9 skeleton cards
        itemBuilder: (context, index) => _buildSkeletonCard(),
      ),
    );
  }

  Widget _buildSkeletonCard() {
    return Container(
      decoration: BoxDecoration(
        color: PosColors.surfaceVariant,
        borderRadius: BorderRadius.circular(PosSpacing.cardRadius),
        border: Border.all(color: PosColors.border),
      ),
      child: Column(
        children: [
          // Image skeleton
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                color: PosColors.borderLight,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(PosSpacing.cardRadius),
                ),
              ),
              child: const Center(
                child: CircularProgressIndicator(
                  color: PosColors.primary,
                  strokeWidth: 2,
                ),
              ),
            ),
          ),

          // Text skeleton
          Expanded(
            flex: 1,
            child: Padding(
              padding: const EdgeInsets.all(PosSpacing.sm),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 16,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: PosColors.borderLight,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: PosSpacing.xs),
                  Container(
                    height: 14,
                    width: 80,
                    decoration: BoxDecoration(
                      color: PosColors.borderLight,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(PosSpacing.contentPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: PosColors.surfaceVariant,
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.inventory_2_outlined,
                size: 60,
                color: PosColors.textTertiary,
              ),
            ),
            const SizedBox(height: PosSpacing.lg),
            const Text(
              'Không có sản phẩm',
              style: PosTypography.headingMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: PosSpacing.sm),
            Text(
              'Hiện tại chưa có sản phẩm nào trong danh mục này.\nVui lòng thêm sản phẩm mới hoặc chọn danh mục khác.',
              style: PosTypography.bodyMedium.copyWith(
                color: PosColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: PosSpacing.xl),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to add product screen
              },
              icon: const Icon(Icons.add),
              label: const Text('Thêm sản phẩm'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Responsive Product Grid cho các màn hình khác nhau
class ResponsiveProductGrid extends StatelessWidget {
  final List<Product> products;
  final Function(Product)? onProductTap;
  final Function(Product)? onProductEdit;
  final Function(Product)? onProductDelete;
  final bool showActions;
  final bool isLoading;
  final String? selectedProductId;
  final ScrollController? scrollController;

  const ResponsiveProductGrid({
    Key? key,
    required this.products,
    this.onProductTap,
    this.onProductEdit,
    this.onProductDelete,
    this.showActions = false,
    this.isLoading = false,
    this.selectedProductId,
    this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        int crossAxisCount;
        double childAspectRatio;

        // Responsive grid based on screen width - cập nhật cho card nhỏ hơn
        if (constraints.maxWidth > 1200) {
          crossAxisCount = 6; // Desktop large - tăng từ 5 lên 6
          childAspectRatio = 1.35; // Tăng tỷ lệ cho card nhỏ hơn
        } else if (constraints.maxWidth > 800) {
          crossAxisCount = 5; // Desktop/Tablet - tăng từ 4 lên 5
          childAspectRatio = 1.27; // Tăng tỷ lệ cho card nhỏ hơn
        } else if (constraints.maxWidth > 600) {
          crossAxisCount = 4; // Tablet portrait - tăng từ 3 lên 4
          childAspectRatio = 1.2; // Tăng tỷ lệ cho card nhỏ hơn
        } else {
          crossAxisCount = 3; // Mobile - tăng từ 2 lên 3
          childAspectRatio = 1.12; // Tăng tỷ lệ cho card nhỏ hơn
        }

        if (isLoading) {
          return _buildLoadingGrid(crossAxisCount, childAspectRatio);
        }

        if (products.isEmpty) {
          return _buildEmptyState();
        }

        return Padding(
          padding: const EdgeInsets.all(PosSpacing.gridPadding),
          child: GridView.builder(
            controller: scrollController,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: PosSpacing.gridSpacing,
              mainAxisSpacing: PosSpacing.gridSpacing,
              childAspectRatio: childAspectRatio,
            ),
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              return ProductCard(
                product: product,
                onTap:
                    onProductTap != null ? () => onProductTap!(product) : null,
                onEdit: onProductEdit != null
                    ? () => onProductEdit!(product)
                    : null,
                onDelete: onProductDelete != null
                    ? () => onProductDelete!(product)
                    : null,
                showActions: showActions,
                isSelected: selectedProductId == product.id,
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildLoadingGrid(int crossAxisCount, double childAspectRatio) {
    return Padding(
      padding: const EdgeInsets.all(PosSpacing.gridPadding),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: PosSpacing.gridSpacing,
          mainAxisSpacing: PosSpacing.gridSpacing,
          childAspectRatio: childAspectRatio,
        ),
        itemCount: crossAxisCount * 3, // 3 rows of skeleton
        itemBuilder: (context, index) => _buildSkeletonCard(),
      ),
    );
  }

  Widget _buildSkeletonCard() {
    return Container(
      decoration: BoxDecoration(
        color: PosColors.surfaceVariant,
        borderRadius: BorderRadius.circular(PosSpacing.cardRadius),
        border: Border.all(color: PosColors.border),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: PosColors.primary,
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: PosColors.textTertiary,
          ),
          SizedBox(height: PosSpacing.lg),
          Text(
            'Không có sản phẩm',
            style: PosTypography.headingMedium,
          ),
        ],
      ),
    );
  }
}
