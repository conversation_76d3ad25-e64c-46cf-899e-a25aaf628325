import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../services/printer_service.dart';
import '../widgets/bluetooth_test_widget.dart';

class BluetoothTestPage extends StatefulWidget {
  const BluetoothTestPage({Key? key}) : super(key: key);

  @override
  State<BluetoothTestPage> createState() => _BluetoothTestPageState();
}

class _BluetoothTestPageState extends State<BluetoothTestPage> {
  final PrinterService _printerService = PrinterService();
  List<BluetoothDevice> _availableDevices = [];
  BluetoothDevice? _selectedDevice;
  bool _isScanning = false;

  @override
  void initState() {
    super.initState();
    _scanForDevices();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bluetooth Library Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _isScanning ? null : _scanForDevices,
            icon: _isScanning 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Icon(Icons.refresh),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Device Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.bluetooth, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'Chọn Thiết Bị Test',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    if (_availableDevices.isEmpty && !_isScanning)
                      const Text(
                        'Không tìm thấy thiết bị nào. Nhấn nút refresh để scan lại.',
                        style: TextStyle(color: Colors.grey),
                      )
                    else if (_isScanning)
                      const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Đang scan thiết bị...'),
                        ],
                      )
                    else
                      Column(
                        children: _availableDevices.map((device) {
                          final isSelected = _selectedDevice?.remoteId == device.remoteId;
                          return Card(
                            color: isSelected ? Colors.blue[50] : null,
                            child: ListTile(
                              leading: Icon(
                                Icons.print,
                                color: isSelected ? Colors.blue : Colors.grey,
                              ),
                              title: Text(
                                device.platformName.isNotEmpty 
                                    ? device.platformName 
                                    : 'Unknown Device',
                                style: TextStyle(
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                ),
                              ),
                              subtitle: Text(device.remoteId.toString()),
                              trailing: isSelected 
                                  ? const Icon(Icons.check_circle, color: Colors.blue)
                                  : null,
                              onTap: () {
                                setState(() {
                                  _selectedDevice = device;
                                });
                              },
                            ),
                          );
                        }).toList(),
                      ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Test Widget
            if (_selectedDevice != null)
              BluetoothTestWidget(
                targetMacAddress: _selectedDevice!.remoteId.toString(),
              )
            else
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.bluetooth_disabled,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Vui lòng chọn thiết bị để bắt đầu test',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _scanForDevices() async {
    setState(() {
      _isScanning = true;
    });

    try {
      final devices = await _printerService.scanForPrinters();
      setState(() {
        _availableDevices = devices;
        // Auto-select first device if available
        if (devices.isNotEmpty && _selectedDevice == null) {
          _selectedDevice = devices.first;
        }
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi khi scan thiết bị: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isScanning = false;
      });
    }
  }
}


