import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/receipt_models.dart';
import 'bluetooth_test_service.dart';

class PrinterService {
  static final PrinterService _instance = PrinterService._internal();
  factory PrinterService() => _instance;
  PrinterService._internal();

  // Trạng thái kết nối
  bool _isConnected = false;
  BluetoothDevice? _connectedDevice;
  BluetoothCharacteristic? _writeCharacteristic;
  String _connectedPrinterName = '';
  String _connectedPrinterAddress = '';

  // Getters
  bool get isConnected => _isConnected;
  String get connectedPrinterName => _connectedPrinterName;
  String get connectedPrinterAddress => _connectedPrinterAddress;

  /// Yêu cầu quyền Bluetooth
  Future<bool> requestBluetoothPermissions() async {
    try {
      debugPrint('🔍 [PRINTER] Bắt đầu yêu cầu quyền Bluetooth...');

      // Danh sách quyền cần thiết
      Map<Permission, PermissionStatus> permissions = await [
        Permission.location,
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
      ].request();

      debugPrint('🔍 [PRINTER] Trạng thái quyền hiện tại:');
      permissions.forEach((permission, status) {
        debugPrint('  - ${permission.toString().split('.').last}: $status');
      });

      debugPrint('🔍 [PRINTER] Kết quả yêu cầu quyền: $permissions');

      bool allGranted = permissions.values.every((status) =>
        status == PermissionStatus.granted);

      if (allGranted) {
        debugPrint('✅ [PRINTER] Tất cả quyền Bluetooth đã được cấp');
        return true;
      } else {
        debugPrint('❌ [PRINTER] Một số quyền Bluetooth chưa được cấp');
        return false;
      }
    } catch (e) {
      debugPrint('🔴 [PRINTER] Lỗi khi yêu cầu quyền Bluetooth: $e');
      return false;
    }
  }

  /// Scan thiết bị Bluetooth
  Future<List<BluetoothDevice>> scanForPrinters() async {
    try {
      debugPrint('🔍 [PRINTER] Bắt đầu scan thiết bị Bluetooth...');

      // Yêu cầu quyền
      final hasPermissions = await requestBluetoothPermissions();
      if (!hasPermissions) {
        throw Exception('Chưa có quyền Bluetooth');
      }

      // Kiểm tra Bluetooth có bật không
      final isSupported = await FlutterBluePlus.isSupported;
      if (!isSupported) {
        throw Exception('Thiết bị không hỗ trợ Bluetooth');
      }

      final adapterState = await FlutterBluePlus.adapterState.first;
      if (adapterState != BluetoothAdapterState.on) {
        throw Exception('Bluetooth chưa được bật');
      }

      // Lấy thiết bị đã ghép nối
      debugPrint('🔍 [PRINTER] Đang lấy danh sách thiết bị đã ghép nối...');
      final bondedDevices = await FlutterBluePlus.bondedDevices;

      debugPrint('🔍 [PRINTER] Tìm thấy ${bondedDevices.length} thiết bị đã ghép nối:');
      for (int i = 0; i < bondedDevices.length; i++) {
        final device = bondedDevices[i];
        debugPrint('  [$i] Name: "${device.platformName}" | MAC: ${device.remoteId}');
      }

      if (bondedDevices.isEmpty) {
        debugPrint('⚠️ [PRINTER] Không tìm thấy thiết bị Bluetooth nào đã ghép nối');
        debugPrint('💡 [PRINTER] Hướng dẫn: Vào Settings → Bluetooth và ghép nối với máy in A70pro trước');
      }

      return bondedDevices;
    } catch (e) {
      debugPrint('🔴 [PRINTER] Lỗi khi scan thiết bị: $e');
      return [];
    }
  }

  /// Kết nối với máy in
  Future<bool> connectToPrinter(BluetoothDevice device) async {
    const maxRetries = 3;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('🔗 [PRINTER] Bắt đầu kết nối với máy in (lần thử $attempt/$maxRetries)...');
        debugPrint('🔗 [PRINTER] Tên máy in: "${device.platformName}"');
        debugPrint('🔗 [PRINTER] MAC Address: ${device.remoteId}');

        // Ngắt kết nối cũ nếu có
        if (_isConnected && _connectedDevice != null) {
          debugPrint('🔄 [PRINTER] Ngắt kết nối cũ...');
          await _connectedDevice!.disconnect();
          await Future.delayed(const Duration(milliseconds: 500));
        }

        // Kiểm tra trạng thái hiện tại của thiết bị
        final currentState = await device.connectionState.first;
        debugPrint('🔍 [PRINTER] Trạng thái hiện tại: $currentState');

        if (currentState == BluetoothConnectionState.connected) {
          debugPrint('✅ [PRINTER] Thiết bị đã kết nối sẵn!');
        } else {
          // Kết nối với thiết bị - tăng timeout lên 45s
          debugPrint('🔗 [PRINTER] Đang kết nối... (timeout: 45s)');
          await device.connect(
            timeout: const Duration(seconds: 45),
            autoConnect: false, // Kết nối trực tiếp, không dùng auto-connect
          );

          // Đợi một chút để kết nối ổn định
          await Future.delayed(const Duration(milliseconds: 1000));
        }

        // Kiểm tra trạng thái kết nối sau khi connect
        final connectionState = await device.connectionState.first;
        if (connectionState != BluetoothConnectionState.connected) {
          debugPrint('❌ [PRINTER] Kết nối thất bại - trạng thái: $connectionState');
          if (attempt < maxRetries) {
            debugPrint('🔄 [PRINTER] Thử lại sau 5 giây...');
            await Future.delayed(const Duration(seconds: 5));
            continue;
          }
          return false;
        }

        debugPrint('✅ [PRINTER] Kết nối thành công!');

        // Discover services với timeout
        debugPrint('🔍 [PRINTER] Đang tìm services...');
        final services = await device.discoverServices().timeout(
          const Duration(seconds: 15),
          onTimeout: () {
            debugPrint('⏰ [PRINTER] Timeout khi discover services');
            throw Exception('Timeout discovering services');
          },
        );

        debugPrint('🔍 [PRINTER] Tìm thấy ${services.length} services');

        // Tìm characteristic để ghi dữ liệu
        BluetoothCharacteristic? writeChar;
        for (final service in services) {
          debugPrint('🔍 [PRINTER] Service: ${service.uuid}');
          for (final characteristic in service.characteristics) {
            debugPrint('  - Characteristic: ${characteristic.uuid}');
            debugPrint('    Properties: write=${characteristic.properties.write}, writeWithoutResponse=${characteristic.properties.writeWithoutResponse}');

            if (characteristic.properties.write || characteristic.properties.writeWithoutResponse) {
              writeChar = characteristic;
              debugPrint('✅ [PRINTER] Tìm thấy write characteristic: ${characteristic.uuid}');
              break;
            }
          }
          if (writeChar != null) break;
        }

        if (writeChar == null) {
          debugPrint('❌ [PRINTER] Không tìm thấy write characteristic');
          await device.disconnect();
          if (attempt < maxRetries) {
            debugPrint('🔄 [PRINTER] Thử lại sau 5 giây...');
            await Future.delayed(const Duration(seconds: 5));
            continue;
          }
          return false;
        }

        // Lưu thông tin kết nối
        _isConnected = true;
        _connectedDevice = device;
        _writeCharacteristic = writeChar;
        _connectedPrinterName = device.platformName;
        _connectedPrinterAddress = device.remoteId.toString();

        debugPrint('✅ [PRINTER] Máy in đã kết nối: $_connectedPrinterName');
        debugPrint('✅ [PRINTER] Địa chỉ MAC: $_connectedPrinterAddress');
        debugPrint('✅ [PRINTER] Write characteristic: ${writeChar.uuid}');

        return true;
      } catch (e) {
        debugPrint('🔴 [PRINTER] Lỗi kết nối (lần thử $attempt/$maxRetries): $e');

        // Thử ngắt kết nối nếu có lỗi
        try {
          await device.disconnect();
        } catch (disconnectError) {
          debugPrint('⚠️ [PRINTER] Lỗi khi ngắt kết nối: $disconnectError');
        }

        if (attempt < maxRetries) {
          final delaySeconds = attempt * 2 + 3; // 5s, 7s, 9s
          debugPrint('🔄 [PRINTER] Thử lại sau $delaySeconds giây...');
          await Future.delayed(Duration(seconds: delaySeconds));
        }
      }
    }

    debugPrint('❌ [PRINTER] Kết nối thất bại sau $maxRetries lần thử');
    return false;
  }

  /// Ngắt kết nối
  Future<void> disconnect() async {
    try {
      if (_connectedDevice != null) {
        await _connectedDevice!.disconnect();
      }
      _isConnected = false;
      _connectedDevice = null;
      _writeCharacteristic = null;
      _connectedPrinterName = '';
      _connectedPrinterAddress = '';
      debugPrint('✅ [PRINTER] Đã ngắt kết nối máy in');
    } catch (e) {
      debugPrint('🔴 [PRINTER] Lỗi khi ngắt kết nối: $e');
    }
  }

  /// Chạy test so sánh các thư viện Bluetooth
  Future<Map<String, TestResult>> runBluetoothLibraryTest() async {
    debugPrint('🧪 [PRINTER] Bắt đầu test các thư viện Bluetooth...');

    if (_connectedPrinterAddress?.isEmpty ?? true) {
      // Thử lấy từ danh sách thiết bị đã scan
      final devices = await scanForPrinters();
      if (devices.isEmpty) {
        throw Exception('Không tìm thấy máy in nào để test');
      }
      // Sử dụng máy in đầu tiên trong danh sách
      final targetMac = devices.first.remoteId.toString();
      return await BluetoothTestService().runAllTests(targetMac);
    }

    return await BluetoothTestService().runAllTests(_connectedPrinterAddress!);
  }

  /// Lấy thư viện Bluetooth tốt nhất
  Future<String?> getBestBluetoothLibrary() async {
    try {
      await runBluetoothLibraryTest();
      return BluetoothTestService().getBestLibrary();
    } catch (e) {
      debugPrint('🔴 [PRINTER] Lỗi khi test thư viện: $e');
      return null;
    }
  }

  /// Test in
  Future<bool> testPrint() async {
    try {
      debugPrint('🖨️ [PRINTER] Bắt đầu test print...');

      if (!_isConnected || _writeCharacteristic == null) {
        throw Exception('Chưa kết nối đến máy in');
      }

      // Tạo nội dung test
      final testText = '''
       === TEST PRINT ===
        LÀNG NUÔI BIỂN VÂN ĐỒN
       171 Nguyễn Như Kon Tum
        Hotline: 0909998889

Máy in: $_connectedPrinterName
Thời gian: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year} ${DateTime.now().hour}:${DateTime.now().minute.toString().padLeft(2, '0')}

      TEST PRINT THÀNH CÔNG!
''';

      debugPrint('🖨️ [PRINTER] Nội dung test print:');
      debugPrint(testText);

      // Chuyển đổi text thành bytes
      final bytes = utf8.encode(testText);
      final data = Uint8List.fromList(bytes);

      // Gửi dữ liệu
      debugPrint('🖨️ [PRINTER] Đang gửi dữ liệu...');
      await _writeCharacteristic!.write(data, withoutResponse: true);

      // Thêm lệnh feed giấy
      final feedBytes = Uint8List.fromList([0x0A, 0x0A, 0x0A]);
      await _writeCharacteristic!.write(feedBytes, withoutResponse: true);

      debugPrint('✅ [PRINTER] Test print thành công!');
      return true;
    } catch (e) {
      debugPrint('🔴 [PRINTER] Lỗi test print: $e');
      return false;
    }
  }

  /// In hóa đơn thanh toán
  Future<bool> printPaymentReceipt(PaymentReceipt receiptData) async {
    try {
      debugPrint('🖨️ [PRINTER] Bắt đầu in hóa đơn thanh toán...');

      if (!_isConnected || _writeCharacteristic == null) {
        throw Exception('Chưa kết nối đến máy in');
      }

      // Tạo nội dung hóa đơn
      final receiptText = _buildPaymentReceiptText(receiptData);

      // Chuyển đổi thành bytes và gửi
      final bytes = utf8.encode(receiptText);
      final data = Uint8List.fromList(bytes);

      await _writeCharacteristic!.write(data, withoutResponse: true);

      // Feed giấy
      final feedBytes = Uint8List.fromList([0x0A, 0x0A, 0x0A]);
      await _writeCharacteristic!.write(feedBytes, withoutResponse: true);

      debugPrint('✅ [PRINTER] In hóa đơn thanh toán thành công!');
      return true;
    } catch (e) {
      debugPrint('🔴 [PRINTER] Lỗi in hóa đơn thanh toán: $e');
      return false;
    }
  }

  /// In tem bếp
  Future<bool> printKitchenReceipt(KitchenReceipt receiptData) async {
    try {
      debugPrint('🖨️ [PRINTER] Bắt đầu in tem bếp...');

      if (!_isConnected || _writeCharacteristic == null) {
        throw Exception('Chưa kết nối đến máy in');
      }

      // Tạo nội dung tem bếp
      final receiptText = _buildKitchenReceiptText(receiptData);

      // Chuyển đổi thành bytes và gửi
      final bytes = utf8.encode(receiptText);
      final data = Uint8List.fromList(bytes);

      await _writeCharacteristic!.write(data, withoutResponse: true);

      // Feed giấy
      final feedBytes = Uint8List.fromList([0x0A, 0x0A, 0x0A]);
      await _writeCharacteristic!.write(feedBytes, withoutResponse: true);

      debugPrint('✅ [PRINTER] In tem bếp thành công!');
      return true;
    } catch (e) {
      debugPrint('🔴 [PRINTER] Lỗi in tem bếp: $e');
      return false;
    }
  }

  /// Tạo nội dung hóa đơn thanh toán
  String _buildPaymentReceiptText(PaymentReceipt receiptData) {
    final buffer = StringBuffer();

    // Header
    buffer.writeln('================================');
    buffer.writeln('        HÓA ĐƠN THANH TOÁN');
    buffer.writeln('================================');
    buffer.writeln('Mã đơn: ${receiptData.orderCode}');
    buffer.writeln('Bàn số: ${receiptData.tableNumber}');
    buffer.writeln('Thời gian: ${receiptData.orderTime}');
    buffer.writeln('Thu ngân: ${receiptData.cashierName ?? 'N/A'}');
    buffer.writeln('================================');

    // Items
    for (final item in receiptData.items) {
      buffer.writeln(item.name);
      buffer.writeln('${item.quantity} x ${item.unitPrice} = ${item.totalPrice}');
      buffer.writeln('--------------------------------');
    }

    // Total
    buffer.writeln('Tạm tính: ${receiptData.subtotal}');
    buffer.writeln('Giảm giá: ${receiptData.discount}');
    buffer.writeln('TỔNG CỘNG: ${receiptData.total}');
    buffer.writeln('Phương thức: ${receiptData.paymentMethod}');
    buffer.writeln('Tiền nhận: ${receiptData.receivedAmount}');
    buffer.writeln('Tiền thừa: ${receiptData.changeAmount}');
    buffer.writeln('================================');
    buffer.writeln('     CẢM ƠN QUÝ KHÁCH!');
    buffer.writeln('================================');

    return buffer.toString();
  }

  /// Tạo nội dung tem bếp
  String _buildKitchenReceiptText(KitchenReceipt receiptData) {
    final buffer = StringBuffer();

    // Header
    buffer.writeln('================================');
    buffer.writeln('           TEM BẾP');
    buffer.writeln('================================');
    buffer.writeln('Mã đơn: ${receiptData.orderCode}');
    buffer.writeln('Bàn số: ${receiptData.tableNumber}');
    buffer.writeln('Thời gian: ${receiptData.orderTime}');
    buffer.writeln('================================');

    // Items
    for (final item in receiptData.items) {
      buffer.writeln('${item.name} x${item.quantity}');
      if (item.notes?.isNotEmpty == true) {
        buffer.writeln('  Ghi chú: ${item.notes}');
      }
      buffer.writeln('--------------------------------');
    }

    // Notes
    if (receiptData.notes?.isNotEmpty == true) {
      buffer.writeln('Ghi chú đơn: ${receiptData.notes}');
      buffer.writeln('================================');
    }

    return buffer.toString();
  }

  /// Test in TSPL
  Future<bool> testPrintTSPL() async {
    try {
      debugPrint('🏷️ [PRINTER] Bắt đầu test TSPL...');
      if (!_isConnected || _writeCharacteristic == null) {
        throw Exception('Chưa kết nối đến máy in');
      }

      const tsplCommands = ''',
                SIZE 80 mm, 60 mm
                GAP 3 mm, 0 mm
                DIRECTION 1
                REFERENCE 0,0
                OFFSET 0 mm
                SET PEEL OFF
                SET CUTTER OFF
                SET PARTIAL_CUTTER OFF
                SET TEAR ON
                CLS
                CODEPAGE 1252
                TEXT 50,50,"3",0,1,1,"TSPL TEST"
                TEXT 50,100,"3",0,1,1,"Hello from Flutter!"
                TEXT 50,150,"3",0,1,1,"Xin chao!"
                PRINT 1,1

            ''';

      final bytes = utf8.encode(tsplCommands);
      final data = Uint8List.fromList(bytes);

      await _writeCharacteristic!.write(data, withoutResponse: true);
      debugPrint('✅ [PRINTER] Gửi lệnh TSPL thành công!');
      return true;
    } catch (e) {
      debugPrint('🔴 [PRINTER] Lỗi test TSPL: $e');
      return false;
    }
  }

  /// Test in CPCL
  Future<bool> testPrintCPCL() async {
    try {
      debugPrint('🖨️ [PRINTER] Bắt đầu test CPCL...');
      if (!_isConnected || _writeCharacteristic == null) {
        throw Exception('Chưa kết nối đến máy in');
      }

      const cpclCommands = ''',
                ! 0 200 200 210 1
                PAGE-WIDTH 576
                TEXT 4 0 30 40 CPCL TEST
                TEXT 4 0 30 80 Hello from Flutter!
                TEXT 4 0 30 120 Xin chao!
                PRINT

            ''';

      final bytes = utf8.encode(cpclCommands);
      final data = Uint8List.fromList(bytes);

      await _writeCharacteristic!.write(data, withoutResponse: true);
      debugPrint('✅ [PRINTER] Gửi lệnh CPCL thành công!');
      return true;
    } catch (e) {
      debugPrint('🔴 [PRINTER] Lỗi test CPCL: $e');
      return false;
    }
  }

}
