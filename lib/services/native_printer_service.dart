import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/bluetooth_device_simple.dart';

class NativePrinterService {
  Future<List<BluetoothDeviceSimple>> getBondedDevices() async {
    try {
      final List<dynamic> devices = await _channel.invokeMethod('getBondedDevices');
      return devices.map((d) => BluetoothDeviceSimple(name: d['name'], address: d['address'])).toList();
    } on PlatformException catch (e) {
      throw Exception('Failed to get bonded devices: ${e.message}');
    }
  }
  static const _channel = MethodChannel('com.house.tgcorp.com.vn/spp_printer');

  Future<void> connect(String macAddress) async {
    try {
      await _channel.invokeMethod('connect', {'macAddress': macAddress});
    } on PlatformException catch (e) {
      throw Exception('Failed to connect: ${e.message}');
    }
  }

  Future<void> disconnect() async {
    try {
      await _channel.invokeMethod('disconnect');
    } on PlatformException catch (e) {
      throw Exception('Failed to disconnect: ${e.message}');
    }
  }

  Future<void> _printRaw(Uint8List data) async {
    try {
      await _channel.invokeMethod('print', {'data': data});
    } on PlatformException catch (e) {
      throw Exception('Failed to print: ${e.message}');
    }
  }

  Future<void> testPrintTSPL() async {
    const tsplCommands = ''',
              SIZE 80 mm, 60 mm
              GAP 3 mm, 0 mm
              DIRECTION 1
              REFERENCE 0,0
              OFFSET 0 mm
              SET PEEL OFF
              SET CUTTER OFF
              SET PARTIAL_CUTTER OFF
              SET TEAR ON
              CLS
              CODEPAGE 1252
              TEXT 50,50,"3",0,1,1,"TSPL NATIVE TEST"
              TEXT 50,100,"3",0,1,1,"Hello from Flutter!"
              TEXT 50,150,"3",0,1,1,"Xin chao!"
              PRINT 1,1

          ''';
    final bytes = utf8.encode(tsplCommands);
    await _printRaw(Uint8List.fromList(bytes));
  }

  Future<void> testPrintCPCL() async {
    // A simpler, more universal CPCL command.
    // The initial command `! 0 200 200 210 1` defines the label height and quantity.
    // We add a newline `\r\n` at the end of each line for better compatibility.
    const cpclCommands =
        '! 0 200 200 240 1\r\n'
        'TEXT 4 0 30 50 Simple CPCL Test\r\n'
        'TEXT 4 0 30 100 Dong 2\r\n'
        'FORM\r\n'
        'PRINT\r\n';

    final bytes = utf8.encode(cpclCommands);
    await _printRaw(Uint8List.fromList(bytes));
  }

  Future<bool> isConnected() async {
    try {
      final bool? connected = await _channel.invokeMethod('isConnected');
      return connected ?? false;
    } on PlatformException catch (e) {
      throw 'Failed to check connection status: ${e.message}';
    }
  }
}

