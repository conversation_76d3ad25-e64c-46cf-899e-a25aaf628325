import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as fbp;
import 'package:permission_handler/permission_handler.dart';

/// Service để test Flutter Blue Plus với các kịch bản khác nhau
class BluetoothTestService {
  static final BluetoothTestService _instance = BluetoothTestService._internal();
  factory BluetoothTestService() => _instance;
  BluetoothTestService._internal();

  // Test results
  final Map<String, TestResult> _testResults = {};

  /// Chạy test tất cả các kịch bản Bluetooth
  Future<Map<String, TestResult>> runAllTests(String targetMacAddress) async {
    debugPrint('🧪 [BLUETOOTH_TEST] Bắt đầu test Flutter Blue Plus...');
    debugPrint('🎯 [BLUETOOTH_TEST] Target MAC: $targetMacAddress');

    _testResults.clear();

    // Test 1: Basic Connection Test
    await _testBasicConnection(targetMacAddress);

    // Test 2: Service Discovery Test
    await _testServiceDiscovery(targetMacAddress);

    // Test 3: Connection Stability Test
    await _testConnectionStability(targetMacAddress);

    // In kết quả
    _printTestResults();

    return Map.from(_testResults);
  }

  /// Test Basic Connection
  Future<void> _testBasicConnection(String targetMac) async {
    final testName = 'Basic Connection Test';
    debugPrint('🧪 [TEST] Bắt đầu test: $testName');
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Request permissions
      await _requestPermissions();

      // Get bonded devices
      final devices = await fbp.FlutterBluePlus.bondedDevices;
      final targetDevice = devices.firstWhere(
        (device) => device.remoteId.toString().toUpperCase() == targetMac.toUpperCase(),
        orElse: () => throw Exception('Device not found in bonded devices'),
      );

      // Try to connect with shorter timeout
      await targetDevice.connect(timeout: const Duration(seconds: 20));

      // Check connection state
      final state = await targetDevice.connectionState.first;
      final isConnected = state == fbp.BluetoothConnectionState.connected;

      if (isConnected) {
        // Disconnect immediately for basic test
        await targetDevice.disconnect();

        stopwatch.stop();

        _testResults[testName] = TestResult(
          success: true,
          connectionTime: stopwatch.elapsedMilliseconds,
          servicesFound: 1,
          error: null,
          details: 'Kết nối cơ bản thành công trong ${stopwatch.elapsedMilliseconds}ms',
        );
      } else {
        throw Exception('Connection failed - state: $state');
      }
      
    } catch (e) {
      stopwatch.stop();
      _testResults[testName] = TestResult(
        success: false,
        connectionTime: stopwatch.elapsedMilliseconds,
        servicesFound: 0,
        error: e.toString(),
        details: 'Basic connection failed',
      );
    }
  }

  /// Test Service Discovery
  Future<void> _testServiceDiscovery(String targetMac) async {
    final testName = 'Service Discovery Test';
    debugPrint('🧪 [TEST] Bắt đầu test: $testName');

    final stopwatch = Stopwatch()..start();

    try {
      // Get bonded devices
      final devices = await fbp.FlutterBluePlus.bondedDevices;
      final targetDevice = devices.firstWhere(
        (device) => device.remoteId.toString().toUpperCase() == targetMac.toUpperCase(),
        orElse: () => throw Exception('Device not found in bonded devices'),
      );

      // Connect
      await targetDevice.connect(timeout: const Duration(seconds: 25));

      // Discover services
      final services = await targetDevice.discoverServices();

      // Disconnect
      await targetDevice.disconnect();

      stopwatch.stop();

      _testResults[testName] = TestResult(
        success: true,
        connectionTime: stopwatch.elapsedMilliseconds,
        servicesFound: services.length,
        error: null,
        details: 'Tìm thấy ${services.length} services trong ${stopwatch.elapsedMilliseconds}ms',
      );

    } catch (e) {
      stopwatch.stop();
      _testResults[testName] = TestResult(
        success: false,
        connectionTime: stopwatch.elapsedMilliseconds,
        servicesFound: 0,
        error: e.toString(),
        details: 'Service discovery failed',
      );
    }
  }

  /// Test Connection Stability
  Future<void> _testConnectionStability(String targetMac) async {
    final testName = 'Connection Stability Test';
    debugPrint('🧪 [TEST] Bắt đầu test: $testName');

    final stopwatch = Stopwatch()..start();

    try {
      // Test connection stability with shorter timeout
      final devices = await fbp.FlutterBluePlus.bondedDevices;
      final targetDevice = devices.firstWhere(
        (device) => device.remoteId.toString().toUpperCase() == targetMac.toUpperCase(),
        orElse: () => throw Exception('Device not found'),
      );

      // Quick connection test with 15s timeout
      await targetDevice.connect(timeout: const Duration(seconds: 15));

      // Test if connection is stable
      final state = await targetDevice.connectionState.first;
      final isConnected = state == fbp.BluetoothConnectionState.connected;

      if (isConnected) {
        // Try to discover services to test stability
        final services = await targetDevice.discoverServices();
        await targetDevice.disconnect();

        stopwatch.stop();

        _testResults[testName] = TestResult(
          success: true,
          connectionTime: stopwatch.elapsedMilliseconds,
          servicesFound: services.length,
          error: null,
          details: 'Kết nối ổn định, tìm thấy ${services.length} services',
        );
      } else {
        throw Exception('Connection not stable');
      }

    } catch (e) {
      stopwatch.stop();
      _testResults[testName] = TestResult(
        success: false,
        connectionTime: stopwatch.elapsedMilliseconds,
        servicesFound: 0,
        error: e.toString(),
        details: 'Connection stability test failed',
      );
    }
  }

  /// Request permissions
  Future<void> _requestPermissions() async {
    final permissions = [
      Permission.location,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
    ];
    
    await permissions.request();
  }

  /// In kết quả test
  void _printTestResults() {
    debugPrint('\n📊 [BLUETOOTH_TEST] KẾT QUẢ TEST:');
    debugPrint('=' * 60);
    
    _testResults.forEach((testName, result) {
      final status = result.success ? '✅ THÀNH CÔNG' : '❌ THẤT BẠI';
      debugPrint('🧪 $testName: $status');
      debugPrint('   ⏱️  Thời gian kết nối: ${result.connectionTime}ms');
      debugPrint('   🔧 Services/Features: ${result.servicesFound}');
      debugPrint('   📝 Chi tiết: ${result.details}');
      if (result.error != null) {
        debugPrint('   ❌ Lỗi: ${result.error}');
      }
      debugPrint('-' * 40);
    });
    
    // Tổng kết
    final successCount = _testResults.values.where((r) => r.success).length;
    final totalCount = _testResults.length;
    debugPrint('📈 Tổng kết: $successCount/$totalCount thư viện hoạt động');
    
    // Recommend best library
    if (successCount > 0) {
      final bestResult = _testResults.entries
          .where((entry) => entry.value.success)
          .reduce((a, b) => a.value.connectionTime < b.value.connectionTime ? a : b);
      debugPrint('🏆 Khuyến nghị: ${bestResult.key} (${bestResult.value.connectionTime}ms)');
    }
    
    debugPrint('=' * 60);
  }

  /// Lấy kết quả test
  Map<String, TestResult> get testResults => Map.from(_testResults);
  
  /// Lấy thư viện tốt nhất
  String? getBestLibrary() {
    final successfulTests = _testResults.entries.where((entry) => entry.value.success);
    if (successfulTests.isEmpty) return null;
    
    return successfulTests
        .reduce((a, b) => a.value.connectionTime < b.value.connectionTime ? a : b)
        .key;
  }
}

/// Kết quả test
class TestResult {
  final bool success;
  final int connectionTime; // milliseconds
  final int servicesFound;
  final String? error;
  final String details;

  TestResult({
    required this.success,
    required this.connectionTime,
    required this.servicesFound,
    required this.error,
    required this.details,
  });

  @override
  String toString() {
    return 'TestResult(success: $success, time: ${connectionTime}ms, services: $servicesFound)';
  }
}
