import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:esc_pos_printer/esc_pos_printer.dart';
import 'package:esc_pos_utils/esc_pos_utils.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/pos_models.dart';
import '../models/receipt_models.dart';

/// Real Printer Service cho thiết bị thật
class RealPrinterService {
  static final RealPrinterService _instance = RealPrinterService._internal();
  factory RealPrinterService() => _instance;
  RealPrinterService._internal();

  // Network printer
  NetworkPrinter? _networkPrinter;
  
  // Bluetooth
  BluetoothConnection? _bluetoothConnection;
  
  // Status
  bool _isConnected = false;
  String _connectedPrinterName = '';
  String _connectionType = ''; // 'bluetooth' or 'network'

  /// Getters
  bool get isConnected => _isConnected;
  String get connectedPrinterName => _connectedPrinterName;
  String get connectionType => _connectionType;

  /// Tìm kiếm máy in Bluetooth
  Future<List<BluetoothDevice>> discoverBluetoothPrinters() async {
    try {
      // Kiểm tra và yêu cầu permissions
      await _requestBluetoothPermissions();
      
      // Kiểm tra Bluetooth có bật không
      bool? isEnabled = await FlutterBluetoothSerial.instance.isEnabled;
      if (isEnabled != true) {
        throw Exception('Bluetooth chưa được bật. Vui lòng bật Bluetooth và thử lại.');
      }

      // Lấy danh sách thiết bị đã ghép nối
      List<BluetoothDevice> bondedDevices = await FlutterBluetoothSerial.instance.getBondedDevices();
      
      // Lọc các thiết bị có thể là máy in
      List<BluetoothDevice> printers = bondedDevices.where((device) {
        return _isPrinterDevice(device);
      }).toList();
      
      return printers;
    } catch (e) {
      print('🔴 Error discovering Bluetooth printers: $e');
      throw Exception('Lỗi tìm kiếm máy in Bluetooth: $e');
    }
  }

  /// Tìm kiếm máy in mạng
  Future<List<String>> discoverNetworkPrinters() async {
    try {
      final List<String> printers = [];
      
      // Lấy thông tin mạng hiện tại
      final info = NetworkInfo();
      final wifiIP = await info.getWifiIP();
      
      if (wifiIP == null) {
        throw Exception('Không thể lấy thông tin mạng WiFi');
      }
      
      // Tách subnet từ IP hiện tại
      final parts = wifiIP.split('.');
      if (parts.length != 4) {
        throw Exception('IP address không hợp lệ');
      }
      
      final subnet = '${parts[0]}.${parts[1]}.${parts[2]}';
      
      // Scan subnet cho port 9100 (ESC/POS standard)
      for (int i = 1; i <= 254; i++) {
        final ip = '$subnet.$i';
        if (await _testPrinterConnection(ip)) {
          printers.add(ip);
        }
      }
      
      return printers;
    } catch (e) {
      print('🔴 Error discovering network printers: $e');
      throw Exception('Lỗi tìm kiếm máy in mạng: $e');
    }
  }

  /// Kết nối máy in Bluetooth
  Future<bool> connectToBluetoothPrinter(String address, String name) async {
    try {
      // Ngắt kết nối hiện tại nếu có
      await disconnect();
      
      // Kết nối Bluetooth
      _bluetoothConnection = await BluetoothConnection.toAddress(address);
      
      if (_bluetoothConnection?.isConnected == true) {
        _isConnected = true;
        _connectedPrinterName = name;
        _connectionType = 'bluetooth';
        
        print('✅ Connected to Bluetooth printer: $name');
        return true;
      }
      
      return false;
    } catch (e) {
      print('🔴 Error connecting to Bluetooth printer: $e');
      throw Exception('Lỗi kết nối máy in Bluetooth: $e');
    }
  }

  /// Kết nối máy in mạng
  Future<bool> connectToNetworkPrinter(String ip) async {
    try {
      // Ngắt kết nối hiện tại nếu có
      await disconnect();
      
      // Tạo profile cho máy in
      const PaperSize paper = PaperSize.mm80;
      final profile = await CapabilityProfile.load();
      
      // Tạo network printer
      _networkPrinter = NetworkPrinter(paper, profile);
      
      // Kết nối
      final result = await _networkPrinter!.connect(ip, port: 9100);
      
      if (result == PosPrintResult.success) {
        _isConnected = true;
        _connectedPrinterName = 'Network Printer ($ip)';
        _connectionType = 'network';
        
        print('✅ Connected to network printer: $ip');
        return true;
      }
      
      return false;
    } catch (e) {
      print('🔴 Error connecting to network printer: $e');
      throw Exception('Lỗi kết nối máy in mạng: $e');
    }
  }

  /// In tem món bếp
  Future<bool> printKitchenReceipt(List<OrderItem> items, String tableNumber) async {
    if (!_isConnected) {
      throw Exception('Chưa kết nối máy in');
    }

    try {
      if (_connectionType == 'network' && _networkPrinter != null) {
        return await _printKitchenReceiptNetwork(items, tableNumber);
      } else if (_connectionType == 'bluetooth' && _bluetoothConnection != null) {
        return await _printKitchenReceiptBluetooth(items, tableNumber);
      }
      
      return false;
    } catch (e) {
      print('🔴 Error printing kitchen receipt: $e');
      throw Exception('Lỗi in tem món: $e');
    }
  }

  /// Test in
  Future<bool> testPrint() async {
    if (!_isConnected) {
      throw Exception('Chưa kết nối máy in');
    }

    try {
      if (_connectionType == 'network' && _networkPrinter != null) {
        _networkPrinter!.text('=== TEST PRINT ===',
            styles: const PosStyles(align: PosAlign.center, bold: true));
        _networkPrinter!.text('Máy in hoạt động bình thường',
            styles: const PosStyles(align: PosAlign.center));
        _networkPrinter!.text('Thời gian: ${DateTime.now()}',
            styles: const PosStyles(align: PosAlign.center));
        _networkPrinter!.emptyLines(2);
        _networkPrinter!.cut();
        return true;
      } else if (_connectionType == 'bluetooth' && _bluetoothConnection != null) {
        // Tạo ESC/POS commands cho Bluetooth
        const PaperSize paper = PaperSize.mm80;
        final profile = await CapabilityProfile.load();
        final generator = Generator(paper, profile);
        
        List<int> bytes = [];
        bytes += generator.text('=== TEST PRINT ===',
            styles: const PosStyles(align: PosAlign.center, bold: true));
        bytes += generator.text('Máy in hoạt động bình thường',
            styles: const PosStyles(align: PosAlign.center));
        bytes += generator.text('Thời gian: ${DateTime.now()}',
            styles: const PosStyles(align: PosAlign.center));
        bytes += generator.emptyLines(2);
        bytes += generator.cut();
        
        _bluetoothConnection!.output.add(Uint8List.fromList(bytes));
        await _bluetoothConnection!.output.allSent;
        return true;
      }
      
      return false;
    } catch (e) {
      print('🔴 Error test printing: $e');
      throw Exception('Lỗi test in: $e');
    }
  }

  /// Ngắt kết nối
  Future<void> disconnect() async {
    try {
      if (_networkPrinter != null) {
        _networkPrinter!.disconnect();
        _networkPrinter = null;
      }
      
      if (_bluetoothConnection != null) {
        await _bluetoothConnection!.close();
        _bluetoothConnection = null;
      }
      
      _isConnected = false;
      _connectedPrinterName = '';
      _connectionType = '';
      
      print('✅ Printer disconnected');
    } catch (e) {
      print('🔴 Error disconnecting printer: $e');
    }
  }

  /// Private methods
  Future<void> _requestBluetoothPermissions() async {
    final permissions = [
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.location,
    ];
    
    for (final permission in permissions) {
      if (await permission.isDenied) {
        await permission.request();
      }
    }
  }

  bool _isPrinterDevice(BluetoothDevice device) {
    if (device.name == null) return false;
    
    final name = device.name!.toLowerCase();
    final printerKeywords = [
      'printer', 'print', 'xprinter', 'hprt', 'epson', 'canon',
      'hp', 'brother', 'zebra', 'pos', 'receipt', 'thermal'
    ];
    
    return printerKeywords.any((keyword) => name.contains(keyword));
  }

  Future<bool> _testPrinterConnection(String ip) async {
    // Implement network ping/connection test
    // This is a simplified version
    return false; // Placeholder
  }

  Future<bool> _printKitchenReceiptNetwork(List<OrderItem> items, String tableNumber) async {
    // Implement network printing
    return true; // Placeholder
  }

  Future<bool> _printKitchenReceiptBluetooth(List<OrderItem> items, String tableNumber) async {
    // Implement Bluetooth printing
    return true; // Placeholder
  }
}
