import '../models/pos_models.dart';

/// Mock Data Service cho POS system
/// Cung cấp dữ liệu mẫu cho development và testing
class MockDataService {
  static MockDataService? _instance;
  MockDataService._internal();

  factory MockDataService() {
    _instance ??= MockDataService._internal();
    return _instance!;
  }

  /// Mock Categories
  static List<Category> get mockCategories => [
        const Category(
          id: 'cat_1',
          name: '<PERSON><PERSON> phê',
          description: '<PERSON><PERSON>c loại cà phê đặc biệt',
          sortOrder: 1,
        ),
        const Category(
          id: 'cat_2',
          name: 'Tr<PERSON>',
          description: 'Trà các loại',
          sortOrder: 2,
        ),
        const Category(
          id: 'cat_3',
          name: '<PERSON><PERSON><PERSON> ngọt',
          description: '<PERSON><PERSON>h và đồ ngọt',
          sortOrder: 3,
        ),
        const Category(
          id: 'cat_4',
          name: '<PERSON><PERSON> ăn nhẹ',
          description: '<PERSON>ón ăn nhẹ và snack',
          sortOrder: 4,
        ),
      ];

  /// Mock Products dựa trên thiết kế <PERSON>lin
  static List<Product> get mockProducts => [
        const Product(
          id: 'prod_1',
          name: 'Coffee sáng tạo',
          price: 35000,
          imageUrl:
              'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',
          description: 'Cà phê đặc biệt với hương vị sáng tạo',
          categoryId: 'cat_1',
          stockQuantity: 50,
        ),
        const Product(
          id: 'prod_2',
          name: 'Cappuccino',
          price: 45000,
          imageUrl:
              'https://images.unsplash.com/photo-1534778101976-62847782c213?w=300',
          description: 'Cappuccino truyền thống Ý',
          categoryId: 'cat_1',
          stockQuantity: 30,
        ),
        const Product(
          id: 'prod_3',
          name: 'Latte',
          price: 42000,
          imageUrl:
              'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=300',
          description: 'Latte mềm mại với foam art',
          categoryId: 'cat_1',
          stockQuantity: 25,
        ),
        const Product(
          id: 'prod_4',
          name: 'Espresso',
          price: 30000,
          imageUrl:
              'https://images.unsplash.com/photo-1510707577719-ae7c14805e3a?w=300',
          description: 'Espresso đậm đà',
          categoryId: 'cat_1',
          stockQuantity: 40,
        ),
        const Product(
          id: 'prod_5',
          name: 'Trà xanh',
          price: 25000,
          imageUrl:
              'https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=300',
          description: 'Trà xanh tươi mát',
          categoryId: 'cat_2',
          stockQuantity: 35,
        ),
        const Product(
          id: 'prod_6',
          name: 'Trà ô long',
          price: 28000,
          imageUrl:
              'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=300',
          description: 'Trà ô long thơm ngon',
          categoryId: 'cat_2',
          stockQuantity: 20,
        ),
        const Product(
          id: 'prod_7',
          name: 'Bánh croissant',
          price: 35000,
          imageUrl:
              'https://images.unsplash.com/photo-1555507036-ab794f4ade2a?w=300',
          description: 'Bánh croissant bơ thơm',
          categoryId: 'cat_3',
          stockQuantity: 15,
        ),
        const Product(
          id: 'prod_8',
          name: 'Bánh muffin',
          price: 32000,
          imageUrl:
              'https://images.unsplash.com/photo-1607958996333-41aef7caefaa?w=300',
          description: 'Bánh muffin chocolate chip',
          categoryId: 'cat_3',
          stockQuantity: 18,
        ),
        const Product(
          id: 'prod_9',
          name: 'Sandwich',
          price: 55000,
          imageUrl:
              'https://images.unsplash.com/photo-1539252554453-80ab65ce3586?w=300',
          description: 'Sandwich thịt nguội và rau',
          categoryId: 'cat_4',
          stockQuantity: 12,
        ),
      ];

  /// Mock Tables
  static List<RestaurantTable> get mockTables => [
        const RestaurantTable(
          id: 'table_1',
          name: 'Bàn 1',
          capacity: 2,
          status: TableStatus.available,
          x: 50,
          y: 50,
        ),
        const RestaurantTable(
          id: 'table_2',
          name: 'Bàn 2',
          capacity: 4,
          status: TableStatus.occupied,
          currentOrderId: 'order_1',
          x: 150,
          y: 50,
        ),
        const RestaurantTable(
          id: 'table_3',
          name: 'Bàn 3',
          capacity: 6,
          status: TableStatus.reserved,
          x: 250,
          y: 50,
        ),
        const RestaurantTable(
          id: 'table_4',
          name: 'Bàn 4',
          capacity: 2,
          status: TableStatus.available,
          x: 50,
          y: 150,
        ),
        const RestaurantTable(
          id: 'table_5',
          name: 'Bàn 5',
          capacity: 4,
          status: TableStatus.cleaning,
          x: 150,
          y: 150,
        ),
      ];

  /// Mock Combos
  static List<Combo> get mockCombos => [
        const Combo(
          id: 'combo_1',
          name: 'Combo Sáng',
          items: [
            ComboItem(
                productId: 'prod_1',
                productName: 'Coffee sáng tạo',
                quantity: 1),
            ComboItem(
                productId: 'prod_7',
                productName: 'Bánh croissant',
                quantity: 1),
          ],
          originalPrice: 70000,
          comboPrice: 60000,
          description: 'Combo hoàn hảo cho bữa sáng',
          imageUrl:
              'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=300',
        ),
        const Combo(
          id: 'combo_2',
          name: 'Combo Chiều',
          items: [
            ComboItem(
                productId: 'prod_2', productName: 'Cappuccino', quantity: 1),
            ComboItem(
                productId: 'prod_8', productName: 'Bánh muffin', quantity: 1),
          ],
          originalPrice: 77000,
          comboPrice: 65000,
          description: 'Combo thư giãn buổi chiều',
          imageUrl:
              'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=300',
        ),
      ];

  /// Mock Orders
  static List<Order> get mockOrders => [
        Order(
          id: 'order_1',
          tableId: 'table_2',
          items: [
            const OrderItem(
              id: 'item_1',
              productId: 'prod_1',
              productName: 'Coffee sáng tạo',
              unitPrice: 35000,
              quantity: 2,
              total: 70000,
            ),
            const OrderItem(
              id: 'item_2',
              productId: 'prod_7',
              productName: 'Bánh croissant',
              unitPrice: 35000,
              quantity: 1,
              total: 35000,
            ),
          ],
          status: OrderStatus.preparing,
          createdAt: DateTime.now().subtract(const Duration(minutes: 15)),
          subtotal: 105000,
          tax: 10500,
          total: 115500,
        ),
        Order(
          id: 'order_2',
          tableId: 'table_1',
          items: [
            const OrderItem(
              id: 'item_3',
              productId: 'prod_2',
              productName: 'Cappuccino',
              unitPrice: 42000,
              quantity: 1,
              total: 42000,
            ),
          ],
          status: OrderStatus.pending,
          createdAt: DateTime.now().subtract(const Duration(hours: 1)),
          subtotal: 42000,
          tax: 4200,
          total: 46200,
        ),
        Order(
          id: 'order_3',
          tableId: 'table_3',
          items: [
            const OrderItem(
              id: 'item_4',
              productId: 'prod_3',
              productName: 'Latte',
              unitPrice: 45000,
              quantity: 2,
              total: 90000,
            ),
            const OrderItem(
              id: 'item_5',
              productId: 'prod_8',
              productName: 'Bánh muffin',
              unitPrice: 42000,
              quantity: 1,
              total: 42000,
            ),
          ],
          status: OrderStatus.completed,
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          subtotal: 132000,
          tax: 13200,
          total: 145200,
        ),
        Order(
          id: 'order_4',
          tableId: 'table_4',
          items: [
            const OrderItem(
              id: 'item_6',
              productId: 'prod_4',
              productName: 'Americano',
              unitPrice: 38000,
              quantity: 1,
              total: 38000,
            ),
          ],
          status: OrderStatus.cancelled,
          createdAt: DateTime.now().subtract(const Duration(hours: 3)),
          subtotal: 38000,
          tax: 3800,
          total: 41800,
        ),
      ];

  /// Mock Payments
  static List<Payment> get mockPayments => [
        Payment(
          id: 'pay_1',
          orderId: 'order_1',
          amount: 115500,
          method: PaymentMethod.cash,
          status: PaymentStatus.completed,
          createdAt: DateTime.now().subtract(const Duration(minutes: 15)),
        ),
        Payment(
          id: 'pay_2',
          orderId: 'order_2',
          amount: 46200,
          method: PaymentMethod.card,
          status: PaymentStatus.pending,
          createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        ),
        Payment(
          id: 'pay_3',
          orderId: 'order_3',
          amount: 145200,
          method: PaymentMethod.bankTransfer,
          status: PaymentStatus.completed,
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        Payment(
          id: 'pay_4',
          orderId: 'order_4',
          amount: 41800,
          method: PaymentMethod.cash,
          status: PaymentStatus.failed,
          createdAt: DateTime.now().subtract(const Duration(hours: 3)),
        ),
      ];

  /// Get products by category
  List<Product> getProductsByCategory(String categoryId) {
    return mockProducts
        .where((product) => product.categoryId == categoryId)
        .toList();
  }

  /// Search products
  List<Product> searchProducts(String query) {
    return mockProducts
        .where((product) =>
            product.name.toLowerCase().contains(query.toLowerCase()) ||
            (product.description?.toLowerCase().contains(query.toLowerCase()) ??
                false))
        .toList();
  }

  /// Get available tables
  List<RestaurantTable> getAvailableTables() {
    return mockTables
        .where((table) => table.status == TableStatus.available)
        .toList();
  }

  // Methods for feature-based structure
  List<Product> getAllProducts() => mockProducts;
  List<Order> getAllOrders() => mockOrders;
  List<RestaurantTable> getAllTables() => mockTables;
  List<Payment> getAllPayments() => mockPayments;

  List<Combo> getAllCombos() => mockCombos;
  List<Category> getAllCategories() => mockCategories;
}
