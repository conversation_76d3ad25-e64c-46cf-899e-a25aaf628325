import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';  // Tạm comment để test iOS
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'dart:async';

import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';  // Comment tạm thời
// import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:myhome/services/map_service.dart';
import 'package:myhome/services/api_service.dart';
import 'package:myhome/services/device_service.dart';
import 'package:myhome/services/logger_service.dart';
import 'package:myhome/screens/error.dart';
import 'package:myhome/utils/error_handler.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'config/proxy_config.dart';
import 'config/environment.dart';
// Imports từ nhánh origin/tuyen-8-7-25
import 'features/profile/presentation/widgets/personal_information_wrapper.dart';
import 'features/auth/presentation/pages/change_password_page.dart';
import 'features/house_management/presentation/pages/house_management_screen.dart';
import 'features/house_management/presentation/bloc/house_management_bloc.dart';
import 'features/add_house/presentation/pages/add_house_screen.dart';
import 'features/add_house/presentation/bloc/add_house_bloc.dart';
import 'features/forum/presentation/pages/forum_post_detail_page.dart';
import 'features/forum/presentation/bloc/forum_post_detail/forum_post_detail_bloc.dart';

// Revenue Management imports
import 'features/revenue_management/presentation/pages/revenue_summary_screen.dart';
import 'features/revenue_management/presentation/pages/revenue_detail_screen.dart';
import 'features/revenue_management/presentation/pages/next_month_plan_screen.dart';
import 'features/revenue_management/presentation/bloc/revenue_summary/revenue_summary_bloc.dart';
import 'features/revenue_management/presentation/bloc/revenue_detail/revenue_detail_bloc.dart';
import 'features/revenue_management/presentation/bloc/next_month_plan/next_month_plan_bloc.dart';
import 'features/revenue_management/presentation/bloc/add_revenue/add_revenue_bloc.dart';

// POS System imports
import 'features/pos/presentation/pages/pos_main_screen.dart';
import 'features/do_uong/presentation/pages/do_uong_page.dart';
import 'features/do_uong/presentation/bloc/do_uong_bloc.dart';
import 'features/ticket/presentation/pages/ticket_page.dart';
import 'features/ticket/presentation/bloc/ticket_bloc.dart';

import 'services/auth_service.dart';
import 'services/post_service.dart';
import 'services/home_service.dart';
import 'services/account_service.dart';

// BLoC + Clean Architecture imports
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/di/injection_container.dart' as di;
import 'core/network/network_info.dart';
import 'features/auth/presentation/bloc/auth_bloc.dart';
import 'features/auth/presentation/bloc/auth_event.dart';
import 'features/auth/presentation/bloc/auth_state.dart';
import 'features/auth/presentation/pages/login_page.dart';
import 'features/auth/presentation/pages/register_page.dart';
import 'features/auth/presentation/pages/forgot_password_page.dart';
import 'features/home/<USER>/bloc/home_bloc.dart';
import 'features/search/presentation/pages/search_page.dart';
import 'features/search/presentation/bloc/search_bloc.dart';
import 'features/create_post/presentation/pages/create_post_page.dart';
import 'features/user_type_listing/presentation/pages/user_type_listing_screen.dart';
import 'features/user_type_listing/presentation/bloc/user_type_listing_bloc.dart';
import 'features/user_detail/domain/entities/user_info.dart';
import 'features/profile/di/profile_injection.dart';
import 'features/map/presentation/pages/map_page.dart';
import 'features/map/presentation/bloc/map_bloc.dart';
import 'features/user_detail/presentation/pages/user_detail_screen.dart';
import 'features/user_detail/presentation/bloc/user_detail_bloc.dart';
import 'features/house_detail/presentation/pages/house_detail_screen.dart';
import 'features/house_detail/presentation/bloc/house_detail_bloc.dart';
import 'features/service/presentation/pages/service_listing_page.dart';
import 'features/service/presentation/bloc/service_listing_bloc.dart';
import 'features/property/presentation/pages/property_detail_screen.dart';
import 'features/property/presentation/pages/property_listing_screen.dart';
import 'features/property/domain/entities/property_filter.dart';
import 'features/property/test/property_detail_test_screen.dart';
import 'features/wallet/presentation/pages/wallet_page.dart';
import 'features/campaign/presentation/bloc/campaign_list_bloc.dart';
import 'features/campaign/presentation/screens/campaign_listing_screen.dart';
import 'features/campaign/presentation/screens/campaign_detail_screen.dart';
import 'features/forum/presentation/pages/forum_posts_page.dart';
import 'features/customer_management/presentation/pages/customer_management_screen.dart';
import 'features/customer_management/presentation/bloc/customer_management_bloc.dart';
import 'features/customer_management/domain/entities/customer_type.dart';
// Removed employee_management imports

// Cho Hai San imports
import 'features/cho_hai_san/presentation/pages/cho_hai_san_page.dart';

// Invoice imports
import 'features/invoice/presentation/pages/invoice_page.dart';
import 'features/invoice/presentation/pages/invoice_demo_page.dart';
import 'features/invoice/presentation/bloc/invoice_bloc.dart';


// Payment imports for testing
import 'features/payment/presentation/pages/payment_page.dart';

// Mặc định sử dụng môi trường development cho testing
const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');

AndroidOptions _getAndroidOptions() => const AndroidOptions(
      encryptedSharedPreferences: true,
    );

// Khởi tạo các plugin trước khi chạy ứng dụng
Future<void> _initializePlugins() async {
  // Khởi tạo ImagePicker để tránh lỗi kết nối channel
  final ImagePicker imagePicker = ImagePicker();
  // Có thể thêm các plugin khác cần khởi tạo sớm ở đây
}

// @pragma('vm:entry-point')
// Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
//   await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
//   // await setupFlutterNotifications();  // Comment tạm thời
//   // showFlutterNotification(message);  // Comment tạm thời
//   // If you're going to use other Firebase services in the background, such as Firestore,
//   // make sure you call `initializeApp` before using other Firebase services.
//   print('Handling a background message ${message.messageId}');
// }

/// Create a [AndroidNotificationChannel] for heads up notifications
// late AndroidNotificationChannel channel;  // Comment tạm thời

bool isFlutterLocalNotificationsInitialized = false;

// Comment tạm thời setupFlutterNotifications vì flutter_local_notifications bị conflict
/*
Future<void> setupFlutterNotifications() async {
  if (isFlutterLocalNotificationsInitialized) {
    return;
  }
  channel = const AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // title
    description:
    'This channel is used for important notifications.', // description
    importance: Importance.high,
  );

  flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  /// Create an Android Notification Channel.
  ///
  /// We use this channel in the `AndroidManifest.xml` file to override the
  /// default FCM channel to enable heads up notifications.
  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
      AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  /// Update the iOS foreground notification presentation options to allow
  /// heads up notifications.
  // await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
  //   alert: true,
  //   badge: true,
  //   sound: true,
  // );
  isFlutterLocalNotificationsInitialized = true;
}
*/

// Comment tạm thời showFlutterNotification
/*
void showFlutterNotification(RemoteMessage message) {
  RemoteNotification? notification = message.notification;
  AndroidNotification? android = message.notification?.android;
  if (notification != null && android != null && !kIsWeb) {
    flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification.title,
      notification.body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          channel.id,
          channel.name,
          channelDescription: channel.description,
          // TODO add a proper drawable resource to android, for now using
          //      one that already exists in example app.
          icon: 'ic_notification',
        ),
      ),
    );
  }
}
*/

/// Initialize the [FlutterLocalNotificationsPlugin] package.
// late FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin;  // Comment tạm thời

Future<void> saveTokenToDatabase(String token) async {
  final deviceService = DeviceService();
  final storage = FlutterSecureStorage(aOptions: _getAndroidOptions());
  final loginToken = await storage.read(key: 'loginToken');
  if (loginToken != null) {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    var deviceId = '';
    var deviceType = 'unknown';
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceId = androidInfo.id;
      deviceType = 'android';
    }
    if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceId = iosInfo.name;
      deviceType = 'ios';
    }
    try {
      final rest =
          await deviceService.updateTokenFirebase(deviceId, token, deviceType);

      debugPrint('token $token');
    } catch (e) {
      await storage.delete(key: 'loginToken');
      debugPrint('Lỗi khi update token user đang đăng nhập');
    }
  }
}

// Future<void> requestPermissionNotification() async {
//   FirebaseMessaging messaging = FirebaseMessaging.instance;

//   NotificationSettings settings = await messaging.requestPermission(
//     alert: true,
//     announcement: false,
//     badge: true,
//     carPlay: false,
//     criticalAlert: false,
//     provisional: false,
//     sound: true,
//   );

//   if (settings.authorizationStatus == AuthorizationStatus.authorized) {
//     print('User granted permission');
//   } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
//     print('User granted provisional permission');
//   } else {
//     print('User declined or has not accepted permission');
//   }
// }

void configLoading() {
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.pulse
    ..loadingStyle = EasyLoadingStyle.custom
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..progressColor = Colors.yellow
    ..backgroundColor = const Color(0xFF0B284F) // POS primary color
    ..indicatorColor = Colors.white
    ..textColor = Colors.white
    ..maskColor = const Color(0xFF0B284F).withOpacity(0.5)
    ..userInteractions = true
    ..dismissOnTap = false;
}

// Khởi tạo môi trường dựa trên biến môi trường hoặc flavor
void _initializeEnvironment() {
  switch (environment) {
    case 'dev':
      AppConfig.initialize(env: Environment.dev);
      break;
    case 'staging':
      AppConfig.initialize(env: Environment.staging);
      break;
    case 'prod':
    default:
      AppConfig.initialize(env: Environment.production);
      break;
  }
}

Future<void> main() async {
  // Chạy ứng dụng trong zone được bảo vệ để bắt lỗi
  runZonedGuarded(() async {
    // Đảm bảo Flutter đã khởi tạo hoàn chỉnh
    WidgetsFlutterBinding.ensureInitialized();

    try {
      // Khởi tạo cấu hình môi trường TRƯỚC KHI sử dụng
      _initializeEnvironment();

      // Khởi tạo dependency injection cho Clean Architecture
      await di.init();

      // Khởi tạo các dependency cơ bản
      final flutterSecureStorage = const FlutterSecureStorage();
      final connectivity = Connectivity();
      final networkInfo = NetworkInfoImpl(connectivity);

      // Đăng ký các dependency cơ bản
      if (!sl.isRegistered<FlutterSecureStorage>()) {
        sl.registerLazySingleton(() => flutterSecureStorage);
      }
      if (!sl.isRegistered<Connectivity>()) {
        sl.registerLazySingleton(() => connectivity);
      }
      if (!sl.isRegistered<NetworkInfo>()) {
        sl.registerLazySingleton<NetworkInfo>(() => networkInfo);
      }

      // Khởi tạo dependency injection cho feature profile
      await initProfileInjection();

      // Khởi tạo LoggerService với xử lý lỗi
      final logger = LoggerService();
      try {
        await logger.initialize();
        debugPrint('✅ LoggerService đã được khởi tạo');
      } catch (e) {
        debugPrint('⚠️ Lỗi khi khởi tạo LoggerService: $e');
        debugPrint(
            '💡 Ứng dụng sẽ tiếp tục chạy nhưng không có chức năng logging');
      }

      // Khởi tạo ErrorHandler
      final errorHandler = ErrorHandler(logger);
      try {
        errorHandler.initialize();
        debugPrint('✅ ErrorHandler đã được khởi tạo');
      } catch (e) {
        debugPrint('⚠️ Lỗi khi khởi tạo ErrorHandler: $e');
      }

      // Thiết lập logger cho ApiService
      try {
        final apiService = ApiService();
        apiService.setLoggers(logger, errorHandler);
        debugPrint('✅ ApiService logging đã được thiết lập');
      } catch (e) {
        debugPrint('⚠️ Lỗi khi thiết lập logging cho ApiService: $e');
      }

      // Log thông tin khởi động ứng dụng
      logger.info('Ứng dụng bắt đầu khởi động');
      logger.debug('Môi trường: ${AppConfig.environment}');

      // Kích hoạt Proxyman nếu đang trong môi trường phát triển
      if (AppConfig.isDevelopment) {
        // ProxyConfig.enable(); // Bật Proxyman
        logger.debug('Proxyman đã được tắt');
      }

      try {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
        logger.info('Firebase đã khởi tạo thành công');
      } catch (e, stackTrace) {
        logger.exception('Firebase khởi tạo thất bại', e, stackTrace);
      }

      // Set the background messaging handler early on, as a named top-level function
      // FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

      // FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      //   print('Got a message whilst in the foreground!');
      //   print('Message data: ${message.data}');

      //   if (message.notification != null) {
      //     print('Message also contained a notification: ${message.notification}');
      //   }
      // });

      if (!kIsWeb) {
        // await setupFlutterNotifications();  // Comment tạm thời
      }
      configLoading();

      // Cấu hình hệ thống
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: Colors.transparent,
        ),
      );

      // Remove splash screen khi app đã sẵn sàng
      // FlutterNativeSplash.remove(); // Tạm vô hiệu hóa do lỗi build

      // DISABLED FOR DEBUG: Comment wallet API test (requires valid token)
      // _testWalletAPI();

      // Khởi tạo các plugin trước khi chạy ứng dụng
      await _initializePlugins();

      // Chạy ứng dụng
      runApp(
        // Cung cấp LoggerService và ErrorHandler cho toàn bộ ứng dụng
        MultiProvider(
          providers: [
            Provider<LoggerService>.value(value: logger),
            Provider<ErrorHandler>.value(value: errorHandler),
            // Providers từ nhánh tuyen-8-7-25
            ChangeNotifierProvider(create: (_) => AuthService()),
            ChangeNotifierProvider(create: (_) => MapService()),
            ChangeNotifierProvider(create: (_) => HomeService()),
            ChangeNotifierProvider(create: (_) => AccountService()),
            ChangeNotifierProvider(create: (_) => PostService()),
            // Thêm BLoC providers ở cấp cao nhất
            BlocProvider(
              create: (context) => di.sl<AuthBloc>()..add(AuthCheckRequested()),
            ),
            BlocProvider(
              create: (context) {
                try {
                  debugPrint('🔍 Creating HomeBloc at app level...');
                  final homeBloc = di.sl<HomeBloc>();
                  debugPrint('✅ HomeBloc created successfully at app level');
                  return homeBloc;
                } catch (e, stackTrace) {
                  debugPrint('❌ Error creating HomeBloc at app level: $e');
                  debugPrint('❌ StackTrace: $stackTrace');
                  rethrow;
                }
              },
            ),
          ],
          child: const MyApp(),
        ),
      );
    } catch (e, stackTrace) {
      // Xử lý trường hợp lỗi nghiêm trọng trong quá trình khởi tạo
      debugPrint('💥 LỖI NGHIÊM TRỌNG TRONG MAIN: $e');
      debugPrint('💥 Stack trace: $stackTrace');

      // Vẫn chạy ứng dụng với cấu hình tối thiểu
      runApp(MaterialApp(
        home: Scaffold(
          body: Center(
            child: Text(
              'Lỗi khởi động ứng dụng: $e',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ));
    }
  }, (error, stackTrace) {
    // Zone error handler
    debugPrint('⚠️ Zone error: $error');
    debugPrint('⚠️ Zone stackTrace: $stackTrace');
  });
}

// Test wallet API từ nhánh origin/tuyen-8-7-25
void _testWalletAPI() async {
  try {
    debugPrint('=== TESTING WALLET API ===');
    final apiService = ApiService();

    // Set test token
    const testToken =
        'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FkbWluLm15aG9tZXZpZXRuYW0uY29tL2FwaS9hdXRoL2xvZ2luIiwiaWF0IjoxNzUxOTA0MTY1LCJleHAiOjE3NTE5OTA1NjUsIm5iZiI6MTc1MTkwNDE2NSwianRpIjoialg5b0VSMnFUcXF5Qm1ibSIsInN1YiI6MTA5LCJwcnYiOiI4N2UwYWYxZWY5ZmQxNTgxMmZkZWM5NzE1M2ExNGUwYjA0NzU0NmFhIn0.wb0FkKrkvdfW1W95fgtBEJlD1izm_6Y93_vyNuNbb4M';
    await apiService.saveToken(testToken);

    final response = await apiService.getWallet();
    debugPrint('=== WALLET API TEST RESULT ===');
    debugPrint('Status Code: ${response.statusCode}');
    debugPrint('Response Data: ${response.data}');
  } catch (e) {
    debugPrint('=== WALLET API TEST ERROR ===');
    debugPrint('Error: $e');
  }
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Nhà Đất',
      debugShowCheckedModeBanner: false, // Ẩn debug banner
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('vi', 'VN'),
        Locale('en', 'US'),
      ],
      locale: const Locale('vi', 'VN'),
      theme: ThemeData(
        primarySwatch: Colors.blue, // Changed from green to blue for POS
        primaryColor: const Color(0xFF0B284F), // POS primary color
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF0B284F), // POS primary color
          primary: const Color(0xFF0B284F), // POS primary color
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF116506),
          foregroundColor: Colors.white,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
      ),
      home: const AuthenticationWrapper(),
      routes: {
        '/webview': (context) => const WebView(),
        '/navigation': (context) => PosMainScreen(),
        '/pos': (context) => PosMainScreen(),
        '/login': (context) => const LoginPage(),
        '/register': (context) => const RegisterPage(),
        '/forgot-password': (context) => const ForgotPasswordPage(),
        '/home': (context) => PosMainScreen(),
        '/menu': (context) => BlocProvider(
              create: (context) => di.sl<DoUongBloc>(),
              child: const DoUongPage(),
            ),
        '/ticket': (context) => BlocProvider(
              create: (context) => di.sl<TicketBloc>(),
              child: const TicketPage(),
            ),
        // Routes từ nhánh origin/tuyen-8-7-25
        '/wallet': (context) => const WalletPage(),
        '/personal-information': (context) =>
            const PersonalInformationWrapper(),
        '/change-password': (context) => const ChangePasswordPage(),
        '/house-management': (context) => BlocProvider(
              create: (context) => di.sl<HouseManagementBloc>(),
              child: const HouseManagementScreen(),
            ),
        '/add-house': (context) => BlocProvider(
              create: (context) => di.sl<AddHouseBloc>(),
              child: const AddHouseScreen(),
            ),
        '/create-post': (context) => const CreatePostPage(),
        '/search': (context) => BlocProvider<SearchBloc>(
              create: (context) => di.sl<SearchBloc>(),
              child: const SearchPage(),
            ),
        '/map': (context) => BlocProvider<MapBloc>(
              create: (context) => di.sl<MapBloc>(),
              child: const MapPage(),
            ),
        '/user-detail': (context) => BlocProvider<UserDetailBloc>(
              create: (context) => di.sl<UserDetailBloc>(),
              child: UserDetailScreen(
                userId: ModalRoute.of(context)!.settings.arguments as int,
              ),
            ),
        '/house-detail': (context) => BlocProvider<HouseDetailBloc>(
              create: (context) => di.sl<HouseDetailBloc>(),
              child: HouseDetailScreen(
                houseId: ModalRoute.of(context)!.settings.arguments as int,
              ),
            ),
        '/property-detail': (context) => PropertyDetailScreen(
              propertyId: ModalRoute.of(context)!.settings.arguments as int,
            ),
        '/property-detail-test': (context) => const PropertyDetailTestScreen(),
        '/property-listing': (context) {
          final args = ModalRoute.of(context)!.settings.arguments
              as Map<String, dynamic>?;
          return PropertyListingScreen(
            initialFilter: args?['filter'] as PropertyFilter?,
            initialQuery: args?['query'] as String?,
            title: args?['title'] as String?,
          );
        },
        '/user-type-listing': (context) {
          final args = ModalRoute.of(context)!.settings.arguments
              as Map<String, dynamic>?;
          final typeString = args?['type'] as String?;
          UserRole? initialType;
          if (typeString != null) {
            initialType = UserRole.fromString(typeString);
          }
          return BlocProvider<UserTypeListingBloc>(
            create: (context) => di.sl<UserTypeListingBloc>(),
            child: UserTypeListingScreen(initialType: initialType),
          );
        },
        '/forum-posts': (context) => const ForumPostsPage(),
        '/forum-post-detail': (context) => BlocProvider<ForumPostDetailBloc>(
              create: (context) => di.sl<ForumPostDetailBloc>(),
              child: ForumPostDetailPage(
                postId: ModalRoute.of(context)!.settings.arguments as int,
              ),
            ),
        '/tin-chien-dich': (context) => const CampaignListingScreen(),
        '/tin-chien-dich-detail': (context) => CampaignDetailScreen(
              campaignId: ModalRoute.of(context)!.settings.arguments as int,
            ),
        '/service-listing': (context) => const ServiceListingPage(),
        '/revenue-summary': (context) => BlocProvider<RevenueSummaryBloc>(
              create: (context) => di.sl<RevenueSummaryBloc>(),
              child: const RevenueSummaryScreen(),
            ),
        '/revenue-detail': (context) {
          final args = ModalRoute.of(context)!.settings.arguments
              as Map<String, dynamic>?;
          return MultiBlocProvider(
            providers: [
              BlocProvider<RevenueDetailBloc>(
                create: (context) => di.sl<RevenueDetailBloc>(),
              ),
              BlocProvider<AddRevenueBloc>(
                create: (context) => di.sl<AddRevenueBloc>(),
              ),
            ],
            child: RevenueDetailScreen(
              month: args?['month'] as int? ?? DateTime.now().month,
              year: args?['year'] as int? ?? DateTime.now().year,
            ),
          );
        },
        '/next-month-plan': (context) => BlocProvider<NextMonthPlanBloc>(
              create: (context) => di.sl<NextMonthPlanBloc>(),
              child: const NextMonthPlanScreen(),
            ),
        '/customer-management': (context) {
          final args = ModalRoute.of(context)!.settings.arguments
              as Map<String, dynamic>?;
          return BlocProvider<CustomerManagementBloc>(
            create: (context) => di.sl<CustomerManagementBloc>(),
            child: CustomerManagementScreen(
              userRole: args?['userRole'] as String?,
              initialType: args?['initialType'] as CustomerType?,
            ),
          );
        },
        '/cho-hai-san': (context) => const ChoHaiSanPage(),
        '/invoice': (context) => BlocProvider<InvoiceBloc>(
              create: (context) => di.sl<InvoiceBloc>(),
              child: const InvoicePage(),
            ),
        '/invoice-demo': (context) => const InvoiceDemoPage(),
        // Test route for PaymentPage
        '/payment-test': (context) => const PaymentPage(),

      },
      onGenerateRoute: (settings) {
        // No dynamic routes handled here for now.

        // Return null for unknown routes
        return null;
      },
      builder: EasyLoading.init(),
    );
  }
}

class AuthenticationWrapper extends StatelessWidget {
  const AuthenticationWrapper({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Không cần tạo lại BLoC providers vì đã được provide ở cấp app level
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        debugPrint(
            '🔍 AuthenticationWrapper - Current state: ${state.runtimeType}');

        // Đảm bảo EasyLoading được ẩn đi ở cấp cao nhất khi không còn loading
        if (state is! AuthLoading) {
          EasyLoading.dismiss();
        }

        if (state is AuthLoading) {
          debugPrint('📱 Showing loading screen');
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        } else if (state is AuthAuthenticated) {
          debugPrint('📱 Showing PosMainScreen for authenticated user');
          return PosMainScreen(key: posMainScreenKey);
        } else if (state is AuthGuest) {
          debugPrint('📱 Showing PosMainScreen for guest user');
          // Handle guest login - chuyển đến PosMainScreen
          return PosMainScreen(key: posMainScreenKey);
        } else if (state is AuthUnauthenticated) {
          debugPrint('📱 Showing LoginPage for unauthenticated user');
          return const LoginPage();
        } else if (state is AuthError) {
          debugPrint('📱 Showing LoginPage due to auth error');
          return const LoginPage();
        } else {
          debugPrint('📱 Showing loading screen for unknown state');
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }
      },
    );
  }
}

class WebView extends StatefulWidget {
  const WebView({super.key});

  @override
  State<WebView> createState() => _WebViewState();
}

class _WebViewState extends State<WebView> with TickerProviderStateMixin {
  String? loginToken;
  String? initialMessage;
  late final WebViewController _controller;
  late AnimationController _animationController;

  static const circularProgressBegin = Color(0xFF448AFF);
  static const circularProgressEnd = Color(0xFFF44336);

  ConnectivityResult _connectionStatus = ConnectivityResult.none;
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;

  int _errorCode = 0;
  bool _isLoading = false, _isVisible = false, _isOffline = false;

  final GeolocatorPlatform _geolocatorPlatform = GeolocatorPlatform.instance;

  @override
  void initState() {
    _animationController =
        AnimationController(vsync: this, duration: const Duration(seconds: 2));
    _animationController.repeat();
    super.initState();
    //check network connectivity
    initConnectivity();
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);

    // setupToken();
    // FirebaseMessaging.instance.getInitialMessage().then(
    //       (value) => setState(
    //         () {
    //       initialMessage = value?.data.toString();
    //     },
    //   ),
    // );

    // FirebaseMessaging.onMessage.listen(showFlutterNotification);  // Comment tạm thời

    // FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
    //   print('A new onMessageOpenedApp event was published!');
    // });

    // #docregion platform_features
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller =
        WebViewController.fromPlatformCreationParams(params);
    // #enddocregion platform_features

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            debugPrint('WebView is loading (progress : $progress%)');
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true; //Show CircularProgressIndicator
            });
            _isVisible = false;
            _isOffline = false;
            // FlutterNativeSplash.remove();  // Comment tạm thời
            debugPrint('Page started loading: $url');

            EasyLoading.show(
                maskType: EasyLoadingMaskType.black, status: 'Vui lòng đợi...');
          },
          onPageFinished: (String url) async {
            EasyLoading.dismiss();
            debugPrint('Page finished loading: $url');
            checkError();
            try {
              loginToken = await _controller.runJavaScriptReturningResult(
                  "window.localStorage['auth._token.laravelJWT']") as String;
              if (loginToken != null) {
                final storage =
                    FlutterSecureStorage(aOptions: _getAndroidOptions());
                await storage.write(
                    key: 'loginToken', value: loginToken?.replaceAll('"', ''));
                // updateFcmToken();
                debugPrint('onUrlChange Token login: $loginToken');
              }
            } catch (e) {
              print(e);
            }
          },
          onWebResourceError: (WebResourceError error) {
            _errorCode = 500;
            _isVisible = true;
            debugPrint('''
            Page resource error:
            code: ${error.errorCode}
            description: ${error.description}
            errorType: ${error.errorType}
            isForMainFrame: ${error.isForMainFrame}
          ''');
          },
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.contains('map')) {
              _getCurrentPosition();
            }
            if (request.url.contains('myhomevietnam')) {
              return NavigationDecision.navigate;
            } else {
              _isVisible = true; //Hide Offline Page
              _isOffline = false; //set Page type to error
              return NavigationDecision.prevent;
            }
          },
          onUrlChange: (UrlChange change) async {
            try {
              if (Platform.isAndroid) {
                if (change.url!.contains('map')) {
                  _getCurrentPosition();
                }
              }
              debugPrint('url change to ${change.url}');
              loginToken = await _controller.runJavaScriptReturningResult(
                  "window.localStorage['auth._token.laravelJWT']") as String;
              if (loginToken != null) {
                final storage =
                    FlutterSecureStorage(aOptions: _getAndroidOptions());
                await storage.write(
                    key: 'loginToken', value: loginToken?.replaceAll('"', ''));
                // updateFcmToken();
                debugPrint('onUrlChange Token login: $loginToken');
              }
            } catch (e) {
              _errorCode = 500;
              _isVisible = true;
              print(e);
            }
          },
        ),
      )
      ..addJavaScriptChannel(
        'Toaster',
        onMessageReceived: (JavaScriptMessage message) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(message.message)),
          );
        },
      )
      ..enableZoom(false)
      ..loadRequest(Uri.parse('https://myhomevietnam.com'));

    _controller = controller;
  }

  @override
  void dispose() {
    _animationController.dispose();
    _connectivitySubscription.cancel();
    super.dispose();
  }

  Future<void> checkError() async {
    //Check Network Status
    ConnectivityResult result = await Connectivity().checkConnectivity();

    //if Online: hide offline page and show web page
    if (result != ConnectivityResult.none) {
      _isVisible = false; //Hide Offline Page
      _isOffline = false; //set Page type to error
    }

    //If Offline: hide web page show offline page
    else {
      _errorCode = 0;
      _isOffline = true; //Set Page type to offline
      _isVisible = true; //Show offline page
    }

    // If error is fixed: hide error page and show web page
    if (_errorCode == 1) _isVisible = false;
    setState(() {});
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initConnectivity() async {
    late ConnectivityResult result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
    } on PlatformException catch (e) {
      print('Couldn\'t check connectivity status');
      return;
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) {
      return Future.value(null);
    }

    return _updateConnectionStatus(result);
  }

  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    setState(() {
      _connectionStatus = result;
    });
    checkError();
  }

  Future<bool> _onWillPop() async {
    if (await _controller.canGoBack()) {
      _controller.goBack();
      return Future.value(false);
    } else {
      return (await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Nhà Đất?'),
              content: const Text('Bạn chắc chắn muốn thoát ứng dụng.'),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Không'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Có'),
                ),
              ],
            ),
          )) ??
          false;
    }
  }

  // Future<void> setupToken() async {
  //   // Get the token each time the application loads
  //   String? token = await FirebaseMessaging.instance.getToken();

  //   // Save the initial token to the database
  //   await saveTokenToDatabase(token!);

  //   // Any time the token refreshes, store this in the database too.
  //   FirebaseMessaging.instance.onTokenRefresh.listen(saveTokenToDatabase);
  // }

  // Future<void> updateFcmToken() async {
  //   // Get the token each time the application loads
  //   String? token = await FirebaseMessaging.instance.getToken();
  //   // Save the initial token to the database
  //   await saveTokenToDatabase(token!);
  // }

  Future<void> _getCurrentPosition() async {
    final hasPermission = await _handlePermission();

    if (!hasPermission) {
      return;
    }

    final position = await _geolocatorPlatform.getCurrentPosition();
    //
    print(position);
  }

  Future<bool> _handlePermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled.
    serviceEnabled = await _geolocatorPlatform.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    permission = await _geolocatorPlatform.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await _geolocatorPlatform.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return false;
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: _onWillPop,
        child: Scaffold(
            backgroundColor: const Color(0xFF0B284F), // POS primary color
            body: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: SafeArea(
                  child: Stack(
                alignment: Alignment.center,
                children: [
                  WebViewWidget(controller: _controller),
                  //Error Page
                  Material(
                    type: MaterialType.transparency,
                    child: Visibility(
                      visible: _isVisible,
                      child: ErrorScreen(
                          isOffline: _isOffline,
                          onPressed: () {
                            _controller.reload();
                            if (_errorCode != 0) {
                              _errorCode = 1;
                            }
                          }),
                    ),
                  )
                ],
              )),
            )));
  }
}
