import 'package:flutter/material.dart';
import '../services/bluetooth_test_service.dart';

class BluetoothTestWidget extends StatefulWidget {
  final String targetMacAddress;
  
  const BluetoothTestWidget({
    Key? key,
    required this.targetMacAddress,
  }) : super(key: key);

  @override
  State<BluetoothTestWidget> createState() => _BluetoothTestWidgetState();
}

class _BluetoothTestWidgetState extends State<BluetoothTestWidget> {
  final BluetoothTestService _testService = BluetoothTestService();
  bool _isRunning = false;
  Map<String, TestResult> _results = {};

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.science, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Bluetooth Library Test',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_isRunning)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            
            Text(
              'Target Device: ${widget.targetMacAddress}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            
            // Test Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRunning ? null : _runTests,
                icon: const Icon(Icons.play_arrow),
                label: Text(_isRunning ? 'Đang test...' : 'Chạy Test Tất Cả Thư Viện'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Results
            if (_results.isNotEmpty) ...[
              const Text(
                'Kết Quả Test:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              
              ..._results.entries.map((entry) => _buildTestResultCard(entry.key, entry.value)),
              
              const SizedBox(height: 16),
              
              // Best Library Recommendation
              _buildRecommendation(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultCard(String libraryName, TestResult result) {
    final isSuccess = result.success;
    final color = isSuccess ? Colors.green : Colors.red;
    final icon = isSuccess ? Icons.check_circle : Icons.error;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    libraryName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                Text(
                  isSuccess ? 'THÀNH CÔNG' : 'THẤT BẠI',
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              children: [
                _buildInfoChip('⏱️ ${result.connectionTime}ms'),
                const SizedBox(width: 8),
                _buildInfoChip('🔧 ${result.servicesFound} services'),
              ],
            ),
            
            const SizedBox(height: 8),
            Text(
              result.details,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
            
            if (result.error != null) ...[
              const SizedBox(height: 4),
              Text(
                'Lỗi: ${result.error}',
                style: const TextStyle(
                  fontSize: 11,
                  color: Colors.red,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(fontSize: 11),
      ),
    );
  }

  Widget _buildRecommendation() {
    final bestLibrary = _testService.getBestLibrary();
    
    if (bestLibrary == null) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.orange[50],
          border: Border.all(color: Colors.orange),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'Không có thư viện nào kết nối thành công. Kiểm tra lại máy in và thử lại.',
                style: TextStyle(color: Colors.orange),
              ),
            ),
          ],
        ),
      );
    }

    final bestResult = _results[bestLibrary]!;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green[50],
        border: Border.all(color: Colors.green),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.recommend, color: Colors.green),
              SizedBox(width: 8),
              Text(
                'Khuyến Nghị',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Sử dụng: $bestLibrary',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          Text(
            'Thời gian kết nối: ${bestResult.connectionTime}ms',
            style: const TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Future<void> _runTests() async {
    setState(() {
      _isRunning = true;
      _results.clear();
    });

    try {
      final results = await _testService.runAllTests(widget.targetMacAddress);
      setState(() {
        _results = results;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi khi chạy test: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }
}
