package com.lnbvd.pos.com.vn

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.lnbvd.pos.com.vn/spp_printer"
    private lateinit var sppPrinter: SppPrinter

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        sppPrinter = SppPrinter(applicationContext)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "connect" -> {
                    val macAddress = call.argument<String>("macAddress")
                    if (macAddress == null) {
                        result.error("INVALID_ARGUMENT", "MAC address is required.", null)
                        return@setMethodCallHandler
                    }
                    CoroutineScope(Dispatchers.IO).launch {
                        val connectResult = sppPrinter.connect(macAddress)
                        withContext(Dispatchers.Main) {
                            if (connectResult == "SUCCESS") {
                                result.success(null)
                            } else {
                                result.error("CONNECTION_FAILED", connectResult, null)
                            }
                        }
                    }
                }
                "print" -> {
                    val data = call.argument<ByteArray>("data")
                    if (data == null) {
                        result.error("INVALID_ARGUMENT", "Data is required.", null)
                        return@setMethodCallHandler
                    }
                     CoroutineScope(Dispatchers.IO).launch {
                        val printResult = sppPrinter.print(data)
                        withContext(Dispatchers.Main) {
                            if (printResult == "SUCCESS") {
                                result.success(null)
                            } else {
                                result.error("PRINT_FAILED", printResult, null)
                            }
                        }
                    }
                }
                "disconnect" -> {
                     CoroutineScope(Dispatchers.IO).launch {
                        sppPrinter.disconnect()
                        withContext(Dispatchers.Main) {
                            result.success(null)
                        }
                    }
                }
                "getBondedDevices" -> {
                    CoroutineScope(Dispatchers.IO).launch {
                        val devices = sppPrinter.getBondedDevices()
                        withContext(Dispatchers.Main) {
                            result.success(devices)
                        }
                    }
                }
                "isConnected" -> {
                    result.success(sppPrinter.isConnected())
                }

                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
