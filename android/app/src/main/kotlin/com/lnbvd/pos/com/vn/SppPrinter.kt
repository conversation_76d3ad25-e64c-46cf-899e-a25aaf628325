package com.lnbvd.pos.com.vn

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothSocket
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import java.io.IOException
import java.util.UUID

class SppPrinter(private val context: Context) {

    private var bluetoothAdapter: BluetoothAdapter? = BluetoothAdapter.getDefaultAdapter()
    private var printerSocket: BluetoothSocket? = null

    private val sppUuid: UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")

    fun connect(macAddress: String): String {
        if (bluetoothAdapter == null) return "Thiết bị không hỗ trợ Bluetooth"
        if (!bluetoothAdapter!!.isEnabled) return "Bluetooth chưa được bật"

        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
            return "Chưa có quyền BLUETOOTH_CONNECT"
        }

        val printerDevice: BluetoothDevice?
        try {
            printerDevice = bluetoothAdapter!!.getRemoteDevice(macAddress)
        } catch (e: IllegalArgumentException) {
            return "Địa chỉ MAC không hợp lệ"
        }

        if (printerDevice == null) return "Không tìm thấy thiết bị với địa chỉ MAC này"

        try {
            printerSocket?.close() // Đóng kết nối cũ nếu có
            printerSocket = printerDevice.createRfcommSocketToServiceRecord(sppUuid)
            bluetoothAdapter!!.cancelDiscovery()
            printerSocket!!.connect()
            return "SUCCESS"
        } catch (e: IOException) {
            // Thử fallback method
            try {
                val fallbackSocket = printerDevice.javaClass.getMethod("createRfcommSocket", Int::class.javaPrimitiveType)
                    .invoke(printerDevice, 1) as BluetoothSocket
                printerSocket = fallbackSocket
                printerSocket!!.connect()
                return "SUCCESS"
            } catch (fallbackException: Exception) {
                return "Kết nối thất bại: ${fallbackException.message}"
            }
        }
    }

    fun print(data: ByteArray): String {
        if (printerSocket == null || !printerSocket!!.isConnected) {
            return "Chưa kết nối với máy in"
        }
        try {
            val outputStream = printerSocket!!.outputStream
            outputStream.write(data)
            outputStream.flush()
            return "SUCCESS"
        } catch (e: IOException) {
            return "Lỗi khi in: ${e.message}"
        }
    }

    fun getBondedDevices(): List<Map<String, String>> {
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
            return emptyList()
        }
        val bondedDevices = bluetoothAdapter?.bondedDevices ?: return emptyList()
        return bondedDevices.map {
            mapOf("name" to (it.name ?: "Unknown"), "address" to it.address)
        }
    }

    fun disconnect() {
        try {
            printerSocket?.close()
        } catch (e: IOException) {
            // Log error or ignore
        }
        printerSocket = null
    }

    fun isConnected(): Boolean {
        return printerSocket != null && printerSocket!!.isConnected
    }
}
